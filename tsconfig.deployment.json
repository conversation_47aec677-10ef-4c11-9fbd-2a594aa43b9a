{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noImplicitOverride": false, "exactOptionalPropertyTypes": false, "noUncheckedIndexedAccess": false, "noPropertyAccessFromIndexSignature": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "skipLibCheck": true}}