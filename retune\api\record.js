// API route for saving recordings
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import formidable from 'formidable';

// Disable body parsing for this route
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'audio', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Parse form with formidable
    const form = new formidable.IncomingForm();
    form.uploadDir = uploadsDir;
    form.keepExtensions = true;

    form.parse(req, async (err, fields, files) => {
      if (err) {
        console.error('Error parsing form:', err);
        return res.status(500).json({ error: 'Error processing upload' });
      }

      if (!files.audio) {
        return res.status(400).json({ error: 'No audio file provided' });
      }

      // Generate a unique filename
      const fileId = uuidv4();
      const originalFilename = files.audio.originalFilename || 'recording.wav';
      const fileExt = path.extname(originalFilename);
      const newFilename = `${fileId}${fileExt}`;
      const finalPath = path.join(uploadsDir, newFilename);

      // Move the file to its final location
      fs.renameSync(files.audio.filepath, finalPath);

      // Parse metadata if provided
      let metadata = {};
      if (fields.metadata) {
        try {
          metadata = JSON.parse(fields.metadata);
        } catch (error) {
          console.warn('Error parsing metadata:', error);
        }
      }

      // Create a record in the database (in a real app)
      // For now, we'll just return the file info
      const recordingInfo = {
        id: fileId,
        filename: newFilename,
        originalFilename,
        path: `/audio/uploads/${newFilename}`,
        timestamp: new Date().toISOString(),
        metadata
      };

      res.status(200).json({
        success: true,
        recording: recordingInfo
      });
    });
  } catch (error) {
    console.error('Error handling recording:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
