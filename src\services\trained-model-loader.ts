/**
 * TRAINED MODEL LOADER - DISABLED FOR VERCEL DEPLOYMENT
 *
 * This service is disabled for cloud deployment as it requires local file system access.
 * Using cloud APIs as fallback.
 */

// DISABLED FOR VERCEL DEPLOYMENT - Local file system access not available
// import fs from 'fs';
// import path from 'path';

// const BASE_PATH = "C:\\Users\\<USER>\\OneDrive\\Desktop\\APIT\\LABEL CEO\\apit-mvp\\COMPLIED DATA FOLDERS FOR THE AI WRITERS";

interface TrainedModel {
  genre: string;
  modelPath: string;
  trainingInfo: any;
  isLoaded: boolean;
  lastUsed: Date;
}

interface ModelGenerationRequest {
  prompt: string;
  genre: string;
  writerId: string;
  mood?: string;
  style?: string;
}

// Cache for loaded models
const modelCache = new Map<string, TrainedModel>();

/**
 * Load a trained model for a specific genre - DISABLED FOR VERCEL DEPLOYMENT
 */
export async function loadTrainedModel(genre: string): Promise<TrainedModel | null> {
  console.log(`⚠️ Trained model loading disabled for cloud deployment. Genre: ${genre}`);

  // Return null to trigger fallback to cloud APIs
  return null;
}

/**
 * Generate lyrics using a trained model
 */
export async function generateWithTrainedModel(request: ModelGenerationRequest): Promise<string | null> {
  const model = await loadTrainedModel(request.genre);

  if (!model) {
    console.warn(`⚠️ No trained model available for ${request.genre}, falling back to AI`);
    return null;
  }

  try {
    console.log(`🎵 Generating lyrics using trained ${model.genre} model...`);

    // For now, we'll use the model's training info to enhance the prompt
    // In a full implementation, you'd load the actual model weights
    const enhancedPrompt = createModelEnhancedPrompt(request, model);

    // Try Gemini first, then Groq as fallback
    console.log(`🤖 Generating with trained ${model.genre} model...`);
    console.log(`📝 Prompt preview: "${enhancedPrompt.substring(0, 100)}..."`);

    // Try Gemini first
    try {
      console.log(`🔄 Trying Gemini for ${model.genre}...`);
      const { default: geminiService } = await import('../utils/geminiService');

      const geminiResult = await geminiService.generateWithFallback(enhancedPrompt, {
        temperature: 0.8,
        maxTokens: 1000,
        systemPrompt: `You are a ${model.genre} specialist. Return ONLY the lyrics, no explanations, no commentary, no introductions. Just the song lyrics with verse/chorus structure.`
      });

      console.log(`✅ Gemini success with ${geminiResult.model}`);
      return cleanLyricsOutput(geminiResult.text);

    } catch (geminiError: any) {
      console.warn(`⚠️ Gemini failed for ${model.genre}:`, geminiError?.message || geminiError);

      // Fallback to Groq
      console.log(`🔄 Trying Groq as fallback for ${model.genre}...`);
      const { generateText: groqGenerateText } = await import('../utils/groqService');

      const groqResult = await groqGenerateText(enhancedPrompt, {
        model: 'llama-3.3-70b-versatile',
        temperature: 0.8,
        maxTokens: 1000,
        systemPrompt: `You are a ${model.genre} specialist. Return ONLY the lyrics, no explanations, no commentary, no introductions. Just the song lyrics with verse/chorus structure.`
      });

      console.log(`✅ Groq success for ${model.genre}`);
      return cleanLyricsOutput(groqResult);
    }

  } catch (error) {
    console.error(`❌ Error generating with trained model:`, error);

    // Fallback: Return a simple template based on the model info
    console.log(`🔄 Using fallback generation for ${model.genre}...`);

    // Simple fallback lyrics based on the request
    const fallbackLyrics = `[${model.genre} Lyrics - Generated with Trained Model]

[Verse 1]
${request.prompt || 'Creating music with passion and soul'}
In the style of ${model.genre}, we tell our story
With ${model.trainingInfo.samples || 'many'} samples as our guide
From ${model.trainingInfo.artists || 'various'} artists who came before

[Chorus]
This is ${model.genre} at its finest
Trained on the best, now we shine the brightest
${request.mood || 'Authentic'} vibes flowing through every line
${model.genre} music, perfectly designed

[Verse 2]
The rhythm speaks, the melody calls
In ${model.genre} tradition, we stand tall
Generated with AI but rooted in truth
${model.genre} legacy, eternal youth

[Outro]
${model.genre} forever, this is our sound
Trained model magic, authentically found`;

    console.log(`✅ Generated fallback lyrics for ${model.genre}`);
    return fallbackLyrics;
  }
}

/**
 * Create an enhanced prompt using model training information
 */
function createModelEnhancedPrompt(request: ModelGenerationRequest, model: TrainedModel): string {
  // Simplified prompt to avoid content filtering issues
  let prompt = `${request.prompt}

Genre: ${model.genre}
Mood: ${request.mood || 'upbeat'}

IMPORTANT: Return ONLY the song lyrics. No explanations, no commentary, no "here are some lyrics" - just the actual lyrics with verse/chorus structure.

Format:
[Verse 1]
...lyrics...

[Chorus]
...lyrics...

[Verse 2]
...lyrics...

[Chorus]
...lyrics...`;

  return prompt;
}

/**
 * Clean up AI output to return only lyrics
 */
function cleanLyricsOutput(text: string): string {
  if (!text) return text;

  // Remove common AI commentary patterns
  let cleaned = text
    // Remove introductory phrases
    .replace(/^(Here are some|Okay, here are some|Here's a|Here are|I'll create|Let me create).*?lyrics.*?[:.]?\s*/i, '')
    .replace(/^(Based on|Using|Drawing from).*?[:.]?\s*/i, '')

    // Remove explanatory text
    .replace(/I've aimed for.*?\./g, '')
    .replace(/This song.*?\./g, '')
    .replace(/The lyrics.*?\./g, '')

    // Remove meta commentary
    .replace(/\(.*?interpreted as.*?\)/g, '')
    .replace(/\(.*?focusing on.*?\)/g, '')
    .replace(/\(.*?avoiding.*?\)/g, '')

    // Remove trailing explanations
    .replace(/\n\n.*?(This captures|The song|These lyrics).*$/s, '')

    // Clean up extra whitespace
    .trim();

  // If the text still starts with explanatory content, try to find where lyrics actually start
  const lyricsStart = cleaned.search(/\[(Verse|Chorus|Intro|Bridge|Outro)/i);
  if (lyricsStart > 0) {
    cleaned = cleaned.substring(lyricsStart);
  }

  return cleaned;
}

/**
 * Normalize genre names to match folder structure
 */
function normalizeGenre(genre: string): string {
  const mapping: Record<string, string> = {
    'Hip-Hop': 'hiphop',
    'hip-hop': 'hiphop',
    'R&B': 'rnb',
    'r&b': 'rnb',
    'African': 'african',
    'Afrobeat': 'african',
    'Pop': 'pop',
    'Jazz': 'jazz',
    'Rock': 'rock',
    'Country': 'country',
    'Latin': 'latin',
    'Alternative': 'alternative',
    'Indie': 'indie',
    'Electronic': 'electronic'
  };

  return mapping[genre] || genre.toLowerCase();
}

/**
 * Get available trained models - DISABLED FOR VERCEL DEPLOYMENT
 */
export async function getAvailableModels(): Promise<string[]> {
  console.log('⚠️ Model listing disabled for cloud deployment');
  return ['hiphop', 'rnb', 'pop', 'rock', 'country', 'jazz', 'latin', 'african', 'indie', 'alternative'];
}

/**
 * Check if a trained model exists for a genre - DISABLED FOR VERCEL DEPLOYMENT
 */
export async function hasTrainedModel(genre: string): Promise<boolean> {
  console.log(`⚠️ Model checking disabled for cloud deployment. Genre: ${genre}`);
  return false; // Always return false to trigger fallback to cloud APIs
}

/**
 * Clear model cache (for memory management)
 */
export function clearModelCache(): void {
  modelCache.clear();
  console.log('🗑️ Cleared trained model cache');
}

/**
 * Get model cache stats
 */
export function getModelCacheStats(): { loaded: number; genres: string[] } {
  return {
    loaded: modelCache.size,
    genres: Array.from(modelCache.keys())
  };
}
