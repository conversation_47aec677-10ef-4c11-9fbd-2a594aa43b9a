'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ExportOptionsProps {
  enhancedAudio: string | null;
  originalAudio: string | null;
  voiceModel: any;
}

export default function ExportOptions({ enhancedAudio, originalAudio, voiceModel }: ExportOptionsProps) {
  // State for export format
  const [exportFormat, setExportFormat] = useState<'wav' | 'mp3' | 'ogg'>('wav');
  
  // State for genre preset
  const [genrePreset, setGenrePreset] = useState<'none' | 'pop' | 'rap' | 'rnb'>('none');
  
  // State for export quality
  const [exportQuality, setExportQuality] = useState<'standard' | 'high' | 'studio'>('high');
  
  // State for export progress
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportComplete, setExportComplete] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  
  // Handle export
  const handleExport = async () => {
    if (!enhancedAudio) return;
    
    setIsExporting(true);
    setExportProgress(0);
    setExportComplete(false);
    setDownloadUrl(null);
    
    // Simulate export process
    const interval = setInterval(() => {
      setExportProgress(prev => {
        const newProgress = prev + Math.random() * 15;
        if (newProgress >= 100) {
          clearInterval(interval);
          setExportComplete(true);
          setDownloadUrl(enhancedAudio);
          return 100;
        }
        return newProgress;
      });
    }, 500);
    
    // In a real implementation, we would call an API to process the export
    /*
    try {
      const response = await fetch('/api/retune/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          audioUrl: enhancedAudio,
          format: exportFormat,
          quality: exportQuality,
          genrePreset
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to export audio');
      }
      
      const data = await response.json();
      setDownloadUrl(data.downloadUrl);
      setExportComplete(true);
    } catch (error) {
      console.error('Error exporting audio:', error);
      alert('Error exporting your audio. Please try again.');
    } finally {
      setIsExporting(false);
    }
    */
  };
  
  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
      <h2 className="text-2xl font-semibold mb-4">Export Enhanced Audio</h2>
      
      {/* Audio Preview */}
      <div className="mb-8 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
        <h3 className="text-lg font-medium mb-3 text-blue-400">Final Audio Preview</h3>
        
        <audio controls className="w-full mb-4">
          <source src={enhancedAudio || undefined} type="audio/wav" />
          Your browser does not support the audio element.
        </audio>
        
        <div className="flex items-center gap-2 text-sm">
          <span className="text-gray-300">Voice Model:</span>
          <span className="font-medium text-blue-400">{voiceModel?.name || 'Voice Model'}</span>
        </div>
      </div>
      
      {/* Export Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Left Column: Format & Quality */}
        <div>
          {/* Format Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-blue-400">Export Format</h3>
            
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => setExportFormat('wav')}
                className={`p-3 rounded-lg border ${
                  exportFormat === 'wav'
                    ? 'bg-blue-600/50 border-blue-500'
                    : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
              >
                <div className="font-medium">WAV</div>
                <div className="text-xs text-gray-400">Lossless</div>
              </button>
              
              <button
                onClick={() => setExportFormat('mp3')}
                className={`p-3 rounded-lg border ${
                  exportFormat === 'mp3'
                    ? 'bg-blue-600/50 border-blue-500'
                    : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
              >
                <div className="font-medium">MP3</div>
                <div className="text-xs text-gray-400">Compressed</div>
              </button>
              
              <button
                onClick={() => setExportFormat('ogg')}
                className={`p-3 rounded-lg border ${
                  exportFormat === 'ogg'
                    ? 'bg-blue-600/50 border-blue-500'
                    : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
              >
                <div className="font-medium">OGG</div>
                <div className="text-xs text-gray-400">Open format</div>
              </button>
            </div>
          </div>
          
          {/* Quality Selection */}
          <div>
            <h3 className="text-lg font-medium mb-3 text-blue-400">Export Quality</h3>
            
            <div className="space-y-2">
              <label className={`flex items-center p-3 rounded-lg border ${
                exportQuality === 'standard'
                  ? 'bg-blue-600/50 border-blue-500'
                  : 'bg-gray-800/50 border-gray-700'
              } transition-colors cursor-pointer`}>
                <input
                  type="radio"
                  name="quality"
                  checked={exportQuality === 'standard'}
                  onChange={() => setExportQuality('standard')}
                  className="hidden"
                />
                <div className={`w-4 h-4 rounded-full border ${
                  exportQuality === 'standard'
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-500'
                } mr-3 flex items-center justify-center`}>
                  {exportQuality === 'standard' && (
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  )}
                </div>
                <div>
                  <div className="font-medium">Standard Quality</div>
                  <div className="text-xs text-gray-400">16-bit / 44.1kHz</div>
                </div>
              </label>
              
              <label className={`flex items-center p-3 rounded-lg border ${
                exportQuality === 'high'
                  ? 'bg-blue-600/50 border-blue-500'
                  : 'bg-gray-800/50 border-gray-700'
              } transition-colors cursor-pointer`}>
                <input
                  type="radio"
                  name="quality"
                  checked={exportQuality === 'high'}
                  onChange={() => setExportQuality('high')}
                  className="hidden"
                />
                <div className={`w-4 h-4 rounded-full border ${
                  exportQuality === 'high'
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-500'
                } mr-3 flex items-center justify-center`}>
                  {exportQuality === 'high' && (
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  )}
                </div>
                <div>
                  <div className="font-medium">High Quality</div>
                  <div className="text-xs text-gray-400">24-bit / 48kHz</div>
                </div>
              </label>
              
              <label className={`flex items-center p-3 rounded-lg border ${
                exportQuality === 'studio'
                  ? 'bg-blue-600/50 border-blue-500'
                  : 'bg-gray-800/50 border-gray-700'
              } transition-colors cursor-pointer`}>
                <input
                  type="radio"
                  name="quality"
                  checked={exportQuality === 'studio'}
                  onChange={() => setExportQuality('studio')}
                  className="hidden"
                />
                <div className={`w-4 h-4 rounded-full border ${
                  exportQuality === 'studio'
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-500'
                } mr-3 flex items-center justify-center`}>
                  {exportQuality === 'studio' && (
                    <div className="w-2 h-2 rounded-full bg-white"></div>
                  )}
                </div>
                <div>
                  <div className="font-medium">Studio Quality</div>
                  <div className="text-xs text-gray-400">32-bit / 96kHz</div>
                </div>
              </label>
            </div>
          </div>
        </div>
        
        {/* Right Column: Genre Presets */}
        <div>
          <h3 className="text-lg font-medium mb-3 text-blue-400">Genre-Specific Presets</h3>
          
          <div className="space-y-2">
            <label className={`flex items-center p-3 rounded-lg border ${
              genrePreset === 'none'
                ? 'bg-blue-600/50 border-blue-500'
                : 'bg-gray-800/50 border-gray-700'
            } transition-colors cursor-pointer`}>
              <input
                type="radio"
                name="genre"
                checked={genrePreset === 'none'}
                onChange={() => setGenrePreset('none')}
                className="hidden"
              />
              <div className={`w-4 h-4 rounded-full border ${
                genrePreset === 'none'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-500'
              } mr-3 flex items-center justify-center`}>
                {genrePreset === 'none' && (
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                )}
              </div>
              <div>
                <div className="font-medium">No Genre Preset</div>
                <div className="text-xs text-gray-400">Use the enhanced audio as is</div>
              </div>
            </label>
            
            <label className={`flex items-center p-3 rounded-lg border ${
              genrePreset === 'pop'
                ? 'bg-blue-600/50 border-blue-500'
                : 'bg-gray-800/50 border-gray-700'
            } transition-colors cursor-pointer`}>
              <input
                type="radio"
                name="genre"
                checked={genrePreset === 'pop'}
                onChange={() => setGenrePreset('pop')}
                className="hidden"
              />
              <div className={`w-4 h-4 rounded-full border ${
                genrePreset === 'pop'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-500'
              } mr-3 flex items-center justify-center`}>
                {genrePreset === 'pop' && (
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                )}
              </div>
              <div>
                <div className="font-medium">Pop</div>
                <div className="text-xs text-gray-400">Bright, polished vocal with subtle effects</div>
              </div>
            </label>
            
            <label className={`flex items-center p-3 rounded-lg border ${
              genrePreset === 'rap'
                ? 'bg-blue-600/50 border-blue-500'
                : 'bg-gray-800/50 border-gray-700'
            } transition-colors cursor-pointer`}>
              <input
                type="radio"
                name="genre"
                checked={genrePreset === 'rap'}
                onChange={() => setGenrePreset('rap')}
                className="hidden"
              />
              <div className={`w-4 h-4 rounded-full border ${
                genrePreset === 'rap'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-500'
              } mr-3 flex items-center justify-center`}>
                {genrePreset === 'rap' && (
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                )}
              </div>
              <div>
                <div className="font-medium">Rap / Hip-Hop</div>
                <div className="text-xs text-gray-400">Clear, punchy vocals with presence</div>
              </div>
            </label>
            
            <label className={`flex items-center p-3 rounded-lg border ${
              genrePreset === 'rnb'
                ? 'bg-blue-600/50 border-blue-500'
                : 'bg-gray-800/50 border-gray-700'
            } transition-colors cursor-pointer`}>
              <input
                type="radio"
                name="genre"
                checked={genrePreset === 'rnb'}
                onChange={() => setGenrePreset('rnb')}
                className="hidden"
              />
              <div className={`w-4 h-4 rounded-full border ${
                genrePreset === 'rnb'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-500'
              } mr-3 flex items-center justify-center`}>
                {genrePreset === 'rnb' && (
                  <div className="w-2 h-2 rounded-full bg-white"></div>
                )}
              </div>
              <div>
                <div className="font-medium">R&B</div>
                <div className="text-xs text-gray-400">Warm, smooth vocals with subtle reverb</div>
              </div>
            </label>
          </div>
          
          {/* Technical Details */}
          <div className="mt-6 bg-gray-900/30 p-3 rounded-lg border border-gray-800">
            <h4 className="text-sm font-medium mb-2 text-gray-300">Technical Details</h4>
            <div className="space-y-1 text-xs text-gray-400">
              <p>• Target loudness: -14 LUFS integrated</p>
              <p>• Peak ceiling: -1 dBTP</p>
              <p>• Stereo enhancement: {genrePreset === 'pop' || genrePreset === 'rnb' ? 'Enabled' : 'Disabled'}</p>
              <p>• De-essing: {genrePreset === 'none' ? 'Light' : 'Adaptive'}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Export Button */}
      <div className="flex justify-center">
        {!exportComplete ? (
          <button
            onClick={handleExport}
            disabled={isExporting || !enhancedAudio}
            className={`px-8 py-3 rounded-lg font-medium transition-colors ${
              isExporting
                ? 'bg-gray-600 text-gray-300'
                : 'bg-blue-600 hover:bg-blue-500 text-white'
            }`}
          >
            {isExporting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Exporting... {Math.round(exportProgress)}%
              </span>
            ) : (
              'Export Audio'
            )}
          </button>
        ) : (
          <a
            href={downloadUrl || '#'}
            download={`enhanced_vocal_${new Date().getTime()}.${exportFormat}`}
            className="px-8 py-3 bg-green-600 hover:bg-green-500 text-white rounded-lg font-medium transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Download Enhanced Audio
          </a>
        )}
      </div>
      
      {/* Export Progress */}
      {isExporting && (
        <div className="mt-4">
          <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-600"
              style={{ width: `${exportProgress}%` }}
            ></div>
          </div>
          <p className="text-center text-sm text-gray-400 mt-2">
            Processing audio with {genrePreset !== 'none' ? `${genrePreset} preset` : 'no genre preset'}...
          </p>
        </div>
      )}
      
      {/* Share Options */}
      {exportComplete && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-3 text-blue-400">Share Options</h3>
          
          <div className="flex flex-wrap justify-center gap-3">
            <button className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
              Copy Link
            </button>
            
            <button className="px-4 py-2 bg-[#1DA1F2]/80 hover:bg-[#1DA1F2] text-white rounded-lg font-medium transition-colors flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
              </svg>
              Twitter
            </button>
            
            <button className="px-4 py-2 bg-[#4267B2]/80 hover:bg-[#4267B2] text-white rounded-lg font-medium transition-colors flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
              </svg>
              Facebook
            </button>
            
            <button className="px-4 py-2 bg-[#FF0000]/80 hover:bg-[#FF0000] text-white rounded-lg font-medium transition-colors flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
              </svg>
              YouTube
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
