'use client';

import React, { useState } from 'react';

const VoiceCloneGuidelines: React.FC = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>('intro');

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  return (
    <div className="retune-glass-container p-5 mb-6">
      <h3 className="text-lg font-medium retune-text-glow-cyan mb-4">Voice Cloning Guidelines</h3>
      
      <div className="space-y-4">
        {/* Introduction */}
        <div className="border-b border-white/10 pb-3">
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('intro')}
          >
            <h4 className="text-white font-medium">Introduction & Requirements</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#00FFEE] transition-transform duration-300 ${expandedSection === 'intro' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'intro' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>For optimal voice cloning results, aim for <span className="text-[#00FFEE]">15-30 minutes</span> of varied vocal content. Record in a quiet room with minimal background noise.</p>
              <div className="bg-black/20 p-3 rounded-lg mt-2">
                <h5 className="text-[#00FFEE] mb-1">Recording Environment:</h5>
                <ul className="list-disc list-inside space-y-1 text-white/70">
                  <li>Quiet room with minimal echo</li>
                  <li>No background noise (AC, fans, etc.)</li>
                  <li>Microphone 6-8 inches from mouth</li>
                  <li>Use pop filter if available</li>
                  <li>Maintain consistent volume</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        
        {/* Phonetic Coverage */}
        <div className="border-b border-white/10 pb-3">
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('phonetic')}
          >
            <h4 className="text-white font-medium">Phonetic Coverage (3-5 min)</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#C600FF] transition-transform duration-300 ${expandedSection === 'phonetic' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'phonetic' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>Record these phonetic elements to ensure your voice model captures all speech sounds:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#C600FF] mb-1">Basic Vowels:</h5>
                  <p className="text-white/70">"ah" (father), "eh" (bed), "ee" (see), "oh" (go), "oo" (blue)</p>
                </div>
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#C600FF] mb-1">Consonant Clusters:</h5>
                  <p className="text-white/70">"str" (string), "bl" (blue), "dr" (drive), "tr" (train), "fl" (flow)</p>
                </div>
              </div>
              <div className="bg-black/20 p-3 rounded-lg mt-2">
                <h5 className="text-[#C600FF] mb-1">Sample Sentences:</h5>
                <ul className="list-disc list-inside space-y-1 text-white/70">
                  <li>The quick brown fox jumps over the lazy dog</li>
                  <li>She sells seashells by the seashore</li>
                  <li>Peter Piper picked a peck of pickled peppers</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        
        {/* Musical Elements */}
        <div className="border-b border-white/10 pb-3">
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('musical')}
          >
            <h4 className="text-white font-medium">Musical Elements (5-7 min)</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#00FFEE] transition-transform duration-300 ${expandedSection === 'musical' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'musical' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>Include these musical elements to enhance singing voice quality:</p>
              <div className="bg-black/20 p-3 rounded-lg mt-2">
                <ul className="list-disc list-inside space-y-1 text-white/70">
                  <li>Scales: Major scale on "la" (do-re-mi-fa-sol-la-ti-do)</li>
                  <li>Sustained Notes: Hold different pitches for 3-5 seconds</li>
                  <li>Dynamic Range: Sing from soft to loud and back</li>
                  <li>Hook/Chorus: 15-20 seconds of a familiar song chorus</li>
                  <li>Vibrato & Techniques: Demonstrate your natural vibrato and vocal techniques</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        
        {/* Speech Rhythm & Flow */}
        <div className="border-b border-white/10 pb-3">
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('rhythm')}
          >
            <h4 className="text-white font-medium">Speech Rhythm & Flow (5-7 min)</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#C600FF] transition-transform duration-300 ${expandedSection === 'rhythm' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'rhythm' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>Capture your unique speech patterns and flow:</p>
              <div className="bg-black/20 p-3 rounded-lg mt-2">
                <ul className="list-disc list-inside space-y-1 text-white/70">
                  <li>Rap Verse: 16 bars at different tempos (slow, medium, fast)</li>
                  <li>Spoken Rhythm: Recite poetry with strong rhythmic elements</li>
                  <li>Varied Pacing: Speak the same sentence at different speeds</li>
                  <li>Pauses & Emphasis: Practice sentences with strategic pauses</li>
                  <li>Flow Patterns: Demonstrate your unique cadence and flow style</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        
        {/* Emotional Expression */}
        <div className="border-b border-white/10 pb-3">
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('emotional')}
          >
            <h4 className="text-white font-medium">Emotional Expression (5-7 min)</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#00FFEE] transition-transform duration-300 ${expandedSection === 'emotional' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'emotional' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>Record these emotional expressions to create a versatile voice model:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#00FFEE] mb-1">Joy/Happiness:</h5>
                  <p className="text-white/70">"I'm so excited to share this amazing news with you!"</p>
                </div>
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#00FFEE] mb-1">Sadness/Melancholy:</h5>
                  <p className="text-white/70">"I really miss the times we used to spend together."</p>
                </div>
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#00FFEE] mb-1">Anger/Frustration:</h5>
                  <p className="text-white/70">"I've told you repeatedly not to do that!"</p>
                </div>
                <div className="bg-black/20 p-3 rounded-lg">
                  <h5 className="text-[#00FFEE] mb-1">Calm/Relaxed:</h5>
                  <p className="text-white/70">"Everything is going according to plan, no need to worry."</p>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Natural Speech */}
        <div>
          <button 
            className="w-full flex justify-between items-center text-left"
            onClick={() => toggleSection('natural')}
          >
            <h4 className="text-white font-medium">Natural Speech (5-10 min)</h4>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className={`h-5 w-5 text-[#C600FF] transition-transform duration-300 ${expandedSection === 'natural' ? 'rotate-180' : ''}`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {expandedSection === 'natural' && (
            <div className="mt-3 text-sm text-white/80 space-y-2 animate-fadeIn">
              <p>Include natural speech samples to capture your everyday voice:</p>
              <div className="bg-black/20 p-3 rounded-lg mt-2">
                <ul className="list-disc list-inside space-y-1 text-white/70">
                  <li>Prose Passage: 2-minute neutral reading from a book or article</li>
                  <li>Conversational: Speak naturally as if talking to a friend</li>
                  <li>Storytelling: Narrate a short story with character voices</li>
                  <li>Casual Phrases: Common expressions you use in everyday speech</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceCloneGuidelines;
