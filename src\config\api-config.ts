/**
 * API Configuration
 *
 * This file contains configuration for all external APIs used in the application.
 * API keys should be stored in environment variables, not in this file.
 */

export const ApiConfig = {
  // Text Generation APIs
  textGeneration: {
    groq: {
      baseUrl: 'https://api.groq.com/openai/v1',
      apiKeyEnvVar: 'GROQ_API_KEY',
      model: 'llama-3.1-70b-versatile',
    },
    gemini: {
      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
      apiKeyEnvVar: 'GEMINI_API_KEY',
      model: 'gemini-1.5-pro',
    },

  },

  // Music Generation APIs
  musicGeneration: {
    sonauto: {
      baseUrl: 'https://api.sonauto.ai/v1',
      apiKeyEnvVar: 'SONAUTO_API_KEY',
    },
    mubert: {
      baseUrl: 'https://api-b2b.mubert.com/v2',
      apiKeyEnvVar: 'MUBERT_API_KEY',
    },
    soundraw: {
      baseUrl: 'https://api.soundraw.io',
      apiKeyEnvVar: 'SOUNDRAW_API_KEY',
    },
  },

  // Voice Cloning/Enhancement APIs
  voiceProcessing: {
    elevenlabs: {
      baseUrl: 'https://api.elevenlabs.io/v1',
      apiKeyEnvVar: 'ELEVENLABS_API_KEY',
    },
    playht: {
      baseUrl: 'https://api.play.ht/api/v2',
      apiKeyEnvVar: 'PLAYHT_API_KEY',
      userIdEnvVar: 'PLAYHT_USER_ID',
    },
    resembleAi: {
      baseUrl: 'https://app.resemble.ai/api/v2',
      apiKeyEnvVar: 'RESEMBLE_API_KEY',
    },
  },

  // Beat Detection/Sync APIs
  beatAnalysis: {
    spotify: {
      baseUrl: 'https://api.spotify.com/v1',
      clientIdEnvVar: 'SPOTIFY_CLIENT_ID',
      clientSecretEnvVar: 'SPOTIFY_CLIENT_SECRET',
    },
    acrCloud: {
      baseUrl: 'https://identify-eu-west-1.acrcloud.com',
      accessKeyEnvVar: 'ACRCLOUD_ACCESS_KEY',
      accessSecretEnvVar: 'ACRCLOUD_ACCESS_SECRET',
      hostEnvVar: 'ACRCLOUD_HOST',
    },
  },

  // Stem Separation APIs
  stemSeparation: {
    spleeter: {
      baseUrl: '/api/best-shot/stem-separation',
      isLocal: true,
    },
    moisesAi: {
      baseUrl: 'https://developer-api.moises.ai/api',
      apiKeyEnvVar: 'MOISES_API_KEY',
    },
  },

  // Image Generation APIs
  imageGeneration: {
    blackForest: {
      baseUrl: 'https://api.bfl.ai',
      apiKeyEnvVar: 'BLACK_FOREST_API_KEY',
      models: {
        fluxKontextPro: 'flux-kontext-pro',
        fluxKontextMax: 'flux-kontext-max',
        fluxPro11Ultra: 'flux-pro-1.1-ultra',
        fluxPro11: 'flux-pro-1.1',
        fluxPro: 'flux-pro',
        fluxDev: 'flux-dev'
      },
      aspectRatios: ['1:1', '16:9', '9:16', '4:3', '3:4', '21:9', '9:21']
    },
    stability: {
      baseUrl: 'https://api.stability.ai',
      apiKeyEnvVar: 'STABILITY_API_KEY',
      models: {
        sd3: 'sd3-large-turbo',
        sdxl: 'stable-diffusion-xl-1024-v1-0'
      }
    },
  },

  // Video Generation APIs
  videoGeneration: {
    runway: {
      baseUrl: 'https://api.runwayml.com/v1',
      apiKeyEnvVar: 'RUNWAY_API_KEY',
    },
    replicate: {
      baseUrl: 'https://api.replicate.com/v1',
      apiKeyEnvVar: 'REPLICATE_API_KEY',
    },
  },
};

/**
 * Get an API key from environment variables
 * @param envVarName The name of the environment variable
 * @returns The API key or null if not found
 */
export function getApiKey(envVarName: string): string | null {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[envVarName] || null;
  }
  return null;
}

/**
 * Check if an API is configured (has an API key)
 * @param apiKeyEnvVar The name of the environment variable for the API key
 * @returns True if the API is configured, false otherwise
 */
export function isApiConfigured(apiKeyEnvVar: string): boolean {
  return getApiKey(apiKeyEnvVar) !== null;
}

export default ApiConfig;
