import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Serve individual files from Suno generations
export async function GET(
  request: Request,
  context: { params: Promise<{ taskId: string; file: string }> }
) {
  try {
    const params = await context.params;
    const { taskId, file } = params;

    // Validate taskId and file parameters
    if (!taskId || !file) {
      return NextResponse.json({ error: 'Missing taskId or file parameter' }, { status: 400 });
    }

    // Sanitize file path to prevent directory traversal
    const sanitizedFile = path.basename(file);

    // Search for the file in the organized structure
    const sunoGenerationsRoot = path.join(process.cwd(), 'public', 'suno-generations');
    let filePath = null;

    if (fs.existsSync(sunoGenerationsRoot)) {
      const findTaskFile = (dir: string): string | null => {
        const items = fs.readdirSync(dir);
        for (const item of items) {
          const itemPath = path.join(dir, item);
          if (fs.statSync(itemPath).isDirectory()) {
            if (item === taskId) {
              const targetFile = path.join(itemPath, sanitizedFile);
              if (fs.existsSync(targetFile)) {
                return targetFile;
              }
            }
            const found = findTaskFile(itemPath);
            if (found) return found;
          }
        }
        return null;
      };

      filePath = findTaskFile(sunoGenerationsRoot);
    }

    // Check if file exists
    if (!filePath || !fs.existsSync(filePath)) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Read file
    const fileBuffer = fs.readFileSync(filePath);

    // Determine content type based on file extension
    const ext = path.extname(sanitizedFile).toLowerCase();
    let contentType = 'application/octet-stream';

    switch (ext) {
      case '.mp3':
        contentType = 'audio/mpeg';
        break;
      case '.wav':
        contentType = 'audio/wav';
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.txt':
        contentType = 'text/plain';
        break;
      case '.json':
        contentType = 'application/json';
        break;
    }

    // Return file with appropriate headers
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Disposition': `inline; filename="${sanitizedFile}"`
      }
    });

  } catch (error) {
    console.error('Error serving generation file:', error);
    return NextResponse.json({
      error: 'Failed to serve file'
    }, { status: 500 });
  }
}
