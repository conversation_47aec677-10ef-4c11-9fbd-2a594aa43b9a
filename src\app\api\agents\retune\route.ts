import { NextRequest, NextResponse } from 'next/server';
import { retunePipelineCoordinator, RetunePipelineTask } from '@/lib/agents/retune/retune-pipeline';
import { vinnOrchestrator } from '@/lib/agents/vinn-orchestrator';

/**
 * RETUNE Division API Route
 * Handles voice enhancement requests coordinated by VINN
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data, userId } = body;

    console.log(`RETUNE API: ${action} request from user ${userId}`);

    switch (action) {
      case 'enhance_voice':
        return await handleVoiceEnhancement(data, userId);
      
      case 'quick_enhance':
        return await handleQuickEnhancement(data, userId);
      
      case 'get_pipeline_status':
        return await handleGetPipelineStatus(data.taskId);
      
      case 'get_pipeline_stats':
        return await handleGetPipelineStats();
      
      default:
        return NextResponse.json(
          { error: 'Unknown action', availableActions: ['enhance_voice', 'quick_enhance', 'get_pipeline_status', 'get_pipeline_stats'] },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('RETUNE API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleVoiceEnhancement(data: any, userId: string) {
  try {
    // VINN coordination - detect intent and plan workflow
    const intent = vinnOrchestrator.detectIntent('enhance my voice for professional quality');
    const workflow = vinnOrchestrator.coordinateWorkflow(intent.intent, 'voice enhancement request');
    
    console.log('VINN Coordination:', { intent, workflow });

    // Send coordination message to RETUNE division
    const messageId = vinnOrchestrator.sendMessage('RETUNE', 'coordination', {
      action: 'voice_enhancement',
      userRequest: data,
      workflow: workflow.coordinationPlan,
    }, 'high');

    // Create RETUNE pipeline task
    const task: RetunePipelineTask = {
      id: `retune_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      audioInput: data.audioInput || data.audioUrl,
      targetQuality: data.targetQuality || 'high',
      enhancementOptions: {
        layering: data.enhancementOptions?.layering ?? true,
        eqEnhancement: data.enhancementOptions?.eqEnhancement ?? true,
        autotuneLevel: data.enhancementOptions?.autotuneLevel || 'light',
        compressionLevel: data.enhancementOptions?.compressionLevel || 'medium',
        spatialProcessing: data.enhancementOptions?.spatialProcessing ?? true,
      },
      targetGenre: data.targetGenre,
      userId,
    };

    // Execute the pipeline
    const result = await retunePipelineCoordinator.executePipeline(task);

    // Send status update to VINN
    vinnOrchestrator.sendMessage('VINN', 'status', {
      taskId: task.id,
      status: result.success ? 'completed' : 'failed',
      result: result.success ? 'Voice enhancement completed successfully' : result.error,
    });

    return NextResponse.json({
      success: true,
      taskId: task.id,
      messageId,
      vinnCoordination: {
        intent: intent.intent,
        confidence: intent.confidence,
        workflow: workflow.coordinationPlan,
        estimatedTime: workflow.estimatedTime,
      },
      pipelineResult: result,
    });

  } catch (error) {
    console.error('Voice enhancement error:', error);
    return NextResponse.json(
      { error: 'Voice enhancement failed', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleQuickEnhancement(data: any, userId: string) {
  try {
    // VINN quick coordination
    const intent = vinnOrchestrator.detectIntent('quick voice enhancement');
    
    console.log('VINN Quick Enhancement:', intent);

    // Send quick coordination message
    const messageId = vinnOrchestrator.sendMessage('RETUNE', 'coordination', {
      action: 'quick_enhancement',
      userRequest: data,
    }, 'medium');

    // Execute quick enhancement
    const result = await retunePipelineCoordinator.quickEnhance(
      data.audioInput || data.audioUrl,
      userId
    );

    return NextResponse.json({
      success: true,
      taskId: result.taskId,
      messageId,
      vinnCoordination: {
        intent: intent.intent,
        confidence: intent.confidence,
        suggestedAction: intent.suggestedAction,
      },
      pipelineResult: result,
    });

  } catch (error) {
    console.error('Quick enhancement error:', error);
    return NextResponse.json(
      { error: 'Quick enhancement failed', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleGetPipelineStatus(taskId: string) {
  try {
    const status = retunePipelineCoordinator.getPipelineStatus(taskId);
    
    if (!status) {
      return NextResponse.json(
        { error: 'Pipeline not found', taskId },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      taskId,
      status,
    });

  } catch (error) {
    console.error('Get pipeline status error:', error);
    return NextResponse.json(
      { error: 'Failed to get pipeline status', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleGetPipelineStats() {
  try {
    const stats = retunePipelineCoordinator.getPipelineStats();
    const vinnStatus = vinnOrchestrator.getSystemStatus();

    return NextResponse.json({
      success: true,
      retunePipelineStats: stats,
      vinnSystemStatus: vinnStatus,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Get pipeline stats error:', error);
    return NextResponse.json(
      { error: 'Failed to get pipeline stats', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'status':
        const vinnStatus = vinnOrchestrator.getSystemStatus();
        const pipelineStats = retunePipelineCoordinator.getPipelineStats();
        
        return NextResponse.json({
          success: true,
          retuneDivision: {
            status: 'operational',
            vinnCoordination: vinnStatus,
            pipelineStats,
            activePipelines: retunePipelineCoordinator.getActivePipelines().length,
          },
        });

      case 'health':
        const healthCheck = vinnOrchestrator.monitorQuality();
        
        return NextResponse.json({
          success: true,
          health: {
            overall: healthCheck.overallHealth,
            retuneDivision: healthCheck.divisionReports.find(r => r.division === 'RETUNE'),
            timestamp: new Date().toISOString(),
          },
        });

      default:
        return NextResponse.json({
          success: true,
          retuneDivision: {
            name: 'RETUNE',
            description: 'Voice Cloning & Superhuman Enhancement Pipeline',
            capabilities: [
              'Voice Analysis',
              'Voice Cloning',
              'Multi-Layer Enhancement',
              'Billboard-Quality Mastering',
            ],
            agents: [
              'VOICE_ANALYZER',
              'CLONE_ENGINEER',
              'ENHANCEMENT_ARCHITECT',
              'BILLBOARD_PRODUCER',
            ],
            pipeline: '5-Stage Enhancement: Analysis → Cloning → Layering → Enhancement → Mastering',
            coordinatedBy: 'VINN',
          },
        });
    }
  } catch (error) {
    console.error('RETUNE GET API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
