import { exec } from 'child_process';
import fs from 'fs';
import { NextResponse } from 'next/server';
import path from 'path';
import util from 'util';

const execPromise = util.promisify(exec);

// ElevenLabs API integration for voice cloning
export async function POST(request: Request) {
  try {
    // Get the form data from the request
    const formData = await request.formData();
    const audioFiles = formData.getAll('audio') as File[];
    const texts = formData.getAll('text') as string[];
    const modelName = formData.get('name') as string || 'My Voice';
    const isSinging = formData.get('is_singing') === 'true';
    const modelType = formData.get('model_type') as string || 'flowclone';

    if (!audioFiles || audioFiles.length === 0) {
      return NextResponse.json({ error: 'No audio samples provided' }, { status: 400 });
    }

    console.log(`Received ${audioFiles.length} samples for voice cloning`);
    console.log(`Model type: ${modelType}, Is singing: ${isSinging}`);

    // Get the ElevenLabs API key from environment variables
    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey || apiKey === 'your_elevenlabs_api_key_here') {
      console.error('ElevenLabs API key not configured');
      return NextResponse.json({ error: 'Voice cloning service not configured' }, { status: 500 });
    }

    // Create temp directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }

    // Create a unique samples directory for this request
    const requestId = Date.now().toString();
    const samplesDir = path.join(tempDir, `samples_${requestId}`);
    fs.mkdirSync(samplesDir);

    // Save all sample files to the samples directory
    const samplePaths = [];
    for (let i = 0; i < audioFiles.length; i++) {
      const audioFile = audioFiles[i];
      const samplePath = path.join(samplesDir, `sample_${i + 1}.wav`);

      // Convert the file to a Buffer and write it to disk
      const arrayBuffer = await audioFile.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      fs.writeFileSync(samplePath, buffer);

      samplePaths.push(samplePath);
      console.log(`Saved sample ${i + 1} to ${samplePath}`);
    }

    // Prepare data for ElevenLabs API
    const samples = [];
    for (let i = 0; i < samplePaths.length; i++) {
      const audioPath = samplePaths[i];
      const text = texts[i] || '';

      // Read the audio file as base64
      const audioBuffer = fs.readFileSync(audioPath);
      const audioBase64 = audioBuffer.toString('base64');

      samples.push({
        audio: audioBase64,
        text: text
      });
    }

    // Call ElevenLabs API to create a voice
    const response = await fetch('https://api.elevenlabs.io/v1/voices/add', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey
      },
      body: JSON.stringify({
        name: modelName,
        samples: samples,
        description: `Voice model created with ${isSinging ? 'VocalSynth™' : 'FlowClone™'} technology.`
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('ElevenLabs API error:', errorData);
      return NextResponse.json({
        error: errorData.detail?.message || 'Failed to create voice model with ElevenLabs'
      }, { status: response.status });
    }

    const elevenlabsData = await response.json();
    console.log('ElevenLabs voice created:', elevenlabsData.voice_id);

    // Calculate quality score based on the number of samples and their quality
    // This is a simplified version - in a real implementation, you would analyze the audio quality
    const coverageScore = Math.min(100, (audioFiles.length / 15) * 100);
    const prosodyScore = Math.min(100, 50 + audioFiles.length * 3);
    const spectralScore = Math.min(100, 60 + audioFiles.length * 2.5);
    const recordingQualityScore = Math.min(100, 70 + audioFiles.length * 2);

    // Calculate weighted Clone Quality Score (0-100)
    const qualityScore = Math.round(
      (coverageScore * 0.30) +  // Phoneme Coverage: 30%
      (prosodyScore * 0.25) +   // Pitch Stability: 25%
      (spectralScore * 0.25) +  // Timbre Consistency: 25%
      (recordingQualityScore * 0.20)  // Noise & Clarity: 20%
    );

    // Create a model object to store locally
    const model = {
      id: elevenlabsData.voice_id,
      name: modelName,
      type: modelType,
      qualityScore: qualityScore,
      isSinging: isSinging,
      elevenlabsId: elevenlabsData.voice_id,
      createdAt: new Date().toISOString(),
      samples: audioFiles.length
    };

    // Save the model data to a JSON file for future reference
    const modelPath = path.join(tempDir, `voice_model_${requestId}.json`);
    fs.writeFileSync(modelPath, JSON.stringify(model, null, 2));

    // Clean up temporary files
    try {
      // Remove sample files
      for (const samplePath of samplePaths) {
        if (fs.existsSync(samplePath)) {
          fs.unlinkSync(samplePath);
        }
      }

      // Remove samples directory
      if (fs.existsSync(samplesDir)) {
        fs.rmdirSync(samplesDir);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary files:', cleanupError);
      // Continue even if cleanup fails
    }

    // Return the voice model data
    return NextResponse.json({
      success: true,
      model: {
        id: model.id,
        name: model.name,
        type: model.type,
        qualityScore: model.qualityScore,
        createdAt: model.createdAt
      }
    });

  } catch (error) {
    console.error('Error cloning voice:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to clone voice' },
      { status: 500 }
    );
  }
}
