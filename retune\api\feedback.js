// API route for collecting user feedback
import fs from 'fs';
import path from 'path';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { recordingId, feedback } = req.body;
    
    if (!recordingId || !feedback) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Create feedback directory if it doesn't exist
    const feedbackDir = path.join(process.cwd(), 'data', 'feedback');
    if (!fs.existsSync(feedbackDir)) {
      fs.mkdirSync(feedbackDir, { recursive: true });
    }
    
    // In a real app, you would store this in a database
    // For now, we'll write it to a JSON file
    const feedbackData = {
      recordingId,
      feedback,
      timestamp: new Date().toISOString()
    };
    
    const feedbackPath = path.join(feedbackDir, `${recordingId}.json`);
    fs.writeFileSync(feedbackPath, JSON.stringify(feedbackData, null, 2));
    
    res.status(200).json({
      success: true,
      message: 'Feedback received. Thank you for helping us improve!'
    });
  } catch (error) {
    console.error('Error handling feedback:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
