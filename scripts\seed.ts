#!/usr/bin/env tsx
/**
 * Supabase Database Seeder
 *
 * This script seeds the Supabase database with:
 * - 3 dummy users
 * - 2 lyrics per user
 * - 1 generated image per lyric
 *
 * Run with: npx tsx ./scripts/seed.ts
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables from .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Generate unique timestamp for this run to avoid email duplicates
const timestamp = Date.now();

// Dummy user data with unique emails - Updated to use our confirmed AI writers
const dummyUsers = [
  {
    id: uuidv4(),
    name: 'Ava Clarke',
    email: `ava.clarke.${timestamp}@example.com`,
    password_hash: 'dummy_password_hash_for_testing_only'
  },
  {
    id: uuidv4(),
    name: 'Elias Fontaine',
    email: `elias.fontaine.${timestamp}@example.com`,
    password_hash: 'dummy_password_hash_for_testing_only'
  },
  {
    id: uuidv4(),
    name: 'Luna Rivers',
    email: `luna.rivers.${timestamp}@example.com`,
    password_hash: 'dummy_password_hash_for_testing_only'
  }
];

// Genres and moods for generating lyrics - Updated to match our confirmed AI writers
const genres = [
  'Hip-Hop', 'R&B', 'Pop', 'Country', 'Jazz', 'Afrobeat', 'Latin',
  'Electronic', 'Rock', 'Indie', 'Alternative', 'Blues', 'Cross-Genre'
];

const moods = [
  'Happy', 'Sad', 'Energetic', 'Calm', 'Angry', 'Peaceful', 'Nostalgic',
  'Romantic', 'Melancholic', 'Hopeful', 'Anxious', 'Confident', 'Reflective',
  'Dreamy', 'Mysterious', 'Playful', 'Intense', 'Relaxed', 'Longing',
  'Inspirational', 'Contemplative', 'Euphoric', 'Determined', 'Vulnerable'
];

// Title generators
const titlePrefixes = [
  'Lost in', 'Finding', 'Chasing', 'Dreaming of', 'Beyond the', 'Under the',
  'Midnight', 'Electric', 'Neon', 'Fading', 'Rising', 'Eternal', 'Broken',
  'Dancing', 'Whispers of', 'Echoes of', 'Memories of', 'Shadows of', 'Heart of',
  'Spirit of', 'Soul of', 'Edge of', 'Rhythm of', 'Pulse of', 'Voice of'
];

const titleSuffixes = [
  'Dreams', 'Love', 'Light', 'Darkness', 'Shadows', 'Stars', 'Sky', 'Ocean',
  'River', 'Mountain', 'City', 'Street', 'Rain', 'Storm', 'Sun', 'Moon',
  'Heart', 'Soul', 'Mind', 'Time', 'Space', 'Fire', 'Ice', 'Wind', 'Earth',
  'Memories', 'Echoes', 'Whispers', 'Silence', 'Noise', 'Beat', 'Rhythm'
];

const titleAdjectives = [
  'Beautiful', 'Broken', 'Endless', 'Eternal', 'Fading', 'Golden', 'Hidden',
  'Infinite', 'Lost', 'Luminous', 'Mysterious', 'Perfect', 'Radiant', 'Sacred',
  'Secret', 'Shattered', 'Silent', 'Silver', 'Stolen', 'Timeless', 'Velvet',
  'Vibrant', 'Wild', 'Wistful', 'Wounded', 'Electric', 'Digital', 'Cosmic'
];

// Verse and chorus templates
const verseTemplates = [
  [
    "I've been %verb_ing through the %place all %time_period long",
    "With your %noun still on my %body_part, where you %verb",
    "%pronoun %verb me like nobody else could do",
    "Now I'm left with nothing but this %adjective %noun"
  ],
  [
    "The %place lights up when you walk in the %noun",
    "%pronoun %verb like %adjective %noun in the %place",
    "Every %time_period I spend with you feels like a %noun",
    "Can't help but %verb whenever you're %preposition"
  ],
  [
    "%time_period falls like %noun on a %adjective %place",
    "I %verb myself in thoughts of what could be",
    "If only %pronoun knew what's in my %body_part",
    "This %adjective feeling won't let me %verb"
  ],
  [
    "Walking down %adjective streets in the %place",
    "Memories of you %verb through my %body_part",
    "The %noun we shared now feels so %adjective",
    "I can't help but %verb when I think of %pronoun"
  ]
];

const chorusTemplates = [
  [
    "Oh, this %adjective %noun won't let me go",
    "I keep %verb_ing back to where we used to %verb",
    "In this %place full of %noun and %noun",
    "I'll be %verb_ing for you until the end of %time_period"
  ],
  [
    "%verb me like you mean it, under %adjective %noun",
    "Take my %body_part and make it %verb again",
    "In this %adjective %place, we can be ourselves",
    "Just %verb with me until the %noun %verb_s"
  ],
  [
    "We're %adjective souls in a %adjective world",
    "%verb_ing through %place after %place",
    "No %noun can stop what we've become",
    "This %adjective %noun between us will never %verb"
  ]
];

// Word banks for template filling
const wordBank: Record<string, string[]> = {
  verb: ['run', 'dance', 'sing', 'cry', 'laugh', 'dream', 'fall', 'rise', 'break', 'mend', 'love', 'hate', 'forget', 'remember', 'shine', 'fade', 'burn', 'freeze', 'whisper', 'shout'],
  verb_ing: ['running', 'dancing', 'singing', 'crying', 'laughing', 'dreaming', 'falling', 'rising', 'breaking', 'mending', 'loving', 'hating', 'forgetting', 'remembering', 'shining', 'fading', 'burning', 'freezing', 'whispering', 'shouting'],
  verb_s: ['runs', 'dances', 'sings', 'cries', 'laughs', 'dreams', 'falls', 'rises', 'breaks', 'mends', 'loves', 'hates', 'forgets', 'remembers', 'shines', 'fades', 'burns', 'freezes', 'whispers', 'shouts'],
  noun: ['heart', 'soul', 'mind', 'dream', 'love', 'life', 'world', 'sky', 'ocean', 'river', 'mountain', 'city', 'street', 'rain', 'storm', 'sun', 'moon', 'star', 'shadow', 'light', 'darkness', 'fire', 'ice', 'wind', 'earth', 'memory', 'echo', 'whisper', 'silence', 'noise', 'beat', 'rhythm'],
  adjective: ['beautiful', 'broken', 'endless', 'eternal', 'fading', 'golden', 'hidden', 'infinite', 'lost', 'luminous', 'mysterious', 'perfect', 'radiant', 'sacred', 'secret', 'shattered', 'silent', 'silver', 'stolen', 'timeless', 'velvet', 'vibrant', 'wild', 'wistful', 'wounded', 'electric', 'digital', 'cosmic'],
  place: ['city', 'street', 'ocean', 'mountain', 'sky', 'river', 'forest', 'desert', 'beach', 'highway', 'room', 'house', 'club', 'party', 'concert', 'garden', 'park', 'field', 'valley', 'hill', 'island', 'shore', 'coast', 'horizon', 'universe', 'world', 'galaxy', 'space'],
  time_period: ['night', 'day', 'morning', 'evening', 'dusk', 'dawn', 'twilight', 'midnight', 'summer', 'winter', 'autumn', 'spring', 'year', 'month', 'week', 'hour', 'minute', 'second', 'moment', 'lifetime', 'eternity', 'forever', 'never'],
  body_part: ['heart', 'soul', 'mind', 'eyes', 'hands', 'face', 'lips', 'skin', 'voice', 'breath', 'touch', 'smile', 'tears', 'arms', 'body', 'head', 'chest', 'memory'],
  pronoun: ['you', 'we', 'they', 'I', 'she', 'he', 'it'],
  preposition: ['around', 'above', 'below', 'beside', 'near', 'far', 'within', 'without', 'beyond', 'through', 'across', 'along', 'behind', 'before', 'after', 'during', 'until', 'since', 'for', 'with', 'without']
};

// Function to generate a random title
function generateTitle(): string {
  const titleTypes = [
    // Type 1: Prefix + Suffix (e.g., "Chasing Dreams")
    () => `${getRandomItem(titlePrefixes)} ${getRandomItem(titleSuffixes)}`,

    // Type 2: Adjective + Noun (e.g., "Beautiful Darkness")
    () => `${getRandomItem(titleAdjectives)} ${getRandomItem(titleSuffixes)}`,

    // Type 3: The + Adjective + Noun (e.g., "The Broken Heart")
    () => `The ${getRandomItem(titleAdjectives)} ${getRandomItem(titleSuffixes)}`,

    // Type 4: Single powerful word (from suffixes or adjectives)
    () => getRandomItem([...titleSuffixes, ...titleAdjectives.map(adj => adj.toLowerCase())]),

    // Type 5: Two-word combination with "&" or "+" (e.g., "Love & Light")
    () => {
      const connector = Math.random() > 0.5 ? ' & ' : ' + ';
      return `${getRandomItem(titleSuffixes)}${connector}${getRandomItem(titleSuffixes)}`;
    }
  ];

  // Pick a random title type and generate
  return titleTypes[Math.floor(Math.random() * titleTypes.length)]();
}

// Function to get a random item from an array
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Function to fill a template with random words
function fillTemplate(template: string): string {
  return template.replace(/%(\w+)/g, (match, type) => {
    if (wordBank[type]) {
      return getRandomItem(wordBank[type]);
    }
    return match;
  });
}

// Function to generate a verse
function generateVerse(): string {
  const template = getRandomItem(verseTemplates);
  return template.map(line => fillTemplate(line)).join('\n');
}

// Function to generate a chorus
function generateChorus(): string {
  const template = getRandomItem(chorusTemplates);
  return template.map(line => fillTemplate(line)).join('\n');
}

// Function to generate a complete lyric
function generateLyric(): { title: string, content: string, genre: string, mood: string } {
  const title = generateTitle();
  const genre = getRandomItem(genres);
  const mood = getRandomItem(moods);

  // Generate verses and chorus
  const verse1 = generateVerse();
  const verse2 = generateVerse();
  const chorus = generateChorus();

  // Assemble the complete lyric
  const content = `VERSE 1:\n${verse1}\n\nCHORUS:\n${chorus}\n\nVERSE 2:\n${verse2}\n\nCHORUS:\n${chorus}`;

  return { title, content, genre, mood };
}

// Extract visual elements from lyrics
function extractVisualElementsFromLyrics(lyrics: string): string[] {
  // Common visual nouns that might appear in lyrics
  const visualNouns = [
    'sky', 'stars', 'moon', 'sun', 'ocean', 'sea', 'mountain', 'river', 'forest', 'tree',
    'flower', 'rain', 'storm', 'cloud', 'lightning', 'fire', 'smoke', 'shadow', 'light',
    'darkness', 'city', 'street', 'road', 'car', 'train', 'window', 'door', 'house', 'building',
    'heart', 'eyes', 'hands', 'face', 'tears', 'smile', 'blood', 'wings', 'bird', 'angel',
    'demon', 'heaven', 'hell', 'dream', 'nightmare', 'memory', 'time', 'clock', 'mirror',
    'glass', 'diamond', 'gold', 'silver', 'crown', 'throne', 'sword', 'gun', 'rose', 'chain'
  ];

  // Colors that might appear in lyrics
  const colors = [
    'red', 'blue', 'green', 'yellow', 'purple', 'pink', 'black', 'white', 'gray', 'silver',
    'gold', 'orange', 'brown', 'crimson', 'azure', 'violet', 'indigo', 'turquoise', 'emerald',
    'ruby', 'sapphire', 'amber', 'ivory', 'ebony', 'scarlet', 'navy', 'maroon', 'lime'
  ];

  // Time periods or settings
  const settings = [
    'night', 'day', 'morning', 'evening', 'dusk', 'dawn', 'twilight', 'midnight', 'sunset',
    'sunrise', 'winter', 'summer', 'spring', 'autumn', 'future', 'past', 'ancient', 'modern'
  ];

  // Extract words from lyrics
  const words = lyrics.toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove punctuation
    .split(/\s+/); // Split by whitespace

  // Find visual elements
  const foundElements: string[] = [];

  // Check for visual nouns
  visualNouns.forEach(noun => {
    if (words.includes(noun) && !foundElements.includes(noun)) {
      foundElements.push(noun);
    }
  });

  // Check for colors
  colors.forEach(color => {
    if (words.includes(color) && !foundElements.includes(color)) {
      foundElements.push(color);
    }
  });

  // Check for settings
  settings.forEach(setting => {
    if (words.includes(setting) && !foundElements.includes(setting)) {
      foundElements.push(setting);
    }
  });

  // If we found less than 3 elements, add some based on the genre and mood
  if (foundElements.length < 3) {
    // We'll add these in the detailed prompt generation
  }

  return foundElements;
}

// This function is kept for reference but we use the more detailed prompt generator below
// Generate basic image prompts based on lyrics
// function generateImagePrompt(lyric: { title: string, genre: string, mood: string, style?: string }): string {
//   const style = lyric.style || 'digital art';
//
//   const prompts = [
//     `A ${lyric.mood.toLowerCase()} ${lyric.genre.toLowerCase()} album cover featuring ${lyric.title}, ${style} style`,
//     `${lyric.title}: An artistic interpretation in ${lyric.genre} style, with ${lyric.mood.toLowerCase()} atmosphere, ${style}`,
//     `Abstract visualization of "${lyric.title}" with elements of ${lyric.genre} music, ${lyric.mood.toLowerCase()} mood, vibrant colors, ${style}`,
//     `Cinematic scene inspired by the lyrics "${lyric.title}", ${lyric.mood} atmosphere, ${lyric.genre} aesthetic, ${style} style`
//   ];
//
//   return prompts[Math.floor(Math.random() * prompts.length)];
// }

// Generate detailed image prompts based on lyrics content and metadata
function generateDetailedImagePrompt(params: {
  title: string,
  genre: string,
  mood: string,
  theme?: string,
  style?: string,
  visualElements?: string[],
  content?: string
}): string {
  const style = params.style || 'digital art';
  const theme = params.theme || '';
  const visualElements = params.visualElements || [];

  // Style mappings based on genre
  const styleByGenre: Record<string, string> = {
    'Pop': 'vibrant, colorful, modern',
    'Hip Hop': 'urban, gritty, high contrast',
    'R&B': 'smooth, soulful, warm-toned',
    'Rock': 'dramatic, high-energy, edgy',
    'Electronic': 'futuristic, digital, neon-lit',
    'Jazz': 'sophisticated, moody, elegant',
    'Country': 'rustic, warm, natural',
    'Folk': 'organic, earthy, textured',
    'Classical': 'refined, timeless, elegant',
    'Indie': 'quirky, authentic, intimate'
  };

  // Visual elements based on mood
  const visualByMood: Record<string, string> = {
    'Happy': 'bright lighting, uplifting scenes, clear skies',
    'Nostalgic': 'vintage filter, warm glow, faded colors',
    'Romantic': 'soft focus, intimate setting, warm colors',
    'Energetic': 'dynamic composition, motion blur, vibrant colors',
    'Confident': 'bold colors, strong posture, dramatic lighting',
    'Intense': 'dramatic shadows, close-up, high contrast',
    'Reflective': 'contemplative scene, natural light, serene setting',
    'Determined': 'focused subject, forward movement, clear direction',
    'Melancholic': 'blue tones, rain, solitude, shadows',
    'Sensual': 'soft lighting, intimate setting, close framing',
    'Soulful': 'expressive faces, emotional depth, rich tones',
    'Angry': 'red tones, harsh lighting, jagged elements',
    'Rebellious': 'chaotic elements, rule-breaking composition, edgy',
    'Euphoric': 'light bursts, upward movement, expansive space',
    'Dreamy': 'soft focus, ethereal lighting, floating elements',
    'Futuristic': 'neon lights, technological elements, digital effects'
  };

  // Get style elements based on genre and mood
  const genreStyle = styleByGenre[params.genre] || 'professional';
  const moodVisual = visualByMood[params.mood] || 'balanced composition';

  // Extract a short excerpt from content if available
  let contentExcerpt = '';
  if (params.content) {
    const lines = params.content.split('\n').filter(line =>
      !line.includes('VERSE') && !line.includes('CHORUS') && line.trim() !== ''
    );
    if (lines.length > 0) {
      contentExcerpt = lines[0].substring(0, 50);
    }
  }

  // Combine visual elements into a string
  let visualElementsStr = '';
  if (visualElements.length > 0) {
    visualElementsStr = `featuring ${visualElements.slice(0, 3).join(', ')}`;
  }

  // Build the prompt
  let prompt = `Album cover art for "${params.title}": `;

  // Add theme if available
  if (theme) {
    prompt += `${theme} themed `;
  }

  // Add genre
  prompt += `${params.genre} music. `;

  // Add mood visuals
  prompt += `${moodVisual}. `;

  // Add visual elements if available
  if (visualElementsStr) {
    prompt += `${visualElementsStr}. `;
  }

  // Add genre style
  prompt += `${genreStyle} ${style} style. `;

  // Add content excerpt if available
  if (contentExcerpt) {
    prompt += `Inspired by lyrics: "${contentExcerpt}". `;
  }

  // Add final quality instructions
  prompt += `Professional quality, album cover format, 8K resolution.`;

  return prompt;
}

// Generate real images using Stability AI API
async function generateImageWithStabilityAI(prompt: string): Promise<string> {
  const STABILITY_API_KEY = process.env.STABLE_DIFFUSION_API_KEY;
  const STABILITY_API_HOST = 'https://api.stability.ai';
  const STABILITY_ENGINE_ID = 'stable-diffusion-xl-1024-v1-0';

  if (!STABILITY_API_KEY) {
    console.warn('Stability AI API key missing, using placeholder image instead.');
    return generatePlaceholderImage(prompt);
  }

  try {
    console.log(`Generating image with Stability AI for prompt: "${prompt}"`);

    const fetch = (await import('node-fetch')).default;
    const response = await fetch(
      `${STABILITY_API_HOST}/v1/generation/${STABILITY_ENGINE_ID}/text-to-image`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${STABILITY_API_KEY}`,
        },
        body: JSON.stringify({
          text_prompts: [{ text: prompt }],
          cfg_scale: 7,
          // Use one of the allowed dimensions for SDXL model
          // 768x1344 is a portrait orientation that works well with masonry layout
          height: 768,
          width: 1344,
          steps: 30,
          samples: 1,
          // Add style preset for more consistent results
          style_preset: "photographic",
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Stability API error: ${response.status} ${errorText}`);
      return generatePlaceholderImage(prompt);
    }

    const responseJSON = await response.json();
    const imageArtifact = responseJSON.artifacts?.[0];

    if (imageArtifact?.base64) {
      console.log('✅ Successfully generated image with Stability AI');
      return `data:image/png;base64,${imageArtifact.base64}`;
    } else {
      console.error('No image data found in Stability API response');
      return generatePlaceholderImage(prompt);
    }
  } catch (error) {
    console.error('Error calling Stability API:', error);
    return generatePlaceholderImage(prompt);
  }
}

// Fallback to placeholder image if API fails
function generatePlaceholderImage(prompt: string): string {
  // Use a data URL for a simple colored rectangle based on the prompt
  // This ensures we have valid image data that can be displayed
  const hash = Buffer.from(prompt).toString('base64').substring(0, 10);
  const hue = parseInt(hash, 36) % 360; // Generate a hue value between 0-359

  // Create a simple SVG with a gradient background
  const svg = `
    <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="hsl(${hue}, 80%, 30%)" />
          <stop offset="100%" stop-color="hsl(${(hue + 40) % 360}, 80%, 50%)" />
        </linearGradient>
      </defs>
      <rect width="512" height="512" fill="url(#grad)" />
      <text x="50%" y="50%" font-family="Arial" font-size="24" fill="white" text-anchor="middle">${prompt.substring(0, 30)}</text>
    </svg>
  `;

  // Convert SVG to base64 data URL
  return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
}

// Main seeding function
async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // 1. Insert users
    console.log('👤 Inserting users...');
    const { error: userError } = await supabase
      .from('users')
      .insert(dummyUsers)
      .select();

    if (userError) {
      throw new Error(`Error inserting users: ${userError.message}`);
    }

    console.log(`✅ Successfully inserted ${dummyUsers.length} users`);

    // 2. Generate and insert lyrics for each user
    console.log('🎵 Generating and inserting lyrics...');
    const allLyrics: any[] = [];

    // Define AI writers with their specialties - make sure Ava Clarke is first
    const aiWriters = [
      {
        id: 'ava_clarke',
        name: 'Ava Clarke',
        genre: 'Lead Writer',
        specialties: ['Cross-genre expertise', 'Emotional storytelling', 'Lyrical innovation', 'Versatile style', 'Collaborative leadership'],
        avatar: '/avatars/BETTER AVATARS/ava clark.png',
        description: 'Born to a jazz musician father and poet mother, Ava grew up immersed in both music and literature. Her multicultural background informs her global perspective on music. As the lead writer at APIT, she coordinates specialized genre writers.'
      },
      {
        id: 'elias_fontaine',
        name: 'Elias Fontaine',
        genre: 'Hip-Hop',
        specialties: ['Flow', 'Wordplay', 'Street narratives', 'Conscious themes', 'Trap beats'],
        avatar: '/avatars/BETTER AVATARS/Elias Fontaine.png',
        description: 'Born in Brooklyn, 24-year-old Elias grew up surrounded by hip-hop culture. He started writing rhymes at 12 and never stopped. His style blends old-school flow with modern trap sensibilities.'
      },
      {
        id: 'luna_rivers',
        name: 'Luna Rivers',
        genre: 'R&B',
        specialties: ['Emotional depth', 'Vocal melodies', 'Love songs', 'Soul influences', 'Harmonies'],
        avatar: '/avatars/BETTER AVATARS/Luna Rivers.png',
        description: 'At just 23, Luna discovered her voice in church choir before studying jazz vocals in college. Her neo-soul approach blends classic R&B with modern production sensibilities. She writes from personal experience, focusing on emotional authenticity.'
      },
      {
        id: 'zane_mercer',
        name: 'Zane Mercer',
        genre: 'Jazz',
        specialties: ['Improvisation', 'Complex harmonies', 'Sophisticated wordplay', 'Musical references', 'Artistic expression'],
        avatar: '/avatars/BETTER AVATARS/Zane Mercer.png',
        description: 'At 38, Zane studied poetry and music theory before becoming a jazz composer and lyricist. His intellectual approach combines literary influences with deep musical knowledge and years of experience.'
      },
      {
        id: 'alex_stone',
        name: 'Alex Stone',
        genre: 'Rock',
        specialties: ['Guitar-driven', 'Anthemic choruses', 'Rebellious themes', 'Raw emotion', 'Classic structures'],
        avatar: '/avatars/BETTER AVATARS/Alex Stone.png',
        description: 'At 25, Alex cut his teeth in garage bands before developing a distinctive rock songwriting style. His approach balances raw energy with thoughtful lyricism and classic rock influences, bringing a youthful edge to the genre.'
      },
      {
        id: 'olivia_chen',
        name: 'Olivia Chen',
        genre: 'Pop',
        specialties: ['Catchy hooks', 'Commercial appeal', 'Relatable lyrics', 'Upbeat energy', 'Contemporary production'],
        avatar: '/avatars/BETTER AVATARS/Olivia Chen.png',
        description: 'Olivia has a natural talent for crafting earworm hooks and relatable lyrics that connect with mainstream audiences. Her background in commercial jingle writing gives her a keen sense for what makes a song stick in listeners\' minds.'
      },
      {
        id: 'marcus_johnson',
        name: 'Marcus Johnson',
        genre: 'Country',
        specialties: ['Storytelling', 'Traditional themes', 'Heartland imagery', 'Acoustic sensibility', 'Emotional resonance'],
        avatar: '/avatars/BETTER AVATARS/Marcus Johnson.png',
        description: 'Growing up in rural Tennessee, Marcus developed a deep appreciation for country music traditions. His writing captures the essence of American heartland values while incorporating contemporary elements that keep the genre evolving.'
      },
      {
        id: 'sofia_rodriguez',
        name: 'Sofia Rodriguez',
        genre: 'Latin',
        specialties: ['Rhythmic patterns', 'Cultural references', 'Bilingual lyrics', 'Dance elements', 'Passionate themes'],
        avatar: '/avatars/BETTER AVATARS/Sofia Rodriguez.png',
        description: 'Sofia brings her rich cultural heritage to her writing, seamlessly blending Spanish and English lyrics with authentic Latin rhythms. Her work spans reggaeton, bachata, salsa, and contemporary Latin pop with equal authenticity.'
      },
      {
        id: 'kai_nakamura',
        name: 'Kai Nakamura',
        genre: 'Electronic',
        specialties: ['Futuristic themes', 'Minimalist lyrics', 'Atmospheric writing', 'Digital imagery', 'Conceptual approaches'],
        avatar: '/avatars/BETTER AVATARS/Kai Nakamura.png',
        description: 'With a background in both music production and creative writing, Kai specializes in crafting lyrics that complement electronic music production. His writing often explores the relationship between humanity and technology.'
      },
      {
        id: 'amara_okafor',
        name: 'Amara Okafor',
        genre: 'Afrobeat',
        specialties: ['Cultural fusion', 'Rhythmic writing', 'Social commentary', 'Celebratory themes', 'Global perspective'],
        avatar: '/avatars/BETTER AVATARS/Amara Okafor.png',
        description: 'Amara\'s writing bridges traditional African musical elements with contemporary global influences. Her lyrics often address social issues while maintaining the infectious energy that makes Afrobeat so compelling worldwide.'
      },
      {
        id: 'ethan_parker',
        name: 'Ethan Parker',
        genre: 'Indie',
        specialties: ['Introspective lyrics', 'Literary references', 'Unconventional structures', 'Emotional vulnerability', 'Artistic integrity'],
        avatar: '/avatars/BETTER AVATARS/Ethan Parker.png',
        description: 'Ethan approaches songwriting as literary art, crafting lyrics that reward close listening and multiple interpretations. His background in poetry and independent music gives his writing a distinctive voice outside mainstream conventions.'
      }
    ];

    // Generate a limited number of lyrics (10 total)
    const MAX_LYRICS_TOTAL = 10;
    let lyricsCount = 0;

    // Loop through writers until we reach the maximum number of lyrics
    for (const writer of aiWriters) {
      // Generate 1 lyric per writer, but stop when we reach the maximum
      if (lyricsCount >= MAX_LYRICS_TOTAL) break;

      const generatedLyric = generateLyric();
      lyricsCount++;

      // Use the writer's genre for consistency
      const lyricGenre = writer.genre;

      // Generate a random mood that fits the genre
      const moodsByGenre: Record<string, string[]> = {
        'Pop': ['Happy', 'Nostalgic', 'Romantic', 'Energetic'],
        'Hip-Hop': ['Confident', 'Intense', 'Reflective', 'Determined'],
        'R&B': ['Romantic', 'Melancholic', 'Sensual', 'Soulful'],
        'Rock': ['Energetic', 'Angry', 'Rebellious', 'Intense'],
        'Electronic': ['Euphoric', 'Dreamy', 'Futuristic', 'Energetic'],
        'Jazz': ['Sophisticated', 'Mellow', 'Introspective', 'Smooth'],
        'Country': ['Nostalgic', 'Heartfelt', 'Storytelling', 'Authentic'],
        'Latin': ['Passionate', 'Energetic', 'Romantic', 'Celebratory'],
        'Afrobeat': ['Energetic', 'Rhythmic', 'Celebratory', 'Cultural'],
        'Indie': ['Introspective', 'Melancholic', 'Authentic', 'Quirky'],
        'Lead Writer': ['Versatile', 'Emotional', 'Innovative', 'Balanced']
      };

      const genreMoods = moodsByGenre[lyricGenre] || moods;
      const lyricMood = getRandomItem(genreMoods);

      // Generate a theme that fits the genre and mood
      const themesByGenre: Record<string, string[]> = {
        'Pop': ['Love', 'Heartbreak', 'Self-discovery', 'Celebration'],
        'Hip-Hop': ['Struggle', 'Success', 'Identity', 'Social commentary'],
        'R&B': ['Love', 'Relationships', 'Desire', 'Healing'],
        'Rock': ['Freedom', 'Rebellion', 'Power', 'Authenticity'],
        'Electronic': ['Future', 'Technology', 'Escape', 'Transcendence'],
        'Jazz': ['Sophistication', 'Artistry', 'Experience', 'Emotion'],
        'Country': ['Heartland', 'Relationships', 'Tradition', 'Life lessons'],
        'Latin': ['Passion', 'Heritage', 'Celebration', 'Family'],
        'Afrobeat': ['Culture', 'Identity', 'Celebration', 'Unity'],
        'Indie': ['Introspection', 'Relationships', 'Existential', 'Personal growth'],
        'Lead Writer': ['Universal themes', 'Human experience', 'Emotional depth', 'Artistic vision']
      };

      const genreThemes = themesByGenre[lyricGenre] || ['Love', 'Loss', 'Hope', 'Freedom', 'Dreams'];
      const lyricTheme = getRandomItem(genreThemes);

      // Assign to a random user
      const randomUser = getRandomItem(dummyUsers);

      // Create the lyric with metadata that matches the database schema
      // Store additional metadata as JSON in a metadata field if it exists
      const lyricMetadata = {
        genre: lyricGenre,
        mood: lyricMood,
        theme: lyricTheme,
        writer_id: writer.id,
        writer_name: writer.name,
        writer_avatar: writer.avatar,
        writer_description: writer.description
      };

      // Basic lyric object with only the fields that exist in the database
      const lyricObject: any = {
        id: uuidv4(),
        user_id: randomUser.id,
        title: generatedLyric.title,
        content: generatedLyric.content,
        is_public: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Add metadata field if it exists in the schema
      lyricObject.metadata = JSON.stringify(lyricMetadata);

      // Push to allLyrics array
      allLyrics.push(lyricObject);
    }

    const { error: lyricsError } = await supabase
      .from('lyrics')
      .insert(allLyrics)
      .select();

    if (lyricsError) {
      throw new Error(`Error inserting lyrics: ${lyricsError.message}`);
    }

    console.log(`✅ Successfully inserted ${allLyrics.length} lyrics`);

    // 3. Generate and insert images for each lyric
    console.log('🖼️ Generating and inserting images...');
    const allImages: any[] = [];

    // Define image styles
    const imageStyles = [
      'digital art', 'photorealistic', 'oil painting', 'watercolor',
      'abstract', 'minimalist', 'surrealist', 'pop art',
      'cyberpunk', 'vaporwave', 'retro', 'futuristic'
    ];

    for (const lyric of allLyrics) {
      // Extract visual elements from the lyrics
      const visualElements = extractVisualElementsFromLyrics(lyric.content);

      // Use the lyric's metadata for the image prompt
      const imageStyle = getRandomItem(imageStyles);

      // Default theme for images
      const defaultTheme = getRandomItem(['Love', 'Loss', 'Hope', 'Freedom', 'Dreams']);

      // Generate a detailed prompt based on lyrics content and metadata
      const prompt = generateDetailedImagePrompt({
        title: lyric.title,
        genre: lyric.genre || getRandomItem(genres),
        mood: typeof lyric.mood === 'string' ? lyric.mood : getRandomItem(moods),
        theme: defaultTheme,
        style: imageStyle,
        visualElements: visualElements,
        content: lyric.content
      });

      // Check if we already have an image with a similar prompt
      const { data: existingImages } = await supabase
        .from('generated_images')
        .select('image_url')
        .filter('prompt', 'ilike', `%${prompt.substring(0, 30)}%`)
        .limit(1);

      let imageUrl;

      if (existingImages && existingImages.length > 0) {
        // Use existing image to save API credits
        console.log(`🔄 Using existing image for prompt: "${prompt.substring(0, 30)}..."`);
        imageUrl = existingImages[0].image_url;
      } else {
        // Generate new image with Stability AI
        console.log(`🎨 Generating new image for prompt: "${prompt.substring(0, 30)}..."`);
        imageUrl = await generateImageWithStabilityAI(prompt);
      }

      // Extract a short excerpt from lyrics
      let lyricsExcerpt = '';
      const lines = lyric.content.split('\n').filter(line =>
        !line.includes('VERSE') && !line.includes('CHORUS') && line.trim() !== ''
      );
      if (lines.length > 0) {
        lyricsExcerpt = lines.slice(0, 2).join(' ');
        if (lyricsExcerpt.length > 100) {
          lyricsExcerpt = lyricsExcerpt.substring(0, 97) + '...';
        }
      }

      // Create rich metadata for the image
      const metadata = {
        title: lyric.title,
        artist: (lyric as any).writer_name || 'AI Generated',
        genre: lyric.genre || getRandomItem(genres),
        mood: lyric.mood || getRandomItem(moods),
        theme: (lyric as any).theme || getRandomItem(['Love', 'Loss', 'Hope', 'Freedom', 'Dreams']),
        style: imageStyle,
        lyrics_id: lyric.id,
        lyrics_content: lyricsExcerpt,
        writer_name: (lyric as any).writer_name || '',
        writer_avatar: (lyric as any).writer_avatar || '',
        writer_description: (lyric as any).writer_description || '',
        visual_elements: visualElements.join(', ')
      };

      allImages.push({
        id: uuidv4(),
        user_id: lyric.user_id,
        prompt: prompt,
        image_url: imageUrl,
        content_type: 'lyric',
        linked_content_id: lyric.id,
        is_public: true,
        metadata: JSON.stringify(metadata)
      });
    }

    const { error: imagesError } = await supabase
      .from('generated_images')
      .insert(allImages)
      .select();

    if (imagesError) {
      throw new Error(`Error inserting images: ${imagesError.message}`);
    }

    console.log(`✅ Successfully inserted ${allImages.length} images`);

    // 4. Summary of inserted data
    console.log('\n🌱 Database seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`  - Users: ${dummyUsers.length}`);
    console.log(`  - Lyrics: ${allLyrics.length}`);
    console.log(`  - Images: ${allImages.length}`);

    // 5. Sample data for verification
    console.log('\n📋 Sample data:');
    console.log('  User:', dummyUsers[0].name, `(${dummyUsers[0].id})`);
    console.log('  Lyric:', allLyrics[0].title, `(${allLyrics[0].id})`);
    console.log('  Image prompt:', allImages[0].prompt);
    console.log('  Image URL:', allImages[0].image_url);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedDatabase();
