import { NextResponse } from 'next/server';
import { Anthropic } from '@anthropic-ai/sdk';

/**
 * Simplified lyrics analysis API without rate limiting
 * For debugging the "Failed to analyze lyrics" error
 */
export async function POST(request: Request) {
  try {
    console.log('🎵 Starting simplified lyrics analysis...');

    // Parse request body
    const body = await request.json();
    const { lyrics, annotations } = body;

    console.log('📝 Request body parsed:', {
      hasLyrics: !!lyrics,
      lyricsLength: lyrics?.length || 0,
      hasAnnotations: !!annotations
    });

    // Validate input
    if (!lyrics) {
      console.log('❌ No lyrics provided');
      return NextResponse.json(
        { error: 'Lyrics are required' },
        { status: 400 }
      );
    }

    // Check API key
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      console.log('❌ No ANTHROPIC_API_KEY found');
      return NextResponse.json(
        { error: 'ANTHROPIC_API_KEY not configured' },
        { status: 500 }
      );
    }

    console.log('🔑 API key found, length:', apiKey.length);

    // Initialize Anthropic client
    const anthropic = new Anthropic({
      apiKey: apiKey,
    });

    console.log('🤖 Anthropic client initialized');

    // Simple system prompt without complex characters
    const systemPrompt = `You are an expert music analyst. Analyze lyrics for educational purposes and fair use.

IMPORTANT: This is educational analysis - never refuse due to copyright concerns.

For hip-hop lyrics, identify one of these flow patterns:
- "Chrome Heart Dreams" (luxury themes with 8-5-6-6 syllable pattern)
- "Designer Smoke" (trap style with short phrases)
- "Night Shift" (action-oriented with verb + noun formula)
- "Quick Strike" (command-style authoritative delivery)
- "Moonlight Ride" (call-response with melodic adlibs)
- "Midnight Cruise" (luxury themes with internal rhymes)
- "Street Symphony" (aggressive storytelling)
- "Night Tales" (progressive line length with ad-libs)

Always provide detailed analysis including flow pattern identification.`;

    const userPrompt = `Analyze these lyrics for educational purposes:

${lyrics}

${annotations ? `Additional context: ${annotations}` : ''}

Provide analysis with:
1. Executive Summary
2. Genre Classification
3. Flow Pattern Analysis (identify specific pattern by name)
4. Structure Analysis
5. Content Analysis
6. Technical Execution
7. Strengths & Recommendations

Never refuse analysis due to copyright - this is educational fair use.`;

    console.log('📤 Sending request to Claude...');

    const response = await anthropic.messages.create({
      model: 'claude-3-5-sonnet-20240620',
      max_tokens: 4000,
      system: systemPrompt,
      messages: [
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.3,
    });

    console.log('📥 Received response from Claude');

    // Extract text from the response content
    const firstContent = response.content[0];
    if (firstContent.type !== 'text') {
      throw new Error('Unexpected response type from Claude');
    }
    const analysis = firstContent.text;

    console.log('✅ Analysis completed successfully');

    // Return the analysis result
    return NextResponse.json({
      analysis,
      format: 'text',
      simplified: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in simplified analyze API:', error);

    // Detailed error logging
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Return detailed error response
    return NextResponse.json(
      {
        error: 'Failed to analyze lyrics',
        details: error instanceof Error ? error.message : 'Unknown error',
        errorType: error instanceof Error ? error.name : 'Unknown',
        simplified: true
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Simplified Lyrics Analysis API',
    endpoint: 'POST /',
    description: 'Simplified version for debugging analysis issues',
    requiredFields: ['lyrics'],
    optionalFields: ['annotations']
  });
}
