import { NextResponse } from 'next/server';
import { enhanceAudioSimple, enhanceAudioAdvanced, checkAuphonicStatus, AuphonicProcessingOptions } from '@/utils/audioEnhancement';

/**
 * API route for enhancing audio using Auphonic
 * 
 * POST /api/retune/auphonic
 * - Accepts audio file and enhancement options
 * - Returns processing ID and status
 * 
 * GET /api/retune/auphonic?id={productionId}
 * - Checks status of a processing job
 * - Returns status and output URL if complete
 */

export async function GET(request: Request) {
  try {
    // Get production ID from query parameters
    const { searchParams } = new URL(request.url);
    const productionId = searchParams.get('id');
    
    if (!productionId) {
      return NextResponse.json({ error: 'Production ID is required' }, { status: 400 });
    }
    
    // Check status of the production
    const result = await checkAuphonicStatus(productionId);
    
    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error checking enhancement status:', error);
    return NextResponse.json(
      { error: 'Failed to check enhancement status' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Parse the multipart form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const optionsJson = formData.get('options') as string;
    const mode = formData.get('mode') as string || 'simple';
    
    if (!audioFile) {
      return NextResponse.json({ error: 'Audio file is required' }, { status: 400 });
    }
    
    // Parse options if provided
    let options: AuphonicProcessingOptions = {};
    if (optionsJson) {
      try {
        options = JSON.parse(optionsJson);
      } catch (e) {
        return NextResponse.json({ error: 'Invalid options format' }, { status: 400 });
      }
    }
    
    // Process the audio file
    let result;
    if (mode === 'advanced') {
      result = await enhanceAudioAdvanced(audioFile, options);
    } else {
      result = await enhanceAudioSimple(audioFile, options);
    }
    
    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error enhancing audio:', error);
    return NextResponse.json(
      { error: 'Failed to enhance audio' },
      { status: 500 }
    );
  }
}
