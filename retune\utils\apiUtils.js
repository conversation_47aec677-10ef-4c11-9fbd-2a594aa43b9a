/**
 * API utility functions for Retune
 * Handles communication with backend services
 */

// Base URL for API endpoints
const API_BASE_URL = '/api';

// Send a recording to the server for analysis
const analyzeRecording = async (audioBlob) => {
  try {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.wav');
    
    const response = await fetch(`${API_BASE_URL}/analyze`, {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error analyzing recording:', error);
    return { success: false, error: error.message };
  }
};

// Send a recording to the server for enhancement
const enhanceRecording = async (audioBlob, parameters = {}) => {
  try {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.wav');
    
    // Add enhancement parameters
    Object.entries(parameters).forEach(([key, value]) => {
      formData.append(key, value);
    });
    
    const response = await fetch(`${API_BASE_URL}/enhance`, {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
    }
    
    // The response should be a blob containing the enhanced audio
    const enhancedAudio = await response.blob();
    return { 
      success: true, 
      enhancedAudio,
      url: URL.createObjectURL(enhancedAudio)
    };
  } catch (error) {
    console.error('Error enhancing recording:', error);
    return { success: false, error: error.message };
  }
};

// Save a recording to the server
const saveRecordingToServer = async (audioBlob, metadata = {}) => {
  try {
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.wav');
    
    // Add metadata
    Object.entries(metadata).forEach(([key, value]) => {
      formData.append(key, JSON.stringify(value));
    });
    
    const response = await fetch(`${API_BASE_URL}/record`, {
      method: 'POST',
      body: formData
    });
    
    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error saving recording to server:', error);
    return { success: false, error: error.message };
  }
};

// Send feedback about an enhanced recording
const sendFeedback = async (recordingId, feedback) => {
  try {
    const response = await fetch(`${API_BASE_URL}/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        recordingId,
        feedback
      })
    });
    
    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    console.error('Error sending feedback:', error);
    return { success: false, error: error.message };
  }
};

// Fallback functions for when the server is not available
// These simulate the API responses for development purposes

const simulateAnalysis = (audioBlob) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          pitch: {
            average: 220 + Math.random() * 40,
            min: 180 + Math.random() * 20,
            max: 260 + Math.random() * 30,
            stability: 0.7 + Math.random() * 0.3
          },
          volume: {
            average: 0.6 + Math.random() * 0.3,
            dynamic_range: 0.4 + Math.random() * 0.4
          },
          clarity: {
            score: 0.65 + Math.random() * 0.35,
            articulation: 0.7 + Math.random() * 0.3
          },
          confidence: {
            score: 0.6 + Math.random() * 0.4,
            steadiness: 0.7 + Math.random() * 0.3
          }
        }
      });
    }, 1500);
  });
};

const simulateEnhancement = (audioBlob) => {
  return new Promise(resolve => {
    setTimeout(() => {
      // Just return the original audio for simulation
      resolve({
        success: true,
        enhancedAudio: audioBlob,
        url: URL.createObjectURL(audioBlob)
      });
    }, 2000);
  });
};

// Export functions with fallbacks
export {
  analyzeRecording: process.env.NODE_ENV === 'development' ? simulateAnalysis : analyzeRecording,
  enhanceRecording: process.env.NODE_ENV === 'development' ? simulateEnhancement : enhanceRecording,
  saveRecordingToServer,
  sendFeedback
};
