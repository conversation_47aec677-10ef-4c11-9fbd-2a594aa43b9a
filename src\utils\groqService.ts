import { Groq } from 'groq-sdk';

// Initialize Groq client
let groqClient: Groq | null = null;

// Initialize the client lazily to avoid issues during build time
const getGroqClient = () => {
  if (!groqClient) {
    if (!process.env.GROQ_API_KEY) {
      throw new Error('Groq API key is not configured');
    }
    groqClient = new Groq({ apiKey: process.env.GROQ_API_KEY });
  }
  return groqClient;
};

// Available Groq models in order of preference (working models only)
const GROQ_MODELS = [
  'llama-3.3-70b-versatile', // Primary working model
  'llama-3.1-70b-versatile', // Backup
  'mixtral-8x7b-32768',      // Alternative
  'llama3-8b-8192'           // Fast fallback
];

/**
 * Try multiple Groq models with fallback
 * @param requestFn Function that makes the API request
 * @param startModelIndex Index to start trying models from
 * @returns Promise with the API response
 */
async function tryGroqModelsWithFallback(requestFn: (model: string) => Promise<any>, startModelIndex: number = 0): Promise<any> {
  for (let i = startModelIndex; i < GROQ_MODELS.length; i++) {
    const model = GROQ_MODELS[i];
    try {
      console.log(`Trying Groq model: ${model}`);
      const result = await requestFn(model);
      console.log(`✅ Success with model: ${model}`);
      return result;
    } catch (error: any) {
      console.log(`❌ Failed with model ${model}:`, error.message);

      // If this is the last model, throw the error
      if (i === GROQ_MODELS.length - 1) {
        throw error;
      }

      // Continue to next model
      continue;
    }
  }
}

/**
 * Generates text using Groq models with fallback
 * @param prompt The prompt to generate text from
 * @param options Optional parameters for the generation
 * @returns Promise with the generated text
 */
export async function generateText(
  prompt: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
  }
) {
  try {
    const client = getGroqClient();
    const preferredModel = options?.model;
    const temperature = options?.temperature || 0.7;
    const maxTokens = options?.maxTokens || 1000;
    const systemPrompt = options?.systemPrompt || '';

    const messages = [];

    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    messages.push({
      role: 'user',
      content: prompt
    });

    // If a specific model is requested, try it first
    let startIndex = 0;
    if (preferredModel) {
      const preferredIndex = GROQ_MODELS.indexOf(preferredModel);
      if (preferredIndex !== -1) {
        startIndex = preferredIndex;
      }
    }

    const response = await tryGroqModelsWithFallback(async (model) => {
      return await client.chat.completions.create({
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      });
    }, startIndex);

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating text with Groq (all models failed):', error);
    throw error;
  }
}

/**
 * Generates lyrics using Groq models
 * @param prompt The prompt to generate lyrics from
 * @param options Optional parameters for the generation
 * @returns Promise with the generated lyrics
 */
export async function generateLyrics(
  prompt: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    genre?: string;
    mood?: string;
  }
) {
  try {
    const client = getGroqClient();
    const preferredModel = options?.model;
    const temperature = options?.temperature || 0.8;
    const maxTokens = options?.maxTokens || 1000;
    const genre = options?.genre || '';
    const mood = options?.mood || '';

    // Create intelligent system prompt that works even without specific parameters
    let systemPrompt = 'You are a professional songwriter with expertise in creating original, creative, and engaging lyrics across all genres and styles.';

    if (genre || mood) {
      systemPrompt += ' Create lyrics';
      if (genre) systemPrompt += ` in the ${genre} genre`;
      if (mood) systemPrompt += ` with a ${mood} mood`;
      systemPrompt += '.';
    } else {
      // If no genre/mood specified, provide intelligent guidance
      systemPrompt += ' Analyze the user\'s prompt to determine the most appropriate genre, mood, and style, then create compelling lyrics that match the theme and emotion of their request.';
    }

    // Add universal quality guidelines
    systemPrompt += ' Focus on creating memorable hooks, authentic emotions, vivid imagery, and natural flow. Structure the lyrics with clear verses, choruses, and bridges as appropriate.';

    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: prompt
      }
    ];

    // If a specific model is requested, try it first
    let startIndex = 0;
    if (preferredModel) {
      const preferredIndex = GROQ_MODELS.indexOf(preferredModel);
      if (preferredIndex !== -1) {
        startIndex = preferredIndex;
      }
    }

    const response = await tryGroqModelsWithFallback(async (model) => {
      return await client.chat.completions.create({
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      });
    }, startIndex);

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error generating lyrics with Groq (all models failed):', error);
    throw error;
  }
}

/**
 * Analyzes lyrics using Groq models
 * @param lyrics The lyrics to analyze
 * @param options Optional parameters for the analysis
 * @returns Promise with the analysis results
 */
export async function analyzeLyrics(
  lyrics: string,
  options?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    analysisType?: 'sentiment' | 'theme' | 'structure' | 'full';
  }
) {
  try {
    const client = getGroqClient();
    const preferredModel = options?.model;
    const temperature = options?.temperature || 0.3;
    const maxTokens = options?.maxTokens || 1000;
    const analysisType = options?.analysisType || 'full';

    let systemPrompt = 'You are a music analyst with expertise in analyzing lyrics in the style of MT (the user). Your analysis should be detailed, insightful, and focus on the artistic and technical aspects of the lyrics.';
    let userPrompt = '';

    switch (analysisType) {
      case 'sentiment':
        userPrompt = `Analyze the sentiment of these lyrics. Provide the overall mood, emotional tone, and any shifts in sentiment throughout the lyrics:\n\n${lyrics}`;
        break;
      case 'theme':
        userPrompt = `Analyze the themes and subject matter of these lyrics. Identify the main topics, metaphors, and any deeper meanings:\n\n${lyrics}`;
        break;
      case 'structure':
        userPrompt = `Analyze the structure of these lyrics. Identify verses, choruses, bridges, and other sections. Comment on the rhyme scheme and flow:\n\n${lyrics}`;
        break;
      case 'full':
      default:
        userPrompt = `Provide a comprehensive analysis of these lyrics. Include:
1. Overall theme and subject matter
2. Sentiment and emotional tone
3. Structure (verses, chorus, etc.)
4. Rhyme scheme and flow
5. Literary devices used
6. Potential genre classification
7. Strengths of the lyrics
8. Suggestions for improvement

Lyrics:

${lyrics}`;
        break;
    }

    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    // If a specific model is requested, try it first
    let startIndex = 0;
    if (preferredModel) {
      const preferredIndex = GROQ_MODELS.indexOf(preferredModel);
      if (preferredIndex !== -1) {
        startIndex = preferredIndex;
      }
    }

    const response = await tryGroqModelsWithFallback(async (model) => {
      return await client.chat.completions.create({
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
      });
    }, startIndex);

    return response.choices[0].message.content;
  } catch (error) {
    console.error('Error analyzing lyrics with Groq (all models failed):', error);
    throw error;
  }
}

// Named exports for backward compatibility
export { generateText as groqGenerateText };
export { generateLyrics as groqGenerateLyrics };
export { analyzeLyrics as groqAnalyzeLyrics };
