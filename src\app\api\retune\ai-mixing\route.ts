import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import os from 'os';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    // Get the path to the Python script
    const scriptDir = path.join(process.cwd(), 'python', 'retune');
    const scriptPath = path.join(scriptDir, 'ai_mixing_presets.py');
    
    // Execute the command to list presets
    const pythonCommand = `python "${scriptPath}" list`;
    const { stdout, stderr } = await execAsync(pythonCommand);
    
    if (stderr) {
      console.error('Python script error:', stderr);
    }
    
    // Parse the output to extract preset information
    const presets = parsePresetList(stdout);
    
    // Return the preset list
    return NextResponse.json({
      success: true,
      presets
    });
  } catch (error) {
    console.error('Error listing presets:', error);
    return NextResponse.json(
      { error: 'Failed to list presets' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    if (!request.headers.get('content-type')?.includes('multipart/form-data')) {
      return NextResponse.json(
        { error: 'Request must be multipart/form-data' },
        { status: 400 }
      );
    }

    // Get the form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const operation = formData.get('operation') as string;
    const presetId = formData.get('presetId') as string;

    if (!audioFile) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    if (!operation) {
      return NextResponse.json(
        { error: 'Operation is required (recommend, analyze, or customize)' },
        { status: 400 }
      );
    }

    // Create a temporary directory
    const tempDir = path.join(os.tmpdir(), 'apit-retune');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Save the audio file to the temporary directory
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
    const audioPath = path.join(tempDir, `audio_${Date.now()}.wav`);
    fs.writeFileSync(audioPath, audioBuffer);

    // Get the path to the Python script
    const scriptDir = path.join(process.cwd(), 'python', 'retune');
    const scriptPath = path.join(scriptDir, 'ai_mixing_presets.py');

    // Create a temporary file for the output
    const outputPath = path.join(tempDir, `output_${Date.now()}.json`);

    // Build the command based on the operation
    let pythonCommand = '';
    
    if (operation === 'recommend') {
      pythonCommand = `python "${scriptPath}" recommend "${audioPath}" --output "${outputPath}"`;
    } 
    else if (operation === 'analyze') {
      pythonCommand = `python "${scriptPath}" analyze "${audioPath}" --output "${outputPath}"`;
    } 
    else if (operation === 'customize') {
      // Validate customize parameters
      if (!presetId) {
        return NextResponse.json(
          { error: 'Preset ID is required for customization' },
          { status: 400 }
        );
      }
      
      pythonCommand = `python "${scriptPath}" customize "${presetId}" "${audioPath}" --output "${outputPath}"`;
    } 
    else {
      return NextResponse.json(
        { error: 'Invalid operation. Must be recommend, analyze, or customize' },
        { status: 400 }
      );
    }
    
    // Execute the command
    const { stdout, stderr } = await execAsync(pythonCommand);
    
    if (stderr) {
      console.error('Python script error:', stderr);
    }
    
    // Check if the output file exists
    if (!fs.existsSync(outputPath)) {
      return NextResponse.json(
        { error: 'Failed to process audio' },
        { status: 500 }
      );
    }
    
    // Read the output file
    const outputData = JSON.parse(fs.readFileSync(outputPath, 'utf-8'));
    
    // Clean up temporary files
    try {
      fs.unlinkSync(audioPath);
      fs.unlinkSync(outputPath);
    } catch (error) {
      console.error('Error cleaning up temporary files:', error);
    }
    
    // Return the results
    return NextResponse.json({
      success: true,
      operation,
      result: outputData
    });
  } catch (error) {
    console.error('Error processing audio:', error);
    return NextResponse.json(
      { error: 'Failed to process audio' },
      { status: 500 }
    );
  }
}

/**
 * Parse the output of the list command to extract preset information
 */
function parsePresetList(output: string): any {
  const presets = {};
  const lines = output.trim().split('\n');
  
  let currentGenre = '';
  
  for (const line of lines) {
    // Check for genre header
    const genreMatch = line.match(/^=== (.+) ===/);
    if (genreMatch) {
      currentGenre = genreMatch[1].toLowerCase();
      continue;
    }
    
    // Check for preset entry
    const presetMatch = line.match(/^\s+(.+?):\s+(.+?)\s+-\s+(.+)$/);
    if (presetMatch && currentGenre) {
      const [_, presetId, presetName, presetDescription] = presetMatch;
      
      if (!presets[currentGenre]) {
        presets[currentGenre] = [];
      }
      
      presets[currentGenre].push({
        id: presetId.trim(),
        name: presetName.trim(),
        description: presetDescription.trim(),
        genre: currentGenre
      });
    }
  }
  
  return presets;
}
