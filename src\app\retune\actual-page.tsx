'use client';

import MainAppLayout from '@/components/layout/MainAppLayout';
import React, { useState } from 'react';
import VoiceCloner from './components/VoiceCloner';
import VoiceEnhancer from './components/VoiceEnhancer';

// Define tab types
type TabType = 'voice-clone' | 'voice-enhance';

// Define voice model type
interface VoiceModel {
  id: string;
  name: string;
  type: 'vocalsync' | 'flowclone';
  qualityScore: number;
  createdAt: string;
}

const ActualRetunePage: React.FC = () => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<TabType>('voice-clone');

  // State for voice model
  const [voiceModel, setVoiceModel] = useState<VoiceModel | null>(null);
  const [voiceModelScore, setVoiceModelScore] = useState<number | null>(null);

  // Handle voice model creation
  const handleVoiceModelCreated = (model: any, score: number) => {
    setVoiceModel(model);
    setVoiceModelScore(score);

    // Automatically switch to voice enhancement tab when model is created
    setActiveTab('voice-enhance');
  };

  // Function to render the appropriate content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'voice-clone':
        return (
          <div className="grid-area-main overflow-y-auto">
            <VoiceCloner onVoiceModelCreated={handleVoiceModelCreated} />
          </div>
        );
      case 'voice-enhance':
        return (
          <div className="grid-area-main overflow-y-auto">
            {voiceModel ? (
              <VoiceEnhancer
                voiceModel={voiceModel}
                voiceModelScore={voiceModelScore}
              />
            ) : (
              <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
                <h2 className="text-2xl font-semibold mb-4">Voice Enhancement</h2>
                <p className="text-gray-300 mb-6">
                  Please create a voice model first using the FlowClone tab before enhancing your voice.
                </p>
                <button
                  onClick={() => setActiveTab('voice-clone')}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-medium transition-colors"
                >
                  Go to Voice Cloning
                </button>
              </div>
            )}
          </div>
        );
      default:
        return (
          <div className="grid-area-main overflow-y-auto">
            <VoiceCloner onVoiceModelCreated={handleVoiceModelCreated} />
          </div>
        );
    }
  };

  return (
    <MainAppLayout>
      <div className="holy-grail-layout">
        {/* Left Sidebar - Controls */}
        <div className="grid-area-left p-4 border-r border-white/10">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-4">
              Retune
            </h1>
            <p className="text-sm text-white/60 mb-6">
              Professional voice cloning and enhancement for music production
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex flex-col space-y-2 mb-6">
            <TabButton
              isActive={activeTab === 'voice-clone'}
              onClick={() => setActiveTab('voice-clone')}
              label="FlowClone"
              description="Voice Cloning"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              }
            />
            <TabButton
              isActive={activeTab === 'voice-enhance'}
              onClick={() => setActiveTab('voice-enhance')}
              label="VocalSynth"
              description="Voice Enhancement"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              }
            />
          </div>

          {/* Quick Stats */}
          {voiceModel && (
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 mb-6 border border-white/10">
              <h3 className="text-sm font-medium text-white/80 mb-2">Voice Model</h3>
              <div className="flex items-center mb-2">
                <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-white">{voiceModel.name}</p>
                  <p className="text-xs text-white/60">{voiceModel.type === 'vocalsync' ? 'VocalSynth™' : 'FlowClone™'}</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-xs text-white/60 mb-1">
                <span>Quality Score</span>
                <span className={`font-medium ${voiceModelScore && voiceModelScore >= 85 ? 'text-green-400' :
                  voiceModelScore && voiceModelScore >= 70 ? 'text-yellow-400' :
                    'text-red-400'
                  }`}>
                  {voiceModelScore}%
                </span>
              </div>
              <div className="h-1.5 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className={`h-full ${voiceModelScore && voiceModelScore >= 85 ? 'bg-green-500' :
                    voiceModelScore && voiceModelScore >= 70 ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}
                  style={{ width: `${voiceModelScore}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Pro Tips */}
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <h3 className="text-sm font-medium text-white/80 mb-2">Pro Tips</h3>
            <ul className="text-xs text-white/60 space-y-2">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span>Record in a quiet environment with minimal background noise</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span>Complete all sample phrases for best quality</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span>Aim for a Clone Quality Score of 85 or higher</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span>Use enhancement presets for specific use cases</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Main Content Area */}
        {renderContent()}

        {/* Right Sidebar - Project Integration */}
        <div className="grid-area-right p-4 border-l border-white/10">
          <h2 className="text-lg font-medium text-white mb-4">Project Integration</h2>

          {voiceModel ? (
            <div className="space-y-4">
              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-2">Save to Project</h3>
                <p className="text-xs text-white/60 mb-3">
                  Save your voice model to use in other features
                </p>
                <button className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-500 text-white text-sm font-medium rounded transition-colors">
                  Save Voice Model
                </button>
              </div>

              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-2">Export Options</h3>
                <div className="space-y-2">
                  <button className="w-full py-2 px-4 bg-black/50 hover:bg-black/70 text-white text-sm font-medium rounded border border-white/10 transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Export as WAV
                  </button>
                  <button className="w-full py-2 px-4 bg-black/50 hover:bg-black/70 text-white text-sm font-medium rounded border border-white/10 transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Export as MP3
                  </button>
                </div>
              </div>

              <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h3 className="text-sm font-medium text-white/80 mb-2">Connect With</h3>
                <div className="grid grid-cols-2 gap-2">
                  <button className="py-2 px-3 bg-black/50 hover:bg-black/70 text-white text-xs font-medium rounded border border-white/10 transition-colors">
                    Create Music
                  </button>
                  <button className="py-2 px-3 bg-black/50 hover:bg-black/70 text-white text-xs font-medium rounded border border-white/10 transition-colors">
                    Rhythm Studio
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/10 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-white/30 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
              <p className="text-sm text-white/60 mb-4">
                Create a voice model to enable project integration options
              </p>
              <button
                onClick={() => setActiveTab('voice-clone')}
                className="py-2 px-4 bg-blue-600 hover:bg-blue-500 text-white text-sm font-medium rounded transition-colors"
              >
                Start Voice Cloning
              </button>
            </div>
          )}
        </div>
      </div>
    </MainAppLayout>
  );
};

// Tab Button Component
interface TabButtonProps {
  isActive: boolean;
  onClick: () => void;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

const TabButton: React.FC<TabButtonProps> = ({
  isActive,
  onClick,
  label,
  description,
  icon
}) => (
  <button
    onClick={onClick}
    className={`flex items-center p-3 rounded-lg text-sm font-medium transition-all duration-200 w-full ${isActive
      ? 'bg-white/10 text-white border border-white/20'
      : 'text-white/60 hover:bg-white/5 hover:text-white/80 border border-transparent'
      }`}
  >
    {icon && <span className="mr-3">{icon}</span>}
    <div className="text-left">
      <div className="font-medium">{label}</div>
      {description && <div className="text-xs opacity-70">{description}</div>}
    </div>
  </button>
);









export default ActualRetunePage;
