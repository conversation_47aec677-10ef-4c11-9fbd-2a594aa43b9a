/* Styles for the recording interface */

.recording-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.recording-header {
  text-align: center;
  margin-bottom: 2rem;
}

.recording-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.recording-header p {
  color: #666;
  font-size: 1rem;
}

.recording-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  width: 100%;
}

.recording-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.record-button {
  background-color: #ff4d4f;
  color: white;
}

.record-button:hover {
  background-color: #ff7875;
}

.record-button.recording {
  animation: pulse 1.5s infinite;
}

.stop-button {
  background-color: #1890ff;
  color: white;
}

.stop-button:hover {
  background-color: #40a9ff;
}

.play-button {
  background-color: #52c41a;
  color: white;
}

.play-button:hover {
  background-color: #73d13d;
}

.recording-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: #666;
}

.recording-timer {
  font-family: monospace;
  font-size: 1.2rem;
  margin-left: 0.5rem;
}

.recording-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.action-button {
  padding: 0.5rem 1rem;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  color: #333;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.recording-guide {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #e6f7ff;
  border-left: 4px solid #1890ff;
  border-radius: 4px;
}

.recording-guide h3 {
  margin-top: 0;
  color: #1890ff;
}

.recording-guide ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.5rem;
}

.recording-guide li {
  margin-bottom: 0.5rem;
}

/* Animation for recording button */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .recording-container {
    padding: 1rem;
  }
  
  .recording-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .recording-button {
    width: 100%;
    max-width: 200px;
  }
}
