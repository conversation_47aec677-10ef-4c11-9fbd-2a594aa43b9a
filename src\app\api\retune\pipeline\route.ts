import { NextResponse } from 'next/server';
import { processAudioThroughPipeline, getPipelinePreset, PipelineOptions } from '@/utils/retunePipeline';
import { EmotionType } from '@/utils/emotionDetection';

/**
 * API route for processing audio through the Retune pipeline
 * 
 * POST /api/retune/pipeline
 * - Accepts audio file and pipeline options
 * - Returns processed audio
 * 
 * GET /api/retune/pipeline/presets
 * - Returns available pipeline presets
 */

export async function GET(request: Request) {
  try {
    // Get the path from the URL
    const { pathname } = new URL(request.url);
    
    // Handle presets endpoint
    if (pathname.endsWith('/presets')) {
      const presets = [
        {
          id: 'vocal_clarity',
          name: 'Vocal Clarity',
          description: 'Enhances vocal clarity and reduces noise',
          steps: ['auphonic']
        },
        {
          id: 'confident_voice',
          name: 'Confident Voice',
          description: 'Enhances vocal clarity and adds confidence to the voice',
          steps: ['auphonic', 'imentiv']
        },
        {
          id: 'energetic_performance',
          name: 'Energetic Performance',
          description: 'Creates an energetic and dynamic vocal performance',
          steps: ['auphonic', 'imentiv']
        },
        {
          id: 'emotional_ballad',
          name: 'Emotional Ballad',
          description: 'Enhances emotional qualities for ballads and slow songs',
          steps: ['auphonic', 'imentiv']
        },
        {
          id: 'podcast_voice',
          name: 'Podcast Voice',
          description: 'Optimizes voice for podcasts and spoken word content',
          steps: ['auphonic', 'imentiv']
        }
      ];
      
      return NextResponse.json(presets);
    }
    
    return NextResponse.json({ error: 'Invalid endpoint' }, { status: 404 });
  } catch (error) {
    console.error('Error processing pipeline request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    // Parse the multipart form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const presetId = formData.get('preset') as string;
    const optionsJson = formData.get('options') as string;
    
    if (!audioFile) {
      return NextResponse.json({ error: 'Audio file is required' }, { status: 400 });
    }
    
    // Get pipeline options
    let options: PipelineOptions;
    
    if (presetId) {
      // Use preset
      options = getPipelinePreset(presetId);
    } else if (optionsJson) {
      // Parse custom options
      try {
        options = JSON.parse(optionsJson);
      } catch (e) {
        return NextResponse.json({ error: 'Invalid options format' }, { status: 400 });
      }
    } else {
      // Use default options
      options = getPipelinePreset('default');
    }
    
    // Process the audio through the pipeline
    const result = await processAudioThroughPipeline(audioFile, options);
    
    if (!result.success) {
      return NextResponse.json({ 
        error: result.error,
        steps: result.steps
      }, { status: 500 });
    }
    
    if (!result.audioBlob) {
      return NextResponse.json({ error: 'No audio produced' }, { status: 500 });
    }
    
    // Return the processed audio
    return new NextResponse(result.audioBlob, {
      headers: {
        'Content-Type': result.audioBlob.type || 'audio/wav',
        'Content-Disposition': 'attachment; filename="retune_processed.wav"'
      }
    });
  } catch (error) {
    console.error('Error processing audio through pipeline:', error);
    return NextResponse.json(
      { error: 'Failed to process audio' },
      { status: 500 }
    );
  }
}
