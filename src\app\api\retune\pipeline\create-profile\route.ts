import { elevenlabsAPI, resembleAPI, supabaseService, VoiceSample } from '@/services/api';
import { NextRequest, NextResponse } from 'next/server';

/**
 * API route for creating a voice profile
 *
 * This route handles the creation of a voice profile with the specified provider
 * and parameters.
 *
 * @param req - The request object
 * @returns A response with the created voice profile
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the form data
    const formData = await req.formData();

    // Extract profile data
    const name = formData.get('name') as string;
    const provider = formData.get('provider') as 'elevenlabs' | 'resemble' | 'voicemod';
    const confidence = parseInt(formData.get('confidence') as string);
    const vocalRange = parseInt(formData.get('vocalRange') as string);
    const emotionalExpression = parseInt(formData.get('emotionalExpression') as string);
    const clarity = parseInt(formData.get('clarity') as string);
    const baseModel = formData.get('baseModel') as string | null;
    const useDemo = formData.get('useDemo') === 'true';

    // Validate required fields
    if (!name || !provider) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Extract audio samples
    const samples: VoiceSample[] = [];
    let sampleIndex = 0;

    while (formData.has(`sample_${sampleIndex}`)) {
      const file = formData.get(`sample_${sampleIndex}`) as File;
      if (file) {
        samples.push({
          file,
          name: file.name,
          mimeType: file.type,
        });
      }
      sampleIndex++;
    }

    // Validate samples
    if (samples.length === 0) {
      return NextResponse.json(
        { error: 'At least one audio sample is required' },
        { status: 400 }
      );
    }

    console.log(`Creating voice profile "${name}" with provider "${provider}" and ${samples.length} samples`);

    // If useDemo is true, skip the API calls and use mock data
    if (useDemo) {
      console.log('Using demo mode - skipping API calls');

      // Create a mock profile
      const mockProfile = {
        id: `profile_${Date.now()}`,
        name,
        provider,
        provider_voice_id: `mock_${provider}_${Date.now()}`,
        confidence,
        vocal_range: vocalRange,
        emotional_expression: emotionalExpression,
        clarity,
        base_model: baseModel,
        quality_score: calculateQualityScore(
          samples.length,
          confidence,
          vocalRange,
          emotionalExpression,
          clarity
        ),
        is_enhanced: false,
        created_at: new Date().toISOString(),
        samples: samples.map((_, i) => `https://example.com/mock_sample_${i}.wav`)
      };

      return NextResponse.json({ profile: mockProfile });
    }

    try {
      // Get the current user
      const user = await supabaseService.getCurrentUser();

      // Create provider-specific voice profile
      let providerVoiceId = '';

      switch (provider) {
        case 'elevenlabs':
          console.log('Using ElevenLabs for voice cloning');
          try {
            // Create voice with ElevenLabs
            const elevenlabsVoice = await elevenlabsAPI.createVoice(
              name,
              samples,
              `Voice profile created by ${user?.email || 'user'}`
            );
            providerVoiceId = elevenlabsVoice.voice_id;
          } catch (elevenlabsError) {
            console.error('ElevenLabs API error:', elevenlabsError);
            // Generate a mock voice ID for development
            providerVoiceId = `elevenlabs_mock_${Date.now()}`;
          }
          break;

        case 'resemble':
          console.log('Using Resemble for voice cloning');
          try {
            // Create voice with Resemble
            const resembleVoice = await resembleAPI.createVoice(
              name,
              `Voice profile created by ${user?.email || 'user'}`
            );
            providerVoiceId = resembleVoice.uuid;

            // Upload samples to Resemble
            for (const sample of samples) {
              await resembleAPI.uploadSample(providerVoiceId, sample);
            }
          } catch (resembleError) {
            console.error('Resemble API error:', resembleError);
            // Generate a mock voice ID for development
            providerVoiceId = `resemble_mock_${Date.now()}`;
          }
          break;

        case 'voicemod':
          console.log('Using Voicemod for voice transformation');
          // Voicemod doesn't have a direct voice cloning API, so we'll store the samples
          // and use them for transformation later
          providerVoiceId = `voicemod_${Date.now()}`;
          break;

        default:
          return NextResponse.json(
            { error: `Unsupported provider: ${provider}` },
            { status: 400 }
          );
      }

      // Calculate quality score based on samples and parameters
      const qualityScore = calculateQualityScore(
        samples.length,
        confidence,
        vocalRange,
        emotionalExpression,
        clarity
      );

      // Upload samples to Supabase storage
      const samplePaths: string[] = [];

      for (const sample of samples) {
        try {
          // Generate a unique file path
          const fileName = `${Date.now()}_${sample.name}`;
          const filePath = `voice_samples/${fileName}`;

          // Upload to Supabase storage
          const { error: storageError } = await supabaseService.getClient().storage
            .from('audio')
            .upload(filePath, sample.file, {
              contentType: sample.mimeType,
              cacheControl: '3600'
            });

          if (storageError) {
            console.error('Storage error:', storageError);
            continue;
          }

          // Get public URL
          const { data: urlData } = supabaseService.getClient().storage
            .from('audio')
            .getPublicUrl(filePath);

          samplePaths.push(urlData.publicUrl);
        } catch (storageError) {
          console.error('Error uploading sample:', storageError);
        }
      }

      // Create voice profile in Supabase
      let profileData;
      try {
        const { data, error: profileError } = await supabaseService.getClient()
          .from('voice_profiles')
          .insert({
            user_id: user?.id || '00000000-0000-0000-0000-000000000000', // Use a default UUID if no user
            name,
            provider,
            provider_voice_id: providerVoiceId,
            confidence,
            vocal_range: vocalRange,
            emotional_expression: emotionalExpression,
            clarity,
            base_model: baseModel,
            quality_score: qualityScore,
            is_enhanced: false
          })
          .select()
          .single();

        if (profileError) {
          console.error('Error creating profile in database:', profileError);

          // Check if the error is related to missing tables
          if (profileError.message && profileError.message.includes('relation "voice_profiles" does not exist')) {
            console.error('The voice_profiles table does not exist in Supabase. Please create it using the SQL statements.');
            throw new Error('Database tables not set up correctly. Please contact the administrator.');
          }

          throw profileError;
        }

        profileData = data;

        // Add samples to database
        for (let i = 0; i < samplePaths.length; i++) {
          const sample = samples[i];
          const path = samplePaths[i];

          try {
            await supabaseService.getClient()
              .from('voice_samples')
              .insert({
                profile_id: profileData.id,
                storage_path: path,
                original_filename: sample.name,
                duration_seconds: 0 // We would need to calculate this
              });
          } catch (sampleError) {
            console.error('Error adding sample to database:', sampleError);
            // Continue with other samples even if one fails
          }
        }

        // Return the created profile with sample paths
        const voiceProfile = {
          ...profileData,
          samples: samplePaths
        };

        return NextResponse.json({ profile: voiceProfile });
      } catch (dbError) {
        console.error('Database operation failed:', dbError);
        throw dbError;
      }
    } catch (apiError) {
      console.error('API Error:', apiError);

      // For development, create a mock profile
      console.log('Falling back to mock profile');

      const mockProfile = {
        id: `profile_${Date.now()}`,
        name,
        provider,
        provider_voice_id: `mock_${provider}_${Date.now()}`,
        confidence,
        vocal_range: vocalRange,
        emotional_expression: emotionalExpression,
        clarity,
        base_model: baseModel,
        quality_score: calculateQualityScore(
          samples.length,
          confidence,
          vocalRange,
          emotionalExpression,
          clarity
        ),
        is_enhanced: false,
        created_at: new Date().toISOString(),
        samples: samples.map((_, i) => `https://example.com/mock_sample_${i}.wav`)
      };

      return NextResponse.json({ profile: mockProfile });
    }
  } catch (error) {
    console.error('Error creating voice profile:', error);
    return NextResponse.json(
      { error: 'Failed to create voice profile' },
      { status: 500 }
    );
  }
}

/**
 * Calculate quality score based on samples and parameters
 */
function calculateQualityScore(
  sampleCount: number,
  confidence: number,
  vocalRange: number,
  emotionalExpression: number,
  clarity: number
): number {
  // For demo mode, ensure we get a good score
  if (sampleCount === 0) {
    // If no samples (shouldn't happen), return a default score
    return 75;
  }

  // Base score from sample count (0-50 points)
  let score = Math.min(50, 30 + sampleCount * 10);

  // Add points from parameters (0-50 points)
  score += (confidence + vocalRange + emotionalExpression + clarity) / 4 * 0.5;

  // Ensure score is between 0-100
  return Math.min(100, Math.max(0, Math.round(score)));
}
