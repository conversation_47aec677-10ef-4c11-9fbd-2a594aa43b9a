'use client';

import { useContextPanel } from '@/contexts/ContextPanelContext';
import React, { useEffect, useState } from 'react';
import AIWritersList from '../AIWritersList';

import BackgroundPreloader from '../BackgroundPreloader';
import Footer from './Footer';
import SunoSidebar from './SunoSidebar';
import TopBar from './TopBar';

interface MainAppLayoutProps {
    children: React.ReactNode;
    contextPanelContent?: React.ReactNode;
}

const MainAppLayout: React.FC<MainAppLayoutProps> = ({ children, contextPanelContent }) => {
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
    const [contextCollapsed, setContextCollapsed] = useState(true); // Default to collapsed
    const { contextContent, isContextPanelOpen, setContextPanelOpen, currentPage } = useContextPanel();

    // Use a ref to track initialization
    const initializedRef = React.useRef(false);

    // One-time initialization to sync states
    useEffect(() => {
        if (!initializedRef.current) {
            initializedRef.current = true;

            // Initial sync of context panel state
            if (isContextPanelOpen !== !contextCollapsed) {
                setContextPanelOpen(!contextCollapsed);
            }
        }
    }, [isContextPanelOpen, contextCollapsed, setContextPanelOpen]);

    const toggleSidebar = () => {
        setSidebarCollapsed(!sidebarCollapsed);
    };

    const toggleContext = () => {
        const newCollapsedState = !contextCollapsed;
        setContextCollapsed(newCollapsedState);
        setContextPanelOpen(!newCollapsedState);
    };

    // We're now using currentPage from the context for all page-specific styling

    return (
        <div
            id="app-root"
            className={`min-h-screen ${sidebarCollapsed ? 'sidebar-collapsed' : ''} ${contextCollapsed ? 'context-collapsed' : ''} bg-black/20`}
        >
            {/* Top Bar */}
            <header id="app-top-bar" className="glass-topbar flex items-center justify-between px-4 md:px-6 h-[60px]" style={{ gridArea: 'header' }}>
                <TopBar />
            </header>

            {/* Left Sidebar */}
            <div className="sidebar-container">
                <SunoSidebar
                    isCollapsed={sidebarCollapsed}
                    onToggleCollapse={toggleSidebar}
                />
            </div>

            {/* Main Content Area */}
            <main id="app-main" className="overflow-y-auto"> {/* Allow main content to scroll */}
                {children}
            </main>

            {/* Right Context Panel */}
            <div className="context-container">
                <aside id="app-context" className="glass-panel backdrop-blur-md border-l border-white/10 overflow-y-auto shadow-lg">
                    {/* Context-aware panel content */}
                    <div className="p-5 text-white/90">
                        <h3 className="text-lg font-semibold border-b border-white/20 pb-2 mb-4">
                            {contextContent?.title || 'Context Panel'}
                        </h3>

                        {/* Display custom context panel content if provided */}
                        {contextContent ? (
                            <>{contextContent.content}</>
                        ) : contextPanelContent ? (
                            <>{contextPanelContent}</>
                        ) : (
                            <>
                                {/* Display content based on current page */}
                                {currentPage === 'rhythm-studio' && (
                                    <div>
                                        <h4 className="text-md font-medium mb-2">Rhythm Studio Tools</h4>
                                        <p className="text-sm text-white/70 mb-3">Align your lyrics with beats or analyze rhythm patterns</p>
                                        <ul className="text-sm space-y-2">
                                            <li className="p-2 theme-nav-button rounded">Beat Detection</li>
                                            <li className="p-2 theme-nav-button rounded">Lyrics Alignment</li>
                                            <li className="p-2 theme-nav-button rounded">Rhythm Analysis</li>
                                        </ul>
                                    </div>
                                )}

                                {currentPage === 'voice-studio' && (
                                    <div>
                                        <h4 className="text-md font-medium mb-2">Voice Models</h4>
                                        <p className="text-sm text-white/70 mb-3">Clone voices or enhance vocal performances</p>
                                        <ul className="text-sm space-y-2">
                                            <li className="p-2 theme-nav-button rounded">APIT Clone (Voice Cloning)</li>
                                            <li className="p-2 theme-nav-button rounded">APIT Enhance (Voice Enhancement)</li>
                                        </ul>
                                    </div>
                                )}

                                {currentPage === 'create-music' && (
                                    <div>
                                        <h4 className="text-md font-medium mb-2">Music Generation</h4>
                                        <p className="text-sm text-white/70 mb-3">Create original music in various styles</p>
                                        <ul className="text-sm space-y-2">
                                            <li className="p-2 theme-nav-button rounded">Instrumental Tracks</li>
                                            <li className="p-2 theme-nav-button rounded">Full Song Production</li>
                                            <li className="p-2 theme-nav-button rounded">Beat Creation</li>
                                        </ul>
                                    </div>
                                )}

                                {currentPage === 'stem-splitter' && (
                                    <div>
                                        <h4 className="text-md font-medium mb-2">Audio Separation</h4>
                                        <p className="text-sm text-white/70 mb-3">Split audio into separate stems</p>
                                        <ul className="text-sm space-y-2">
                                            <li className="p-2 theme-nav-button rounded">Vocals Extraction</li>
                                            <li className="p-2 theme-nav-button rounded">Instrumental Extraction</li>
                                            <li className="p-2 theme-nav-button rounded">Multi-stem Separation</li>
                                        </ul>
                                    </div>
                                )}

                                {/* Default content for home or other pages */}
                                {(currentPage === 'home' ||
                                    !['create-music', 'rhythm-studio', 'voice-studio', 'stem-splitter'].includes(currentPage)) && (
                                        <div>
                                            <h4 className="text-md font-medium mb-2">AI Writers</h4>
                                            <p className="text-sm text-white/70 mb-3">Our AI writers can help create lyrics in various styles</p>
                                            <AIWritersList />
                                        </div>
                                    )}
                            </>
                        )}
                    </div>
                </aside>
                <button
                    onClick={toggleContext}
                    className="context-toggle theme-nav-button p-1.5 rounded-full absolute top-[70px] right-[290px] transform translate-x-1/2 z-50 transition-all shadow-md"
                    aria-label={contextCollapsed ? "Expand context panel" : "Collapse context panel"}
                    style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={contextCollapsed ? "M15 19l-7-7 7-7" : "M9 5l7 7-7 7"}
                        />
                    </svg>
                </button>
            </div>

            {/* Footer */}
            <footer id="app-footer" className="glass-panel">
                <Footer />
            </footer>

            {/* Background image preloader - invisible but preloads all theme backgrounds */}
            <BackgroundPreloader />
        </div>
    );
};

export default MainAppLayout;
export { MainAppLayout };