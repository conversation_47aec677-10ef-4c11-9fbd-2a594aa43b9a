import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { cookies } from 'next/headers';
import { trackUsage } from '@/lib/usage-tracking';
import { getCreditCost, getServiceType } from '@/lib/credit-config';

// Function to get user credits
function getUserCredits(userId: string): any {
  try {
    const creditsDir = path.join(process.cwd(), 'data', 'credits');

    // Create directory if it doesn't exist
    if (!fs.existsSync(creditsDir)) {
      fs.mkdirSync(creditsDir, { recursive: true });
    }

    const userCreditsPath = path.join(creditsDir, `${userId}.json`);

    // If user doesn't have a credits file, create one with default credits
    if (!fs.existsSync(userCreditsPath)) {
      const defaultData = {
        credits: 500, // Default starting credits for new users (increased from 50)
        tier: 'free',
        history: [],
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(userCreditsPath, JSON.stringify(defaultData));
      return defaultData;
    }

    // Read user credits
    return JSON.parse(fs.readFileSync(userCreditsPath, 'utf-8'));
  } catch (error) {
    console.error('Error getting user credits:', error);
    // Return default data if there's an error
    return {
      credits: 500, // Default credits (increased from 50)
      tier: 'free',
      history: [],
      lastUpdated: new Date().toISOString()
    };
  }
}

// Function to get user ID from auth token
function getUserIdFromToken(token: string): string | null {
  try {
    // In a real implementation, this would verify the JWT token
    // For now, we'll just extract the user ID from the token if it exists
    if (!token) return null;

    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) return null;

    const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
    return payload.userId || null;
  } catch (error) {
    console.error('Error extracting user ID from token:', error);
    return null;
  }
}

// Function to deduct credits
export async function POST(request: Request) {
  try {
    // Get request body
    const body = await request.json();
    const { amount, reason, userId: providedUserId } = body;

    // Get the auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth_token')?.value;

    // Try to get user ID from token, fall back to provided ID or demo-user
    const tokenUserId = authToken ? getUserIdFromToken(authToken) : null;
    const userId = tokenUserId || providedUserId || 'demo-user';

    if (!amount || amount <= 0) {
      return NextResponse.json({ error: 'Invalid amount' }, { status: 400 });
    }

    // Get user credits
    const userData = getUserCredits(userId);

    // Free mode - don't deduct credits but still track usage
    console.log(`FREE MODE: Would have used ${amount} credits for ${reason || 'usage'}`);

    // Get the credit cost and service type from the config
    const feature = reason || 'unknown-feature';
    const creditCost = getCreditCost(feature);
    const serviceType = getServiceType(feature);

    // Track the usage for analytics with credit value
    await trackUsage(feature, userId, { amount }, true, creditCost, serviceType);

    // Create a transaction record for tracking
    const transaction = {
      id: `free-txn-${Date.now()}`,
      type: 'free-usage',
      amount: 0, // No actual deduction
      reason: `FREE: ${reason || 'Credit usage'}`,
      timestamp: new Date().toISOString()
    };

    // Add transaction to history (without deducting credits)
    userData.history = [...(userData.history || []), transaction];
    userData.lastUpdated = new Date().toISOString();

    // Save updated data
    const creditsDir = path.join(process.cwd(), 'data', 'credits');
    const userCreditsPath = path.join(creditsDir, `${userId}.json`);
    fs.writeFileSync(userCreditsPath, JSON.stringify(userData));

    // Return success with unchanged balance
    return NextResponse.json({
      success: true,
      currentCredits: userData.credits,
      tier: userData.tier,
      freeMode: true,
      message: 'All features are currently free',
      transaction
    });
  } catch (error) {
    console.error('Error deducting credits:', error);

    // Handle different types of errors
    const errorMessage = error instanceof Error ? error.message : 'Failed to deduct credits';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// GET endpoint to retrieve user credits
export async function GET(request: Request) {
  try {
    // Get user ID from query parameter
    const url = new URL(request.url);
    const providedUserId = url.searchParams.get('userId');

    // Get the auth token from cookies
    const cookieStore = await cookies();
    const authToken = cookieStore.get('auth_token')?.value;

    // Try to get user ID from token, fall back to provided ID or demo-user
    const tokenUserId = authToken ? getUserIdFromToken(authToken) : null;
    const userId = tokenUserId || providedUserId || 'demo-user';

    // Get user credits
    const userData = getUserCredits(userId);

    // Format response
    const creditsData = {
      data: {
        credits: userData.credits,
        tier: userData.tier,
        lastUpdated: userData.lastUpdated,
        // Include only the last 5 transactions in the response
        recentHistory: (userData.history || []).slice(-5).reverse()
      }
    };

    return NextResponse.json(creditsData);
  } catch (error) {
    console.error('Error fetching credits:', error);

    // Handle different types of errors
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch credits';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
