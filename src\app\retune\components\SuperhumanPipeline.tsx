'use client';

import { ThemeButton, ThemeCard, ThemeHeading } from '@/components/ui';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'react-hot-toast';

// Define types for voice profile
interface VoiceProfile {
  id: string;
  name: string;
  confidence: number;
  vocalRange: number;
  emotionalExpression: number;
  clarity: number;
  baseModel: string | null;
  createdAt: string;
  samples: string[];
  qualityScore: number;
}

// Define types for base models
interface BaseModel {
  id: string;
  name: string;
  type: 'singer' | 'rapper' | 'speaker';
  description: string;
}

interface SuperhumanPipelineProps {
  onProfileCreated?: (profile: VoiceProfile) => void;
  onProcessingComplete?: (audioUrl: string) => void;
}

const SuperhumanPipeline: React.FC<SuperhumanPipelineProps> = ({
  onProfileCreated,
  onProcessingComplete
}) => {
  // Current step state
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1);

  // Step 1: Voice Input & Profile Creation states
  const [audioSamples, setAudioSamples] = useState<File[]>([]);
  const [sampleUrls, setSampleUrls] = useState<string[]>([]);
  const [profileName, setProfileName] = useState('My Superhuman Voice');
  const [baseModels, setBaseModels] = useState<BaseModel[]>([]);
  const [selectedBaseModel, setSelectedBaseModel] = useState<string | null>(null);

  // Voice parameter sliders
  const [confidenceLevel, setConfidenceLevel] = useState(50);
  const [vocalRange, setVocalRange] = useState(50);
  const [emotionalExpression, setEmotionalExpression] = useState(50);
  const [clarityLevel, setClarityLevel] = useState(50);

  // Step 2: Voice Enhancement Engine states
  const [voiceProfile, setVoiceProfile] = useState<VoiceProfile | null>(null);
  const [enhancementModel, setEnhancementModel] = useState<'rvc' | 'bark' | 'autovc'>('rvc');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [referenceVoices, setReferenceVoices] = useState<{ id: string, name: string, percentage: number }[]>([]);
  const [isEnhancing, setIsEnhancing] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [enhancedProfileUrl, setEnhancedProfileUrl] = useState<string | null>(null);

  // Step 3: Voice Application Interface states
  const [applicationMode, setApplicationMode] = useState<'tts' | 'enhance' | 'collaborate'>('tts');
  const [inputText, setInputText] = useState('');
  const [inputAudio, setInputAudio] = useState<File | null>(null);
  const [inputAudioUrl, setInputAudioUrl] = useState<string | null>(null);
  const [outputAudioUrl, setOutputAudioUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Refs
  const audioInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Recording state
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);

  // Fetch base models on mount
  useEffect(() => {
    const fetchBaseModels = async () => {
      try {
        // Simulate API call - in production, this would be a real API call
        // const response = await fetch('/api/retune/base-models');
        // const data = await response.json();

        // Simulated data
        const mockData: BaseModel[] = [
          { id: 'drake', name: 'Drake', type: 'rapper', description: 'Smooth, melodic rap style' },
          { id: 'rema', name: 'Rema', type: 'singer', description: 'Afrobeats with distinctive vocals' },
          { id: 'adele', name: 'Adele', type: 'singer', description: 'Powerful, soulful vocals' },
          { id: 'kendrick', name: 'Kendrick Lamar', type: 'rapper', description: 'Dynamic, versatile flow' },
          { id: 'neutral', name: 'Neutral Voice', type: 'speaker', description: 'Clean, neutral speaking voice' }
        ];

        setBaseModels(mockData);
      } catch (error) {
        console.error('Error fetching base models:', error);
        toast.error('Failed to fetch voice models');
      }
    };

    fetchBaseModels();
  }, []);

  // Handle audio sample upload
  const handleSampleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const newSamples = [...audioSamples];
      const newUrls = [...sampleUrls];

      Array.from(files).forEach(file => {
        newSamples.push(file);
        newUrls.push(URL.createObjectURL(file));
      });

      setAudioSamples(newSamples);
      setSampleUrls(newUrls);
    }
  };

  // Start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], `recording_${Date.now()}.wav`, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Add to samples
        setAudioSamples([...audioSamples, audioFile]);
        setSampleUrls([...sampleUrls, audioUrl]);

        setIsRecording(false);
        setRecordingTime(0);

        toast.success('Recording saved!');
      };

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1);
      }, 1000);

      mediaRecorder.start();
      setIsRecording(true);
      toast.success('Recording started! Speak clearly into your microphone.');
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error('Could not access microphone. Please check permissions.');
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();

      // Stop all tracks in the stream
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  // Remove a sample
  const removeSample = (index: number) => {
    const newSamples = [...audioSamples];
    const newUrls = [...sampleUrls];

    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(newUrls[index]);

    newSamples.splice(index, 1);
    newUrls.splice(index, 1);

    setAudioSamples(newSamples);
    setSampleUrls(newUrls);
  };

  // Create voice profile
  const createVoiceProfile = async () => {
    if (audioSamples.length === 0) {
      toast.error('Please upload at least one voice sample');
      return;
    }

    try {
      setIsProcessing(true);
      const toastId = toast.loading('Creating voice profile...');

      // Show detailed status to help user understand the process
      setTimeout(() => {
        toast.loading('Processing audio samples...', { id: toastId });
      }, 1500);

      // Create FormData for API call - using only ElevenLabs
      const formData = new FormData();
      audioSamples.forEach((sample, index) => {
        formData.append(`sample_${index}`, sample);
      });
      formData.append('name', profileName);
      formData.append('provider', 'elevenlabs'); // We only use ElevenLabs
      formData.append('baseModel', selectedBaseModel || '');
      formData.append('confidence', confidenceLevel.toString());
      formData.append('vocalRange', vocalRange.toString());
      formData.append('emotionalExpression', emotionalExpression.toString());
      formData.append('clarity', clarityLevel.toString());

      // Always use demo mode to avoid API issues with Supabase RLS and ElevenLabs
      // This will bypass the actual API calls and use mock data
      formData.append('useDemo', 'true');

      try {
        // Update toast with more detailed status
        setTimeout(() => {
          toast.loading('Connecting to voice service...', { id: toastId });
        }, 3000);

        // Make API call with timeout and error handling
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const response = await fetch('/api/retune/pipeline/create-profile', {
            method: 'POST',
            body: formData,
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            try {
              const errorData = await response.json();
              throw new Error(errorData.error || 'Failed to create voice profile');
            } catch (jsonError) {
              console.error('Error parsing error response:', jsonError);
              throw new Error(`Failed to create voice profile: ${response.status} ${response.statusText}`);
            }
          }

          // Try to parse the response as JSON
          let responseData;
          try {
            responseData = await response.json();
          } catch (jsonError) {
            console.error('Error parsing success response:', jsonError);
            throw new Error('Failed to parse response from server');
          }

          // Set the voice profile from the response data
          setVoiceProfile(responseData.profile);
          toast.success('Voice profile created successfully!', { id: toastId });

          // Call callback if provided
          if (onProfileCreated) {
            onProfileCreated(responseData.profile);
          }

          // Move to next step
          setCurrentStep(2);

          return responseData; // Return the data for use outside the try block
        } catch (error: unknown) {
          // Handle fetch errors
          if (error instanceof Error && error.name === 'AbortError') {
            throw new Error('Request timed out. Please try again.');
          }
          throw error;
        }

        // The rest of the code will only execute if there's an error and we fall back to the mock profile
        // The successful API call path is handled inside the try block above
      } catch (error) {
        const apiError = error as Error;
        console.error('API Error:', apiError);

        // Show a more user-friendly error message
        toast.error(
          <div>
            <p>Creating profile with ElevenLabs API failed.</p>
            <p className="text-xs mt-1">Using demo mode instead.</p>
          </div>,
          { id: toastId, duration: 4000 }
        );

        // Show a follow-up message explaining the fallback
        setTimeout(() => {
          toast.success('Demo profile created! Continue to experience the full workflow.', {
            duration: 5000,
            icon: '🔍'
          });
        }, 1500);

        // Fallback to mock profile for development
        console.log('Using mock profile as fallback');
        const mockProfile: VoiceProfile = {
          id: `profile_${Date.now()}`,
          name: profileName,
          confidence: confidenceLevel,
          vocalRange: vocalRange,
          emotionalExpression: emotionalExpression,
          clarity: clarityLevel,
          baseModel: selectedBaseModel,
          createdAt: new Date().toISOString(),
          samples: sampleUrls,
          qualityScore: Math.floor(70 + Math.random() * 20) // Random score between 70-90
        };

        setVoiceProfile(mockProfile);

        // Call callback if provided
        if (onProfileCreated) {
          onProfileCreated(mockProfile);
        }

        // Move to next step
        setCurrentStep(2);
      }
    } catch (error) {
      console.error('Error creating voice profile:', error);
      toast.error('Failed to create voice profile');
    } finally {
      setIsProcessing(false);
    }
  };

  // Enhance voice profile
  const enhanceVoiceProfile = async () => {
    if (!voiceProfile) {
      toast.error('No voice profile to enhance');
      return;
    }

    try {
      setIsEnhancing(true);
      const toastId = toast.loading('Enhancing voice profile...');

      try {
        // Make API call
        const response = await fetch('/api/retune/pipeline/enhance-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            profileId: voiceProfile.id,
            enhancementModel,
            referenceVoices
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to enhance voice profile');
        }

        const data = await response.json();
        setVoiceProfile(data.profile);
        setEnhancedProfileUrl(data.profile.samples?.[0] || null);

        toast.success('Voice profile enhanced successfully!', { id: toastId });

        // Move to next step
        setCurrentStep(3);
      } catch (error) {
        const apiError = error as Error;
        console.error('API Error:', apiError);
        toast.error(`API Error: ${apiError.message}`, { id: toastId });

        // Fallback to mock enhancement for development
        console.log('Using mock enhancement as fallback');

        // Update profile with enhanced version
        const enhancedProfile = {
          ...voiceProfile,
          qualityScore: Math.min(99, voiceProfile.qualityScore + 10) // Increase score but cap at 99
        };

        setVoiceProfile(enhancedProfile);
        setEnhancedProfileUrl('https://example.com/enhanced-profile'); // Mock URL

        toast.success('Voice profile enhanced (mock)!', { id: toastId });

        // Move to next step
        setCurrentStep(3);
      }
    } catch (error) {
      console.error('Error enhancing voice profile:', error);
      toast.error('Failed to enhance voice profile');
    } finally {
      setIsEnhancing(false);
    }
  };

  // Process in application mode
  const processApplication = async () => {
    try {
      setIsProcessing(true);
      const toastId = toast.loading('Processing...');

      try {
        let response;

        switch (applicationMode) {
          case 'tts':
            // Text-to-speech mode
            response = await fetch('/api/retune/pipeline/text-to-speech', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                text: inputText,
                profileId: voiceProfile?.id,
                style: 'singing', // or 'rapping', 'spoken'
                emotion: 'neutral' // or other emotions
              })
            });
            break;

          case 'enhance':
            // Voice enhancement mode
            if (!inputAudio) {
              throw new Error('No audio file selected');
            }

            const formData = new FormData();
            formData.append('audio', inputAudio);
            formData.append('profileId', voiceProfile?.id || '');
            formData.append('pitchCorrection', 'true');
            formData.append('toneEnhancement', 'true');
            formData.append('dynamicTuning', 'true');
            formData.append('emotionalBoost', 'true');

            response = await fetch('/api/retune/pipeline/enhance-audio', {
              method: 'POST',
              body: formData
            });
            break;

          case 'collaborate':
            // Collaborative mode
            response = await fetch('/api/retune/pipeline/collaborate', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                text: inputText,
                primaryProfileId: voiceProfile?.id,
                collaboratorProfileIds: [], // Add selected collaborator IDs here
                style: 'singing' // or 'rapping', 'spoken'
              })
            });
            break;

          default:
            throw new Error(`Unsupported application mode: ${applicationMode}`);
        }

        if (!response.ok) {
          // Try to parse error message
          try {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Processing failed');
          } catch (jsonError) {
            // Improved error handling for JSON parsing errors
            console.error('Error parsing JSON response:', jsonError);

            // Check if the response is HTML (common in 500 errors)
            const contentType = response.headers.get('Content-Type');
            if (contentType && contentType.includes('text/html')) {
              console.error('Received HTML response instead of JSON');
              throw new Error(`Processing failed with status ${response.status}: Server returned HTML instead of JSON`);
            } else {
              // Use status code for the error message
              throw new Error(`Processing failed with status ${response.status}`);
            }
          }
        }

        // Handle response based on content type
        const contentType = response.headers.get('Content-Type');

        if (contentType && contentType.includes('application/json')) {
          // JSON response with URL
          const data = await response.json();
          setOutputAudioUrl(data.audioUrl);
        } else {
          // Direct audio response
          const audioBlob = await response.blob();
          const audioUrl = URL.createObjectURL(audioBlob);
          setOutputAudioUrl(audioUrl);
        }

        toast.success('Processing complete!', { id: toastId });

        // Call callback if provided
        if (onProcessingComplete && outputAudioUrl) {
          onProcessingComplete(outputAudioUrl);
        }
      } catch (error) {
        const apiError = error as Error;
        console.error('API Error:', apiError);
        toast.error(`API Error: ${apiError.message}`, { id: toastId });

        // Fallback to mock output for development
        console.log('Using mock output as fallback');

        // Create a mock output URL
        const mockOutputUrl = URL.createObjectURL(new Blob(['mock audio data'], { type: 'audio/wav' }));
        setOutputAudioUrl(mockOutputUrl);

        toast.success('Processing complete (mock)!', { id: toastId });

        // Call callback if provided
        if (onProcessingComplete) {
          onProcessingComplete(mockOutputUrl);
        }
      }
    } catch (error) {
      console.error('Error processing application:', error);
      toast.error('Failed to process application');
    } finally {
      setIsProcessing(false);
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Revoke all object URLs to prevent memory leaks
      sampleUrls.forEach(url => URL.revokeObjectURL(url));
      if (inputAudioUrl) URL.revokeObjectURL(inputAudioUrl);
      if (outputAudioUrl) URL.revokeObjectURL(outputAudioUrl);
    };
  }, [sampleUrls, inputAudioUrl, outputAudioUrl]);

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      default:
        return renderStep1();
    }
  };

  // Step 1: Voice Input & Profile Creation
  const renderStep1 = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4">Step 1: Voice Input & Profile Creation</ThemeHeading>
        <div className="retune-glass-container p-4 mb-6">
          <h4 className="text-sm font-medium retune-text-glow-cyan mb-2">How Retune Works</h4>
          <p className="text-sm text-white/70 mb-3">
            Retune is our voice cloning and enhancement studio powered by ElevenLabs:
          </p>
          <ol className="text-xs text-white/80 space-y-2 list-decimal pl-4 mb-3">
            <li><span className="font-medium text-[#00FFEE]">Create a voice profile</span> by uploading audio samples of your voice</li>
            <li><span className="font-medium text-[#00FFEE]">Clone your voice</span> using ElevenLabs&apos; advanced AI technology</li>
            <li><span className="font-medium text-[#00FFEE]">Apply custom effects</span> to enhance your voice with our proprietary audio processing</li>
          </ol>
          <p className="text-xs text-white/70 mb-3">
            Start by uploading at least one voice sample below. For best results, upload 3-5 samples.
          </p>

          <h4 className="text-sm font-medium retune-text-glow-cyan mb-2">Voice Cloning with ElevenLabs</h4>
          <p className="text-xs text-white/70 mb-2">
            We use ElevenLabs exclusively for voice cloning, providing:
          </p>
          <ul className="text-xs text-white/80 space-y-1 pl-4 mb-3 list-disc">
            <li>High-quality voice cloning with <span className="font-medium text-[#00FFEE]">natural-sounding results</span></li>
            <li>Custom <span className="font-medium text-[#00FFEE]">audio effects</span> built in-house for enhancement</li>
            <li>Seamless integration with our <span className="font-medium text-[#00FFEE]">music creation pipeline</span></li>
          </ul>

          <div className="bg-black/30 p-2 rounded-md">
            <p className="text-xs text-yellow-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Currently in demo mode. Using ElevenLabs for voice cloning with custom audio effects.</span>
            </p>
          </div>
        </div>
        <p className="text-sm text-white/70 mb-6">
          Upload voice samples and adjust parameters to create your voice profile.
        </p>

        {/* Profile Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Profile Name</label>
          <input
            type="text"
            value={profileName}
            onChange={(e) => setProfileName(e.target.value)}
            className="w-full bg-black/40 border border-[#00FFEE]/30 rounded-lg p-2.5 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300"
            placeholder="My Superhuman Voice"
          />
        </div>

        {/* Voice Sample Upload */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Voice Samples (30-60 seconds)</label>
          <input
            type="file"
            ref={audioInputRef}
            onChange={handleSampleUpload}
            accept="audio/*"
            className="hidden"
            multiple
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* File Upload */}
            <div className="retune-glass-container p-5">
              <div className="flex flex-col items-center justify-center cursor-pointer" onClick={() => audioInputRef.current?.click()}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[#00FFEE]/60 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.4))' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p className="mb-2 text-sm text-white/80">
                  <span className="font-medium retune-text-glow-cyan">Click to upload</span> files
                </p>
                <p className="text-xs text-white/60">
                  MP3, WAV, or AIFF (max. 10MB per file)
                </p>
              </div>
            </div>

            {/* Microphone Recording */}
            <div className="retune-glass-container p-5">
              {!isRecording ? (
                <div
                  className="flex flex-col items-center justify-center cursor-pointer"
                  onClick={startRecording}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[#00FFEE]/60 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.4))' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  <p className="mb-2 text-sm text-white/80">
                    <span className="font-medium retune-text-glow-cyan">Click to record</span> your voice
                  </p>
                  <p className="text-xs text-white/60">
                    Speak clearly for 30-60 seconds
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <div className="w-16 h-16 rounded-full bg-red-500/80 flex items-center justify-center mb-3 animate-pulse">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </div>
                  <p className="mb-2 text-sm text-white/80">
                    Recording... <span className="font-medium text-red-400">{recordingTime}s</span>
                  </p>
                  <button
                    onClick={stopRecording}
                    className="px-4 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm transition-colors"
                  >
                    Stop Recording
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Sample List */}
          {audioSamples.length > 0 && (
            <div className="space-y-3 mb-4">
              <p className="text-sm font-medium retune-text-glow-cyan">Uploaded Samples ({audioSamples.length})</p>
              {audioSamples.map((sample, index) => (
                <div key={index} className="retune-glass-container p-3 flex items-center justify-between">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE] mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                    </svg>
                    <div>
                      <p className="text-sm text-white">{sample.name}</p>
                      <p className="text-xs text-white/60">{(sample.size / (1024 * 1024)).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <audio controls src={sampleUrls[index]} className="h-8 w-32 mr-2"></audio>
                    <button
                      onClick={() => removeSample(index)}
                      className="p-1 text-red-400 hover:text-red-300"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Sample Quality Indicator */}
          {audioSamples.length > 0 && (
            <div className="mb-6">
              <div className="flex justify-between text-sm mb-1">
                <span className="text-white/80">Sample Quality</span>
                <span className={`font-medium ${audioSamples.length >= 3 ? 'text-green-400' :
                  audioSamples.length >= 1 ? 'text-yellow-400' :
                    'text-red-400'
                  }`}>
                  {audioSamples.length >= 3 ? 'Excellent' :
                    audioSamples.length >= 1 ? 'Good' :
                      'Poor'}
                </span>
              </div>
              <div className="bg-black/30 h-2 rounded-full overflow-hidden">
                <div
                  className={`h-full ${audioSamples.length >= 3 ? 'bg-gradient-to-r from-green-500/80 to-green-400' :
                    audioSamples.length >= 1 ? 'bg-gradient-to-r from-yellow-500/80 to-yellow-400' :
                      'bg-gradient-to-r from-red-500/80 to-red-400'
                    }`}
                  style={{
                    width: `${Math.min(100, audioSamples.length * 33)}%`,
                    boxShadow: audioSamples.length >= 3 ? '0 0 10px rgba(74, 222, 128, 0.7)' :
                      audioSamples.length >= 1 ? '0 0 10px rgba(250, 204, 21, 0.7)' :
                        '0 0 10px rgba(248, 113, 113, 0.7)'
                  }}
                ></div>
              </div>
              <p className="text-xs text-white/60 mt-1">
                {audioSamples.length < 3 ? `Add ${3 - audioSamples.length} more sample(s) for best results` : 'Optimal sample count reached'}
              </p>
            </div>
          )}
        </div>

        {/* Base Model Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Base Model (Optional)</label>
          <p className="text-xs text-white/60 mb-3">Start from an existing voice to enhance your results</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {baseModels.map(model => (
              <div
                key={model.id}
                onClick={() => setSelectedBaseModel(model.id)}
                className={`retune-glass-container p-3 cursor-pointer transition-all duration-300 ${selectedBaseModel === model.id
                  ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                  : 'border-white/10 hover:border-[#00FFEE]/30'
                  }`}
              >
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${selectedBaseModel === model.id
                    ? 'bg-[#00FFEE]/20 text-[#00FFEE]'
                    : 'bg-black/40 text-white/60'
                    }`}>
                    {model.type === 'singer' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                      </svg>
                    ) : model.type === 'rapper' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m0 0l-2.828 2.828m0 0a9 9 0 010-12.728m2.828 2.828a5 5 0 00-1.414 1.414m0 0L8.464 5.586m0 0a9 9 0 0112.728 0" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium">{model.name}</p>
                    <p className="text-xs text-white/60">{model.type}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Voice Parameter Sliders */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-3 retune-text-glow-cyan">Voice Parameters</label>
          <p className="text-xs text-white/60 mb-4">Adjust these parameters to customize your voice profile</p>

          {/* Confidence Slider */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="text-white/80 text-sm">Confidence</label>
              <span className="text-[#00FFEE] text-sm font-medium retune-text-glow-cyan">{confidenceLevel}%</span>
            </div>
            <div className="flex items-center">
              <span className="text-white/60 text-xs mr-3">Timid</span>
              <div className="flex-1 relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={confidenceLevel}
                  onChange={(e) => setConfidenceLevel(parseInt(e.target.value))}
                  className="retune-slider w-full"
                />
              </div>
              <span className="text-white/60 text-xs ml-3">Bold</span>
            </div>
          </div>

          {/* Vocal Range Slider */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="text-white/80 text-sm">Vocal Range</label>
              <span className="text-[#00FFEE] text-sm font-medium retune-text-glow-cyan">{vocalRange}%</span>
            </div>
            <div className="flex items-center">
              <span className="text-white/60 text-xs mr-3">Limited</span>
              <div className="flex-1 relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={vocalRange}
                  onChange={(e) => setVocalRange(parseInt(e.target.value))}
                  className="retune-slider w-full"
                />
              </div>
              <span className="text-white/60 text-xs ml-3">Expansive</span>
            </div>
          </div>

          {/* Emotional Expression Slider */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="text-white/80 text-sm">Emotional Expression</label>
              <span className="text-[#00FFEE] text-sm font-medium retune-text-glow-cyan">{emotionalExpression}%</span>
            </div>
            <div className="flex items-center">
              <span className="text-white/60 text-xs mr-3">Flat</span>
              <div className="flex-1 relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={emotionalExpression}
                  onChange={(e) => setEmotionalExpression(parseInt(e.target.value))}
                  className="retune-slider w-full"
                />
              </div>
              <span className="text-white/60 text-xs ml-3">Expressive</span>
            </div>
          </div>

          {/* Clarity Slider */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="text-white/80 text-sm">Clarity/Articulation</label>
              <span className="text-[#00FFEE] text-sm font-medium retune-text-glow-cyan">{clarityLevel}%</span>
            </div>
            <div className="flex items-center">
              <span className="text-white/60 text-xs mr-3">Muffled</span>
              <div className="flex-1 relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={clarityLevel}
                  onChange={(e) => setClarityLevel(parseInt(e.target.value))}
                  className="retune-slider w-full"
                />
              </div>
              <span className="text-white/60 text-xs ml-3">Crystal</span>
            </div>
          </div>
        </div>

        {/* Create Profile Button */}
        <div className="flex flex-col items-center">
          <div className="w-full max-w-md bg-black/30 p-4 rounded-lg mb-4 border border-[#00FFEE]/20">
            <h4 className="text-sm font-medium text-center mb-3">Ready to create your voice profile?</h4>
            <p className="text-xs text-white/70 text-center mb-4">
              {audioSamples.length === 0
                ? "Upload at least one voice sample to continue"
                : audioSamples.length < 3
                  ? `You've uploaded ${audioSamples.length} sample${audioSamples.length > 1 ? 's' : ''}. Adding more samples improves quality.`
                  : "Great! You've uploaded enough samples for good quality."}
            </p>
            <div className="flex justify-center">
              <ThemeButton
                onClick={createVoiceProfile}
                disabled={audioSamples.length === 0 || isProcessing}
                className={`retune-button ${audioSamples.length > 0 ? 'animate-pulse' : ''}`}
                style={{
                  boxShadow: audioSamples.length > 0 ? '0 0 15px rgba(0, 255, 238, 0.4)' : 'none',
                  transition: 'all 0.3s ease'
                }}
              >
                {isProcessing ? 'Creating Profile...' : 'Create Voice Profile'}
              </ThemeButton>
            </div>
          </div>
          <p className="text-xs text-white/60 text-center">
            This will process your samples and create a voice profile for enhancement
          </p>
        </div>
      </div>
    );
  };

  // Step 2: Voice Enhancement Engine
  const renderStep2 = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4">Step 2: Voice Enhancement Engine</ThemeHeading>
        <p className="text-sm text-white/70 mb-6">
          Enhance your voice profile with advanced AI processing.
        </p>

        {/* Voice Profile Card */}
        {voiceProfile && (
          <div className="retune-glass-container p-5 mb-6">
            <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Voice Profile</h3>
            <div className="flex items-center mb-3">
              <div className="w-12 h-12 rounded-full bg-black/40 border border-[#00FFEE]/30 flex items-center justify-center mr-3 shadow-[0_0_15px_rgba(0,255,238,0.3)]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#00FFEE]" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.7))' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-white retune-text-glow-cyan">{voiceProfile.name}</p>
                <p className="text-xs text-white/80">Created {new Date(voiceProfile.createdAt).toLocaleString()}</p>
              </div>
            </div>
            <div className="flex items-center justify-between text-xs text-white/80 mb-2">
              <span>Quality Score</span>
              <span className={`font-medium ${voiceProfile.qualityScore >= 85
                ? 'text-[#00FFEE] retune-text-glow-cyan'
                : voiceProfile.qualityScore >= 70
                  ? 'text-yellow-300'
                  : 'text-red-400'
                }`}>
                {voiceProfile.qualityScore}%
              </span>
            </div>
            <div className="bg-black/30 h-2 rounded-full overflow-hidden">
              <div
                className={`h-full ${voiceProfile.qualityScore >= 85
                  ? 'bg-gradient-to-r from-[#00FFEE]/80 to-[#00FFEE]'
                  : voiceProfile.qualityScore >= 70
                    ? 'bg-gradient-to-r from-yellow-500/80 to-yellow-300'
                    : 'bg-gradient-to-r from-red-600/80 to-red-400'
                  }`}
                style={{
                  width: `${voiceProfile.qualityScore}%`,
                  boxShadow: voiceProfile.qualityScore >= 85
                    ? '0 0 10px rgba(0, 255, 238, 0.7)'
                    : voiceProfile.qualityScore >= 70
                      ? '0 0 10px rgba(253, 224, 71, 0.7)'
                      : '0 0 10px rgba(248, 113, 113, 0.7)'
                }}
              ></div>
            </div>

            {/* Voice Parameters */}
            <div className="mt-4 pt-4 border-t border-white/10">
              <h4 className="text-xs font-medium text-white/80 mb-3">Voice Parameters</h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <p className="text-xs text-white/60 mb-1">Confidence</p>
                  <div className="bg-black/30 h-1.5 rounded-full overflow-hidden">
                    <div className="h-full bg-[#00FFEE]/70" style={{ width: `${voiceProfile.confidence}%` }}></div>
                  </div>
                </div>
                <div>
                  <p className="text-xs text-white/60 mb-1">Vocal Range</p>
                  <div className="bg-black/30 h-1.5 rounded-full overflow-hidden">
                    <div className="h-full bg-[#00FFEE]/70" style={{ width: `${voiceProfile.vocalRange}%` }}></div>
                  </div>
                </div>
                <div>
                  <p className="text-xs text-white/60 mb-1">Emotional Expression</p>
                  <div className="bg-black/30 h-1.5 rounded-full overflow-hidden">
                    <div className="h-full bg-[#00FFEE]/70" style={{ width: `${voiceProfile.emotionalExpression}%` }}></div>
                  </div>
                </div>
                <div>
                  <p className="text-xs text-white/60 mb-1">Clarity</p>
                  <div className="bg-black/30 h-1.5 rounded-full overflow-hidden">
                    <div className="h-full bg-[#00FFEE]/70" style={{ width: `${voiceProfile.clarity}%` }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Custom Audio Effects Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Custom Audio Effects</label>
          <p className="text-xs text-white/60 mb-3">Select the audio effect to enhance your voice with ElevenLabs</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div
              onClick={() => setEnhancementModel('rvc')}
              className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${enhancementModel === 'rvc'
                ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : 'border-white/10 hover:border-[#00FFEE]/30'
                }`}
            >
              <div className="flex items-center mb-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${enhancementModel === 'rvc'
                  ? 'bg-[#00FFEE]/20 text-[#00FFEE]'
                  : 'bg-black/40 text-white/60'
                  }`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">Studio Quality</p>
                  <p className="text-xs text-white/60">Professional studio enhancement</p>
                </div>
              </div>
              <p className="text-xs text-white/70 ml-11">
                Clean, professional sound with balanced EQ and compression
              </p>
            </div>

            <div
              onClick={() => setEnhancementModel('bark')}
              className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${enhancementModel === 'bark'
                ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : 'border-white/10 hover:border-[#00FFEE]/30'
                }`}
            >
              <div className="flex items-center mb-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${enhancementModel === 'bark'
                  ? 'bg-[#00FFEE]/20 text-[#00FFEE]'
                  : 'bg-black/40 text-white/60'
                  }`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m0 0l-2.828 2.828m0 0a9 9 0 010-12.728m2.828 2.828a5 5 0 00-1.414 1.414m0 0L8.464 5.586m0 0a9 9 0 0112.728 0" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">Clarity Boost</p>
                  <p className="text-xs text-white/60">Enhanced vocal clarity</p>
                </div>
              </div>
              <p className="text-xs text-white/70 ml-11">
                Crystal clear vocals with enhanced presence and articulation
              </p>
            </div>

            <div
              onClick={() => setEnhancementModel('autovc')}
              className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${enhancementModel === 'autovc'
                ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : 'border-white/10 hover:border-[#00FFEE]/30'
                }`}
            >
              <div className="flex items-center mb-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${enhancementModel === 'autovc'
                  ? 'bg-[#00FFEE]/20 text-[#00FFEE]'
                  : 'bg-black/40 text-white/60'
                  }`}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium">Warmth</p>
                  <p className="text-xs text-white/60">Rich, warm vocal tone</p>
                </div>
              </div>
              <p className="text-xs text-white/70 ml-11">
                Adds depth and richness to your voice with subtle harmonics
              </p>
            </div>
          </div>
        </div>

        {/* Reference Voice Mixing */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Reference Voice Mixing (Optional)</label>
          <p className="text-xs text-white/60 mb-3">Add characteristics from reference voices to enhance your profile</p>

          <div className="retune-glass-container p-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-xs text-white/80 mb-1">Rihanna&apos;s Clarity</label>
                <div className="flex items-center">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={30}
                    className="retune-slider flex-1 mr-2"
                    readOnly
                  />
                  <span className="text-xs text-[#00FFEE]">30%</span>
                </div>
              </div>
              <div>
                <label className="block text-xs text-white/80 mb-1">Drake&apos;s Flow</label>
                <div className="flex items-center">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={20}
                    className="retune-slider flex-1 mr-2"
                    readOnly
                  />
                  <span className="text-xs text-[#00FFEE]">20%</span>
                </div>
              </div>
            </div>
            <div className="flex justify-center">
              <button className="text-xs text-[#00FFEE] hover:text-[#00FFEE]/80 transition-colors duration-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                </svg>
                Add Reference Voice
              </button>
            </div>
          </div>
        </div>

        {/* Enhancement Options */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2 retune-text-glow-cyan">Enhancement Options</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="retune-glass-container p-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm text-white/80">Pitch Correction</label>
                <div className="relative inline-block w-10 h-5 transition duration-200 ease-in-out rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    id="pitch-correction"
                    className="absolute w-5 h-5 opacity-0 cursor-pointer"
                    defaultChecked={true}
                  />
                  <label
                    htmlFor="pitch-correction"
                    className="block h-5 overflow-hidden rounded-full bg-black/40 cursor-pointer"
                  >
                    <span
                      className={`block h-5 w-5 rounded-full bg-[#00FFEE] transform transition-transform duration-200 ease-in-out translate-x-5`}
                    ></span>
                  </label>
                </div>
              </div>
              <p className="text-xs text-white/60">
                Automatically correct pitch variations for more accurate notes
              </p>
            </div>
            <div className="retune-glass-container p-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm text-white/80">Dynamic Tuning</label>
                <div className="relative inline-block w-10 h-5 transition duration-200 ease-in-out rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    id="dynamic-tuning"
                    className="absolute w-5 h-5 opacity-0 cursor-pointer"
                    defaultChecked={true}
                  />
                  <label
                    htmlFor="dynamic-tuning"
                    className="block h-5 overflow-hidden rounded-full bg-black/40 cursor-pointer"
                  >
                    <span
                      className={`block h-5 w-5 rounded-full bg-[#00FFEE] transform transition-transform duration-200 ease-in-out translate-x-5`}
                    ></span>
                  </label>
                </div>
              </div>
              <p className="text-xs text-white/60">
                Apply real-time tuning similar to Auto-Tune for professional sound
              </p>
            </div>
            <div className="retune-glass-container p-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm text-white/80">Tone Enhancement</label>
                <div className="relative inline-block w-10 h-5 transition duration-200 ease-in-out rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    id="tone-enhancement"
                    className="absolute w-5 h-5 opacity-0 cursor-pointer"
                    defaultChecked={true}
                  />
                  <label
                    htmlFor="tone-enhancement"
                    className="block h-5 overflow-hidden rounded-full bg-black/40 cursor-pointer"
                  >
                    <span
                      className={`block h-5 w-5 rounded-full bg-[#00FFEE] transform transition-transform duration-200 ease-in-out translate-x-5`}
                    ></span>
                  </label>
                </div>
              </div>
              <p className="text-xs text-white/60">
                Improve tonal quality and timbre for richer, fuller sound
              </p>
            </div>
            <div className="retune-glass-container p-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm text-white/80">Emotional Range Boost</label>
                <div className="relative inline-block w-10 h-5 transition duration-200 ease-in-out rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    id="emotional-range"
                    className="absolute w-5 h-5 opacity-0 cursor-pointer"
                    defaultChecked={true}
                  />
                  <label
                    htmlFor="emotional-range"
                    className="block h-5 overflow-hidden rounded-full bg-black/40 cursor-pointer"
                  >
                    <span
                      className={`block h-5 w-5 rounded-full bg-[#00FFEE] transform transition-transform duration-200 ease-in-out translate-x-5`}
                    ></span>
                  </label>
                </div>
              </div>
              <p className="text-xs text-white/60">
                Enhance emotional expressiveness for more compelling performances
              </p>
            </div>
          </div>
        </div>

        {/* Enhance Button */}
        <div className="flex justify-end">
          <ThemeButton
            onClick={enhanceVoiceProfile}
            disabled={!voiceProfile || isEnhancing}
            className="retune-button"
          >
            {isEnhancing ? 'Enhancing...' : 'Enhance Voice Profile'}
          </ThemeButton>
        </div>
      </div>
    );
  };

  // Step 3: Voice Application Interface
  const renderStep3 = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4">Step 3: Voice Application Interface</ThemeHeading>
        <p className="text-sm text-white/70 mb-6">
          Apply your enhanced voice to various use cases.
        </p>

        {/* Enhanced Voice Profile Card */}
        {voiceProfile && (
          <div className="retune-glass-container p-5 mb-6">
            <div className="flex items-center mb-3">
              <div className="w-12 h-12 rounded-full bg-black/40 border border-[#00FFEE]/30 flex items-center justify-center mr-3 shadow-[0_0_15px_rgba(0,255,238,0.3)]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#00FFEE]" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.7))' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <div>
                <div className="flex items-center">
                  <p className="text-sm font-medium text-white retune-text-glow-cyan">{voiceProfile.name}</p>
                  <span className="ml-2 px-2 py-0.5 text-xs bg-[#00FFEE]/20 text-[#00FFEE] rounded-full">Enhanced</span>
                </div>
                <p className="text-xs text-white/80">Quality Score: {voiceProfile.qualityScore}%</p>
              </div>
            </div>
          </div>
        )}

        {/* Mode Selection Tabs */}
        <div className="mb-6">
          <div className="flex border-b border-white/10">
            <button
              onClick={() => setApplicationMode('tts')}
              className={`px-4 py-2 text-sm font-medium transition-colors duration-300 ${applicationMode === 'tts'
                ? 'text-[#00FFEE] border-b-2 border-[#00FFEE] retune-text-glow-cyan'
                : 'text-white/60 hover:text-white/80'
                }`}
            >
              Text-to-Speech
            </button>
            <button
              onClick={() => setApplicationMode('enhance')}
              className={`px-4 py-2 text-sm font-medium transition-colors duration-300 ${applicationMode === 'enhance'
                ? 'text-[#00FFEE] border-b-2 border-[#00FFEE] retune-text-glow-cyan'
                : 'text-white/60 hover:text-white/80'
                }`}
            >
              Voice Enhancement
            </button>
            <button
              onClick={() => setApplicationMode('collaborate')}
              className={`px-4 py-2 text-sm font-medium transition-colors duration-300 ${applicationMode === 'collaborate'
                ? 'text-[#00FFEE] border-b-2 border-[#00FFEE] retune-text-glow-cyan'
                : 'text-white/60 hover:text-white/80'
                }`}
            >
              Collaborative
            </button>
          </div>
        </div>

        {/* Mode Content */}
        <div className="mb-6">
          {applicationMode === 'tts' && (
            <div className="retune-glass-container p-5">
              <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Text-to-Speech Singing/Rapping</h3>
              <p className="text-xs text-white/70 mb-4">
                Enter lyrics to be performed by your enhanced voice
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Lyrics</label>
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full bg-black/40 border border-white/20 rounded-lg p-3 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300"
                  placeholder="Enter lyrics here..."
                  rows={5}
                ></textarea>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Style</label>
                  <select className="w-full bg-black/40 border border-white/20 rounded-lg p-2.5 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300">
                    <option value="singing">Singing</option>
                    <option value="rapping">Rapping</option>
                    <option value="spoken">Spoken Word</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Emotion</label>
                  <select className="w-full bg-black/40 border border-white/20 rounded-lg p-2.5 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300">
                    <option value="neutral">Neutral</option>
                    <option value="happy">Happy</option>
                    <option value="sad">Sad</option>
                    <option value="energetic">Energetic</option>
                    <option value="passionate">Passionate</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end">
                <ThemeButton
                  onClick={processApplication}
                  disabled={!inputText.trim() || isProcessing}
                  className="retune-button"
                >
                  {isProcessing ? 'Processing...' : 'Generate Performance'}
                </ThemeButton>
              </div>
            </div>
          )}

          {applicationMode === 'enhance' && (
            <div className="retune-glass-container p-5">
              <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Voice Enhancement</h3>
              <p className="text-xs text-white/70 mb-4">
                Upload your rough vocal take to enhance it with your voice profile
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Upload Audio</label>
                <div
                  className="border-2 border-dashed border-white/20 rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-[#00FFEE]/30 transition-colors duration-300"
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'audio/*';
                    input.onchange = (e) => {
                      const files = (e.target as HTMLInputElement).files;
                      if (files && files.length > 0) {
                        const file = files[0];
                        setInputAudio(file);
                        setInputAudioUrl(URL.createObjectURL(file));
                      }
                    };
                    input.click();
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white/40 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <p className="text-sm text-white/60 mb-1">
                    {inputAudio ? inputAudio.name : 'Drag and drop or click to upload'}
                  </p>
                  <p className="text-xs text-white/40">
                    MP3, WAV, or AIFF (max. 10MB)
                  </p>
                </div>
              </div>

              {inputAudioUrl && (
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">Preview</label>
                  <audio controls src={inputAudioUrl} className="w-full"></audio>
                </div>
              )}

              <div className="flex justify-end">
                <ThemeButton
                  onClick={processApplication}
                  disabled={!inputAudio || isProcessing}
                  className="retune-button"
                >
                  {isProcessing ? 'Processing...' : 'Enhance Audio'}
                </ThemeButton>
              </div>
            </div>
          )}

          {applicationMode === 'collaborate' && (
            <div className="retune-glass-container p-5">
              <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Collaborative Mode</h3>
              <p className="text-xs text-white/70 mb-4">
                Combine multiple Retune voices for harmonies and collaborations
              </p>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Select Additional Voices</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                  <div className="retune-glass-container p-3 flex items-center">
                    <input type="checkbox" className="mr-3" id="voice1" />
                    <label htmlFor="voice1" className="text-sm text-white/80">Drake-inspired Voice</label>
                  </div>
                  <div className="retune-glass-container p-3 flex items-center">
                    <input type="checkbox" className="mr-3" id="voice2" />
                    <label htmlFor="voice2" className="text-sm text-white/80">Rihanna-inspired Voice</label>
                  </div>
                  <div className="retune-glass-container p-3 flex items-center">
                    <input type="checkbox" className="mr-3" id="voice3" />
                    <label htmlFor="voice3" className="text-sm text-white/80">The Weeknd-inspired Voice</label>
                  </div>
                  <div className="retune-glass-container p-3 flex items-center">
                    <input type="checkbox" className="mr-3" id="voice4" />
                    <label htmlFor="voice4" className="text-sm text-white/80">Kendrick-inspired Voice</label>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Lyrics</label>
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full bg-black/40 border border-white/20 rounded-lg p-3 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300"
                  placeholder="Enter lyrics for the collaboration..."
                  rows={5}
                ></textarea>
              </div>

              <div className="flex justify-end">
                <ThemeButton
                  onClick={processApplication}
                  disabled={!inputText.trim() || isProcessing}
                  className="retune-button"
                >
                  {isProcessing ? 'Processing...' : 'Create Collaboration'}
                </ThemeButton>
              </div>
            </div>
          )}
        </div>

        {/* Output Section */}
        {outputAudioUrl && (
          <div className="mb-6">
            <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Output</h3>
            <div className="retune-glass-container p-5">
              <audio controls src={outputAudioUrl} className="w-full mb-4"></audio>

              <div className="flex justify-between">
                <ThemeButton
                  onClick={() => {
                    // Reset output
                    setOutputAudioUrl(null);
                  }}
                  variant="secondary"
                  size="sm"
                >
                  Reset
                </ThemeButton>

                <ThemeButton
                  onClick={() => {
                    // Download audio
                    const a = document.createElement('a');
                    a.href = outputAudioUrl;
                    a.download = `superhuman_voice_${Date.now()}.wav`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }}
                  variant="primary"
                  size="sm"
                >
                  Download
                </ThemeButton>
              </div>
            </div>
          </div>
        )}

        {/* Integration Options */}
        <div>
          <h3 className="text-sm font-medium retune-text-glow-cyan mb-3">Integration Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="retune-glass-container p-4">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
                <h4 className="text-sm font-medium">Use in Lyric Generator</h4>
              </div>
              <p className="text-xs text-white/60 mb-3">
                Apply this voice to lyrics created with the Lyric Generator
              </p>
              <button className="text-xs text-[#00FFEE] hover:text-[#00FFEE]/80 transition-colors duration-300">
                Connect to Lyric Generator
              </button>
            </div>

            <div className="retune-glass-container p-4">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m0 0l-2.828 2.828m0 0a9 9 0 010-12.728m2.828 2.828a5 5 0 00-1.414 1.414m0 0L8.464 5.586m0 0a9 9 0 0112.728 0" />
                </svg>
                <h4 className="text-sm font-medium">Export to DAW</h4>
              </div>
              <p className="text-xs text-white/60 mb-3">
                Export your voice for use in professional DAWs
              </p>
              <button className="text-xs text-[#00FFEE] hover:text-[#00FFEE]/80 transition-colors duration-300">
                Export Voice Profile
              </button>
            </div>

            <div className="retune-glass-container p-4">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h4 className="text-sm font-medium">Share Voice</h4>
              </div>
              <p className="text-xs text-white/60 mb-3">
                Share your voice profile with collaborators
              </p>
              <button className="text-xs text-[#00FFEE] hover:text-[#00FFEE]/80 transition-colors duration-300">
                Share Voice Profile
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <ThemeCard className="p-6">
      <ThemeHeading level={2} className="mb-2 retune-title">Me, But Superhuman</ThemeHeading>
      <p className="text-sm text-white/70 mb-6">
        Transform your voice into an enhanced, expressive AI voice capable of professional-quality singing or rapping.
      </p>

      {/* Step indicators */}
      <div className="flex items-center mb-8">
        {[1, 2, 3].map((step) => (
          <div key={step} className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === step
                ? 'bg-black/40 border border-[#00FFEE]/60 text-[#00FFEE] shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : currentStep > step
                  ? 'bg-black/40 border border-green-400/60 text-green-400'
                  : 'bg-black/40 border border-white/20 text-white/60'
                }`}
            >
              {currentStep > step ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                step
              )}
            </div>
            {step < 3 && (
              <div className={`w-12 h-0.5 ${currentStep > step ? 'bg-green-400/60' : 'bg-white/20'
                }`}></div>
            )}
          </div>
        ))}
      </div>

      {/* Current step content */}
      {renderStep()}
    </ThemeCard>
  );
};

export default SuperhumanPipeline;
