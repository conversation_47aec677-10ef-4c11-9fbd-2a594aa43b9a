import { NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs';

// Import utilities
import { executeInRepository, validateRepository } from '@/utils/repositoryManager';
import { createGenerationDirectory, saveJsonFile, getPublicUrl } from '@/utils/fileManager';
import { deductCredits } from '@/lib/credit-config';
import { getUserIdFromRequest } from '@/utils/auth';

export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();
    const { lyrics, style, mood, tempo, userId } = body;

    // Validate required fields
    if (!lyrics) {
      return NextResponse.json({ error: 'Lyrics are required' }, { status: 400 });
    }

    // Get user ID from request or use provided ID
    const authenticatedUserId = await getUserIdFromRequest(request) || userId || 'demo-user';

    // Create a directory for this generation
    const { id: generationId, path: outputDir } = createGenerationDirectory();

    // Save the request details
    saveJsonFile({
      id: generationId,
      status: 'processing',
      progress: 0,
      message: 'Starting music generation...',
      timestamp: Date.now(),
      userId: authenticatedUserId,
      request: {
        lyrics,
        style,
        mood,
        tempo
      }
    }, outputDir, 'status.json');

    // Deduct credits for this operation (100 credits)
    try {
      await deductCredits(authenticatedUserId, 100, 'Music generation with Suno');
    } catch (creditError) {
      return NextResponse.json({
        error: 'Insufficient credits for this operation',
        details: creditError instanceof Error ? creditError.message : 'Unknown credit error'
      }, { status: 402 });
    }

    // Process the music generation in the background
    (async () => {
      try {
        // Validate Suno repository
        const sunoStatus = await validateRepository('SUNO');

        if (!sunoStatus.valid) {
          throw new Error(`Suno repository issue: ${sunoStatus.error}`);
        }

        // Update status to generating music
        saveJsonFile({
          id: generationId,
          status: 'processing',
          progress: 20,
          message: 'Generating music...',
          timestamp: Date.now(),
          userId: authenticatedUserId
        }, outputDir, 'status.json');

        // Prepare parameters for Suno
        const styleParam = style ? `--style "${style}"` : '';
        const moodParam = mood ? `--mood "${mood}"` : '';
        const tempoParam = tempo ? `--tempo ${tempo}` : '';

        // Save lyrics to a file
        const lyricsPath = path.join(outputDir, 'lyrics.txt');
        fs.writeFileSync(lyricsPath, lyrics, 'utf-8');

        // Output paths
        const outputPath = path.join(outputDir, 'generated.wav');
        const metadataPath = path.join(outputDir, 'metadata.json');

        // Execute the Suno command
        const sunoCommand = `python -m suno.generate \
          --lyrics_file "${lyricsPath}" \
          --output "${outputPath}" \
          --metadata_output "${metadataPath}" \
          ${styleParam} ${moodParam} ${tempoParam}`;

        console.log(`Executing Suno command: ${sunoCommand}`);

        const sunoResult = await executeInRepository('SUNO', sunoCommand);

        if (!sunoResult.success) {
          throw new Error(`Music generation failed: ${sunoResult.stderr}`);
        }

        // Read metadata if available
        let metadata = {};
        if (fs.existsSync(metadataPath)) {
          try {
            metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf-8'));
          } catch (metadataError) {
            console.error('Error parsing metadata:', metadataError);
          }
        }

        // Update status to completed
        const audioUrl = `/generations/${generationId}/generated.wav`;

        saveJsonFile({
          id: generationId,
          status: 'completed',
          progress: 100,
          message: 'Music generation completed',
          timestamp: Date.now(),
          userId: authenticatedUserId,
          audioUrl: audioUrl,
          metadata: {
            ...metadata,
            lyrics,
            style,
            mood,
            tempo,
            createdAt: new Date().toISOString()
          }
        }, outputDir, 'status.json');

      } catch (error) {
        console.error('Error in music generation process:', error);

        // Update status file with error
        saveJsonFile({
          id: generationId,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
          userId: authenticatedUserId
        }, outputDir, 'status.json');
      }
    })();

    // Return immediate response with generation ID
    return NextResponse.json({
      id: generationId,
      status: 'processing',
      message: 'Music generation process started'
    });

  } catch (error) {
    console.error('Error starting music generation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to start music generation' },
      { status: 500 }
    );
  }
}

// Status check endpoint
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'No ID provided' }, { status: 400 });
    }

    const statusPath = path.join(process.cwd(), 'public', 'generations', id, 'status.json');

    if (!fs.existsSync(statusPath)) {
      return NextResponse.json({ error: 'Generation not found' }, { status: 404 });
    }

    const statusData = JSON.parse(fs.readFileSync(statusPath, 'utf-8'));
    return NextResponse.json(statusData);

  } catch (error) {
    console.error('Error checking generation status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to check generation status' },
      { status: 500 }
    );
  }
}
