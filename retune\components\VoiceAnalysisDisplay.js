import React from 'react';

const VoiceAnalysisDisplay = ({ analysisData, isLoading }) => {
  if (isLoading) {
    return (
      <div className="analysis-loading">
        <div className="loading-spinner"></div>
        <p>Analyzing your voice...</p>
      </div>
    );
  }

  if (!analysisData) {
    return null;
  }

  const { pitch, volume, clarity, confidence, ai_artist_potential } = analysisData;

  // Helper function to render a meter
  const renderMeter = (value, label, description) => {
    const percentage = Math.min(100, Math.max(0, value * 100));

    return (
      <div className="analysis-meter">
        <div className="meter-label">
          <span>{label}</span>
          <span className="meter-value">{percentage.toFixed(0)}%</span>
        </div>
        <div className="meter-bar">
          <div
            className="meter-fill"
            style={{ width: `${percentage}%` }}
          ></div>
        </div>
        <p className="meter-description">{description}</p>
      </div>
    );
  };

  return (
    <div className="voice-analysis">
      <h3>Voice Analysis Results</h3>

      <div className="analysis-section">
        <h4>Pitch Profile</h4>
        <div className="pitch-info">
          <div className="pitch-stat">
            <span className="stat-label">Average</span>
            <span className="stat-value">{pitch.average.toFixed(1)} Hz</span>
          </div>
          <div className="pitch-stat">
            <span className="stat-label">Range</span>
            <span className="stat-value">{pitch.min.toFixed(1)} - {pitch.max.toFixed(1)} Hz</span>
          </div>
        </div>
        {renderMeter(
          pitch.stability,
          "Pitch Stability",
          "How consistent your pitch remains throughout your speech"
        )}
      </div>

      <div className="analysis-section">
        <h4>Volume Profile</h4>
        {renderMeter(
          volume.average,
          "Volume Level",
          "The overall loudness of your voice"
        )}
        {renderMeter(
          volume.dynamic_range,
          "Dynamic Range",
          "The variation between soft and loud parts of your speech"
        )}
      </div>

      <div className="analysis-section">
        <h4>Clarity</h4>
        {renderMeter(
          clarity.score,
          "Overall Clarity",
          "How clear and understandable your speech is"
        )}
        {renderMeter(
          clarity.articulation,
          "Articulation",
          "How precisely you form words and sounds"
        )}
      </div>

      <div className="analysis-section">
        <h4>Confidence</h4>
        {renderMeter(
          confidence.score,
          "Confidence Score",
          "How confident your voice sounds to listeners"
        )}
        {renderMeter(
          confidence.steadiness,
          "Vocal Steadiness",
          "The stability and control in your voice"
        )}
      </div>

      {ai_artist_potential && (
        <div className="analysis-section">
          <h4>AI Artist Potential</h4>
          {renderMeter(
            ai_artist_potential.score,
            "Artist Potential",
            "Your potential for AI-enhanced music creation"
          )}
          {renderMeter(
            ai_artist_potential.uniqueness,
            "Voice Uniqueness",
            "How distinctive your voice is compared to others"
          )}
          <div className="genre-match">
            <span className="genre-label">Best Genre Match:</span>
            <span className="genre-value">{ai_artist_potential.genre_match}</span>
          </div>
        </div>
      )}

      <div className="analysis-summary">
        <h4>Summary</h4>
        <p>
          {generateSummary(analysisData)}
        </p>
      </div>
    </div>
  );
};

// Helper function to generate a summary based on analysis data
const generateSummary = (data) => {
  const { pitch, volume, clarity, confidence, ai_artist_potential } = data;

  // Calculate overall score
  const overallScore = (
    pitch.stability * 0.2 +
    volume.average * 0.15 +
    volume.dynamic_range * 0.15 +
    clarity.score * 0.2 +
    clarity.articulation * 0.1 +
    confidence.score * 0.1 +
    confidence.steadiness * 0.1
  );

  // Identify strengths
  const strengths = [];
  if (pitch.stability > 0.7) strengths.push("pitch stability");
  if (volume.average > 0.7) strengths.push("volume level");
  if (volume.dynamic_range > 0.7) strengths.push("dynamic range");
  if (clarity.score > 0.7) strengths.push("clarity");
  if (clarity.articulation > 0.7) strengths.push("articulation");
  if (confidence.score > 0.7) strengths.push("confidence");
  if (confidence.steadiness > 0.7) strengths.push("vocal steadiness");

  // Identify areas for improvement
  const improvements = [];
  if (pitch.stability < 0.5) improvements.push("pitch stability");
  if (volume.average < 0.5) improvements.push("volume level");
  if (volume.dynamic_range < 0.5) improvements.push("dynamic range");
  if (clarity.score < 0.5) improvements.push("clarity");
  if (clarity.articulation < 0.5) improvements.push("articulation");
  if (confidence.score < 0.5) improvements.push("confidence");
  if (confidence.steadiness < 0.5) improvements.push("vocal steadiness");

  // Generate summary text
  let summary = `Your voice has an overall score of ${(overallScore * 100).toFixed(0)}%. `;

  if (strengths.length > 0) {
    summary += `Your strengths include ${formatList(strengths)}. `;
  }

  if (improvements.length > 0) {
    summary += `Areas for improvement include ${formatList(improvements)}. `;
  }

  // Add recommendation for voice enhancement
  if (overallScore < 0.5) {
    summary += "We recommend using our voice enhancement to significantly improve your vocal presence. ";
  } else if (overallScore < 0.7) {
    summary += "Our voice enhancement can help refine your voice for better impact. ";
  } else {
    summary += "Your voice is already strong, but our enhancement can add that extra polish for a truly professional sound. ";
  }

  // Add AI artist potential information if available
  if (ai_artist_potential) {
    if (ai_artist_potential.score > 0.7) {
      summary += `You have excellent potential as an AI-enhanced artist, particularly in the ${ai_artist_potential.genre_match} genre. `;
      summary += "Sesame AI can help create a unique artist persona based on your voice characteristics.";
    } else if (ai_artist_potential.score > 0.5) {
      summary += `You have good potential as an AI-enhanced artist, with a natural fit for ${ai_artist_potential.genre_match} music. `;
      summary += "Sesame AI can help develop your unique sound further.";
    } else {
      summary += `With Sesame AI enhancement, we can develop your voice for ${ai_artist_potential.genre_match} music creation. `;
    }
  }

  return summary;
};

// Helper function to format a list of items in a grammatically correct way
const formatList = (items) => {
  if (items.length === 0) return "";
  if (items.length === 1) return items[0];
  if (items.length === 2) return `${items[0]} and ${items[1]}`;

  const lastItem = items.pop();
  return `${items.join(", ")}, and ${lastItem}`;
};

export default VoiceAnalysisDisplay;
