'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Preset {
  id: string;
  name: string;
  description: string;
  genre: string;
}

interface PresetsByGenre {
  [genre: string]: Preset[];
}

export default function AIMixingPresets() {
  const [presets, setPresets] = useState<PresetsByGenre>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Selected preset
  const [selectedPresetId, setSelectedPresetId] = useState<string>('');
  const [selectedGenre, setSelectedGenre] = useState<string>('');

  // Audio file
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  // Analysis results
  const [analyzing, setAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any | null>(null);

  // Recommendation results
  const [recommending, setRecommending] = useState(false);
  const [recommendedPreset, setRecommendedPreset] = useState<any | null>(null);

  // Customization results
  const [customizing, setCustomizing] = useState(false);
  const [customizedPreset, setCustomizedPreset] = useState<any | null>(null);

  // Active tab
  const [activeTab, setActiveTab] = useState<'recommend' | 'analyze' | 'customize'>('recommend');

  // Fetch available presets on component mount
  useEffect(() => {
    fetchPresets();
  }, []);

  // Fetch available presets (mock data)
  const fetchPresets = async () => {
    setLoading(true);
    setError(null);

    try {
      // Mock data instead of API call
      const mockPresets: PresetsByGenre = {
        'pop': [
          { id: 'pop-1', name: 'Pop Vocal', description: 'Bright, polished vocal with subtle effects', genre: 'pop' },
          { id: 'pop-2', name: 'Pop Ballad', description: 'Warm, emotional vocal with reverb', genre: 'pop' },
        ],
        'rap': [
          { id: 'rap-1', name: 'Modern Rap', description: 'Clear, punchy vocals with presence', genre: 'rap' },
          { id: 'rap-2', name: 'Trap Vocal', description: 'Processed vocal with character', genre: 'rap' },
        ],
        'rnb': [
          { id: 'rnb-1', name: 'R&B Smooth', description: 'Warm, smooth vocals with subtle reverb', genre: 'rnb' },
          { id: 'rnb-2', name: 'Neo Soul', description: 'Natural vocal with vintage character', genre: 'rnb' },
        ],
        'podcast': [
          { id: 'podcast-1', name: 'Podcast Clear', description: 'Clear, professional voice for spoken word', genre: 'podcast' },
          { id: 'podcast-2', name: 'Broadcast', description: 'Radio-ready voice with presence', genre: 'podcast' },
        ],
      };

      // Simulate network delay
      setTimeout(() => {
        setPresets(mockPresets);

        // Set default selected genre
        const firstGenre = Object.keys(mockPresets)[0];
        setSelectedGenre(firstGenre);

        // Set default selected preset
        if (mockPresets[firstGenre].length > 0) {
          setSelectedPresetId(mockPresets[firstGenre][0].id);
        }

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error fetching presets:', error);
      setError(`Error fetching presets: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLoading(false);
    }
  };

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setAudioFile(file);

    // Create URL for audio preview
    const url = URL.createObjectURL(file);
    setAudioUrl(url);

    // Reset analysis results
    setAnalysisResults(null);
    setRecommendedPreset(null);
    setCustomizedPreset(null);
  };

  // Handle analyze (mock implementation)
  const handleAnalyze = async () => {
    if (!audioFile) {
      setError('Please select an audio file');
      return;
    }

    setAnalyzing(true);
    setError(null);
    setAnalysisResults(null);

    try {
      // Simulate API call with timeout
      setTimeout(() => {
        // Create mock analysis results
        const mockResults = {
          estimated_gender: Math.random() > 0.5 ? 'male' : 'female',
          gender_probability: 0.85 + (Math.random() * 0.15),
          estimated_genre: ['pop', 'rap', 'rnb'][Math.floor(Math.random() * 3)],
          pitch_mean: 120 + (Math.random() * 80),
          pitch_min: 80 + (Math.random() * 40),
          pitch_max: 200 + (Math.random() * 100),
          pitch_std: 10 + (Math.random() * 40),
          spectral_centroid_mean: 2000 + (Math.random() * 3000),
          sibilance: 0.2 + (Math.random() * 0.6),
          noise_floor: -60 + (Math.random() * 10),
          dynamic_range: 20 + (Math.random() * 20),
          resonance_peaks: [
            { frequency: 200 + (Math.random() * 100), amplitude: 0.5 + (Math.random() * 0.5) },
            { frequency: 500 + (Math.random() * 200), amplitude: 0.3 + (Math.random() * 0.5) },
            { frequency: 1000 + (Math.random() * 500), amplitude: 0.2 + (Math.random() * 0.4) },
          ]
        };

        setAnalysisResults(mockResults);
        setAnalyzing(false);
      }, 2000);
    } catch (error) {
      console.error('Error analyzing audio:', error);
      setError(`Error analyzing audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setAnalyzing(false);
    }
  };

  // Handle recommend (mock implementation)
  const handleRecommend = async () => {
    if (!audioFile) {
      setError('Please select an audio file');
      return;
    }

    setRecommending(true);
    setError(null);
    setRecommendedPreset(null);

    try {
      // Simulate API call with timeout
      setTimeout(() => {
        // Create mock recommended preset
        const genres = ['pop', 'rap', 'rnb'];
        const randomGenre = genres[Math.floor(Math.random() * genres.length)];

        const mockRecommendedPreset = {
          id: `${randomGenre}-${Math.floor(Math.random() * 100)}`,
          name: `${randomGenre.charAt(0).toUpperCase() + randomGenre.slice(1)} Vocal`,
          description: `AI-recommended preset for your ${randomGenre} vocal`,
          genre: randomGenre,
          gender: Math.random() > 0.5 ? 'male' : 'female',
          eq: {
            low_cut: Math.floor(80 + (Math.random() * 40)),
            low_shelf: {
              freq: Math.floor(100 + (Math.random() * 100)),
              gain: Math.floor(1 + (Math.random() * 3))
            },
            presence: {
              freq: Math.floor(2000 + (Math.random() * 1000)),
              gain: Math.floor(2 + (Math.random() * 4))
            },
            air: {
              freq: Math.floor(8000 + (Math.random() * 2000)),
              gain: Math.floor(1 + (Math.random() * 3))
            }
          },
          compression: {
            threshold: -1 * Math.floor(12 + (Math.random() * 12)),
            ratio: Math.floor(2 + (Math.random() * 2)),
            attack: Math.floor(10 + (Math.random() * 20)),
            release: Math.floor(80 + (Math.random() * 120))
          },
          de_esser: {
            frequency: Math.floor(4000 + (Math.random() * 3000)),
            threshold: -1 * Math.floor(10 + (Math.random() * 10)),
            ratio: Math.floor(2 + (Math.random() * 3))
          },
          pitch_correction: {
            amount: Math.floor(20 + (Math.random() * 60)),
            speed: Math.random() > 0.5 ? 'medium' : 'fast',
            scale: Math.random() > 0.5 ? 'chromatic' : 'major'
          }
        };

        setRecommendedPreset(mockRecommendedPreset);
        setRecommending(false);
      }, 2000);
    } catch (error) {
      console.error('Error recommending preset:', error);
      setError(`Error recommending preset: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setRecommending(false);
    }
  };

  // Handle customize (mock implementation)
  const handleCustomize = async () => {
    if (!audioFile) {
      setError('Please select an audio file');
      return;
    }

    if (!selectedPresetId) {
      setError('Please select a preset to customize');
      return;
    }

    setCustomizing(true);
    setError(null);
    setCustomizedPreset(null);

    try {
      // Find the selected preset
      let selectedPreset: any = null;
      let selectedPresetGenre = '';

      // Search through all genres to find the preset
      Object.keys(presets).forEach(genre => {
        const found = presets[genre].find(preset => preset.id === selectedPresetId);
        if (found) {
          selectedPreset = found;
          selectedPresetGenre = genre;
        }
      });

      if (!selectedPreset) {
        throw new Error('Selected preset not found');
      }

      // Simulate API call with timeout
      setTimeout(() => {
        // Create a customized version of the preset
        const mockCustomizedPreset = {
          ...selectedPreset,
          id: `${selectedPresetId}-custom-${Date.now()}`,
          name: `${selectedPreset.name} (Customized)`,
          description: `AI-customized version of ${selectedPreset.name} for your voice`,
          eq: {
            low_cut: Math.floor(80 + (Math.random() * 40)),
            low_shelf: {
              freq: Math.floor(100 + (Math.random() * 100)),
              gain: Math.floor(1 + (Math.random() * 3))
            },
            presence: {
              freq: Math.floor(2000 + (Math.random() * 1000)),
              gain: Math.floor(2 + (Math.random() * 4))
            },
            air: {
              freq: Math.floor(8000 + (Math.random() * 2000)),
              gain: Math.floor(1 + (Math.random() * 3))
            }
          },
          compression: {
            threshold: -1 * Math.floor(12 + (Math.random() * 12)),
            ratio: Math.floor(2 + (Math.random() * 2)),
            attack: Math.floor(10 + (Math.random() * 20)),
            release: Math.floor(80 + (Math.random() * 120))
          },
          de_esser: {
            frequency: Math.floor(4000 + (Math.random() * 3000)),
            threshold: -1 * Math.floor(10 + (Math.random() * 10)),
            ratio: Math.floor(2 + (Math.random() * 3))
          },
          pitch_correction: {
            amount: Math.floor(20 + (Math.random() * 60)),
            speed: Math.random() > 0.5 ? 'medium' : 'fast',
            scale: Math.random() > 0.5 ? 'chromatic' : 'major'
          }
        };

        setCustomizedPreset(mockCustomizedPreset);
        setCustomizing(false);
      }, 2000);
    } catch (error) {
      console.error('Error customizing preset:', error);
      setError(`Error customizing preset: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setCustomizing(false);
    }
  };

  // Render the component
  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
      <h2 className="text-2xl font-semibold mb-4">AI-Powered Mixing Presets <span className="text-sm font-normal text-purple-400 ml-2">Sesame AI Technology</span></h2>

      <div className="bg-green-900/20 border border-green-800/30 rounded-lg p-3 mb-6">
        <h3 className="text-sm font-medium text-green-400 mb-1">Genre-Specific Processing</h3>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="px-2 py-1 bg-blue-900/30 text-xs rounded border border-blue-700/30">Pop</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Rap</span>
          <span className="px-2 py-1 bg-green-900/30 text-xs rounded border border-green-700/30">R&B</span>
          <span className="px-2 py-1 bg-yellow-900/30 text-xs rounded border border-yellow-700/30">Podcast</span>
        </div>
        <p className="text-xs text-gray-400">
          Our AI mixing system analyzes your voice characteristics and applies genre-specific processing chains.
          Each preset is designed by professional audio engineers and enhanced by Sesame AI's voice analysis technology
          to deliver broadcast-ready results tailored to your specific voice and genre.
        </p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-6">
        <button
          onClick={() => setActiveTab('recommend')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'recommend'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Recommend Preset
        </button>

        <button
          onClick={() => setActiveTab('analyze')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'analyze'
              ? 'text-purple-400 border-b-2 border-purple-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Analyze Voice
        </button>

        <button
          onClick={() => setActiveTab('customize')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'customize'
              ? 'text-green-400 border-b-2 border-green-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Customize Preset
        </button>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"></div>
          <p className="text-gray-400">Loading presets...</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Technical Guide */}
      <div className="mb-6 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-medium text-green-400 mb-3">Technical Guide: AI Mixing with Sesame AI</h3>

        <div className="space-y-4">
          <div>
            <h4 className="text-md font-medium text-white mb-1">Voice Analysis Technology</h4>
            <p className="text-sm text-gray-400 mb-2">
              Sesame AI analyzes your voice recording to extract key characteristics:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li><span className="text-green-400">Spectral Analysis</span> - Frequency distribution and timbral qualities</li>
              <li><span className="text-green-400">Dynamic Range</span> - Volume variations and expression patterns</li>
              <li><span className="text-green-400">Sibilance Detection</span> - Identifies problematic high-frequency content</li>
              <li><span className="text-green-400">Pitch Analysis</span> - Fundamental frequency and variations</li>
            </ul>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Genre-Specific Processing</h4>
            <p className="text-sm text-gray-400 mb-2">
              Each genre preset applies different processing chains optimized for that style:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li><span className="text-blue-400">Pop</span> - Bright, polished sound with subtle effects and moderate compression</li>
              <li><span className="text-purple-400">Rap</span> - Clear, punchy vocals with presence and controlled low-end</li>
              <li><span className="text-green-400">R&B</span> - Warm, smooth vocals with subtle reverb and gentle saturation</li>
              <li><span className="text-yellow-400">Podcast</span> - Clear, professional voice with minimal effects and controlled dynamics</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Audio File Upload */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3 text-blue-400">Upload Voice Recording</h3>
        <div className="flex flex-col md:flex-row gap-4">
          <label className="flex-1 flex flex-col items-center px-4 py-6 bg-gray-700/50 text-blue-400 rounded-lg border border-blue-400/30 border-dashed cursor-pointer hover:bg-gray-700/80 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
            <span className="mt-2 text-base">Select audio file</span>
            <span className="text-sm text-gray-400 mt-1">(WAV, MP3, OGG)</span>
            <input
              type="file"
              className="hidden"
              accept="audio/*"
              onChange={handleFileChange}
            />
          </label>

          {audioFile && (
            <div className="flex-1 bg-gray-700/30 p-4 rounded-lg">
              <p className="font-medium text-green-400 mb-2">File selected:</p>
              <p className="text-gray-300 truncate">{audioFile.name}</p>
              <p className="text-gray-400 text-sm mt-1">{(audioFile.size / (1024 * 1024)).toFixed(2)} MB</p>

              {audioUrl && (
                <div className="mt-3">
                  <audio controls className="w-full">
                    <source src={audioUrl} type={audioFile.type} />
                    Your browser does not support the audio element.
                  </audio>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Recommend Preset Tab */}
      {activeTab === 'recommend' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="mb-6">
            <button
              onClick={handleRecommend}
              disabled={recommending || !audioFile}
              className="w-full py-3 bg-blue-600 hover:bg-blue-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
            >
              {recommending ? 'Analyzing Voice...' : 'Recommend Preset'}
            </button>
          </div>

          {/* Recommendation Results */}
          {recommendedPreset && (
            <div className="bg-gray-700/30 p-4 rounded-lg">
              <h3 className="text-lg font-medium mb-3 text-blue-400">Recommended Preset</h3>

              <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700 mb-4">
                <p className="text-lg font-medium text-white">{recommendedPreset.name}</p>
                <p className="text-gray-400 mb-2">{recommendedPreset.description}</p>

                <div className="grid grid-cols-2 gap-2 mt-4">
                  <div className="text-sm">
                    <span className="text-blue-400">Genre:</span>
                    <span className="text-gray-300 ml-2 capitalize">{recommendedPreset.genre}</span>
                  </div>

                  <div className="text-sm">
                    <span className="text-blue-400">Type:</span>
                    <span className="text-gray-300 ml-2 capitalize">{recommendedPreset.gender || 'Neutral'}</span>
                  </div>
                </div>
              </div>

              {/* Preset Settings */}
              <div className="mt-4">
                <h4 className="text-md font-medium mb-2 text-blue-400">Preset Settings</h4>

                {/* EQ Settings */}
                {recommendedPreset.eq && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-300 mb-1">EQ</p>
                    <div className="bg-gray-800/30 p-2 rounded border border-gray-700 text-xs text-gray-400">
                      {recommendedPreset.eq.low_cut && (
                        <div>Low Cut: {recommendedPreset.eq.low_cut} Hz</div>
                      )}
                      {recommendedPreset.eq.low_shelf && (
                        <div>Low Shelf: {recommendedPreset.eq.low_shelf.freq} Hz, {recommendedPreset.eq.low_shelf.gain} dB</div>
                      )}
                      {recommendedPreset.eq.presence && (
                        <div>Presence: {recommendedPreset.eq.presence.freq} Hz, {recommendedPreset.eq.presence.gain} dB</div>
                      )}
                      {recommendedPreset.eq.air && (
                        <div>Air: {recommendedPreset.eq.air.freq} Hz, {recommendedPreset.eq.air.gain} dB</div>
                      )}
                    </div>
                  </div>
                )}

                {/* Compression Settings */}
                {recommendedPreset.compression && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-300 mb-1">Compression</p>
                    <div className="bg-gray-800/30 p-2 rounded border border-gray-700 text-xs text-gray-400">
                      <div>Threshold: {recommendedPreset.compression.threshold} dB</div>
                      <div>Ratio: {recommendedPreset.compression.ratio}:1</div>
                      <div>Attack: {recommendedPreset.compression.attack} ms</div>
                      <div>Release: {recommendedPreset.compression.release} ms</div>
                    </div>
                  </div>
                )}

                {/* De-esser Settings */}
                {recommendedPreset.de_esser && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-300 mb-1">De-esser</p>
                    <div className="bg-gray-800/30 p-2 rounded border border-gray-700 text-xs text-gray-400">
                      <div>Frequency: {recommendedPreset.de_esser.frequency} Hz</div>
                      <div>Threshold: {recommendedPreset.de_esser.threshold} dB</div>
                      <div>Ratio: {recommendedPreset.de_esser.ratio}:1</div>
                    </div>
                  </div>
                )}

                {/* Pitch Correction Settings */}
                {recommendedPreset.pitch_correction && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-gray-300 mb-1">Pitch Correction</p>
                    <div className="bg-gray-800/30 p-2 rounded border border-gray-700 text-xs text-gray-400">
                      <div>Amount: {recommendedPreset.pitch_correction.amount}%</div>
                      <div>Speed: {recommendedPreset.pitch_correction.speed}</div>
                      <div>Scale: {recommendedPreset.pitch_correction.scale}</div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-4">
                <button className="w-full py-2 bg-green-600 hover:bg-green-500 rounded-lg text-white font-medium transition-colors">
                  Apply Preset
                </button>
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Analyze Voice Tab */}
      {activeTab === 'analyze' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="mb-6">
            <button
              onClick={handleAnalyze}
              disabled={analyzing || !audioFile}
              className="w-full py-3 bg-purple-600 hover:bg-purple-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
            >
              {analyzing ? 'Analyzing Voice...' : 'Analyze Voice'}
            </button>
          </div>

          {/* Analysis Results */}
          {analysisResults && (
            <div className="bg-gray-700/30 p-4 rounded-lg">
              <h3 className="text-lg font-medium mb-3 text-purple-400">Voice Analysis Results</h3>

              {/* Voice Characteristics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                {/* Gender */}
                {analysisResults.estimated_gender && (
                  <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700">
                    <p className="text-sm font-medium text-purple-400 mb-1">Estimated Gender</p>
                    <p className="text-lg text-white capitalize">{analysisResults.estimated_gender}</p>
                    <p className="text-xs text-gray-400">
                      Probability: {(analysisResults.gender_probability * 100).toFixed(1)}%
                    </p>
                  </div>
                )}

                {/* Genre */}
                {analysisResults.estimated_genre && (
                  <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700">
                    <p className="text-sm font-medium text-purple-400 mb-1">Estimated Genre</p>
                    <p className="text-lg text-white capitalize">{analysisResults.estimated_genre}</p>
                  </div>
                )}
              </div>

              {/* Detailed Metrics */}
              <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700 mb-4">
                <p className="text-sm font-medium text-purple-400 mb-2">Voice Metrics</p>

                <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                  {/* Pitch */}
                  {analysisResults.pitch_mean && (
                    <div>
                      <p className="text-xs text-gray-400">Average Pitch</p>
                      <p className="text-sm text-white">{analysisResults.pitch_mean.toFixed(1)} Hz</p>
                    </div>
                  )}

                  {/* Pitch Range */}
                  {analysisResults.pitch_min && analysisResults.pitch_max && (
                    <div>
                      <p className="text-xs text-gray-400">Pitch Range</p>
                      <p className="text-sm text-white">
                        {analysisResults.pitch_min.toFixed(1)} - {analysisResults.pitch_max.toFixed(1)} Hz
                      </p>
                    </div>
                  )}

                  {/* Spectral Centroid */}
                  {analysisResults.spectral_centroid_mean && (
                    <div>
                      <p className="text-xs text-gray-400">Spectral Centroid</p>
                      <p className="text-sm text-white">{analysisResults.spectral_centroid_mean.toFixed(1)} Hz</p>
                    </div>
                  )}

                  {/* Sibilance */}
                  {analysisResults.sibilance && (
                    <div>
                      <p className="text-xs text-gray-400">Sibilance</p>
                      <p className="text-sm text-white">{analysisResults.sibilance.toFixed(2)}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Voice Characteristics Visualization */}
              <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700">
                <p className="text-sm font-medium text-purple-400 mb-2">Voice Characteristics</p>

                {/* Pitch Stability */}
                {analysisResults.pitch_std && (
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Pitch Stability</span>
                      <span>
                        {analysisResults.pitch_std < 10 ? 'Very Stable' :
                         analysisResults.pitch_std < 30 ? 'Stable' :
                         analysisResults.pitch_std < 50 ? 'Moderate' : 'Variable'}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-purple-500"
                        style={{ width: `${Math.min(100, analysisResults.pitch_std)}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Brightness */}
                {analysisResults.spectral_centroid_mean && (
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Brightness</span>
                      <span>
                        {analysisResults.spectral_centroid_mean < 2000 ? 'Dark' :
                         analysisResults.spectral_centroid_mean < 3000 ? 'Warm' :
                         analysisResults.spectral_centroid_mean < 4000 ? 'Bright' : 'Very Bright'}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-purple-500"
                        style={{ width: `${Math.min(100, analysisResults.spectral_centroid_mean / 50)}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Sibilance Level */}
                {analysisResults.sibilance && (
                  <div>
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>Sibilance Level</span>
                      <span>
                        {analysisResults.sibilance < 1.2 ? 'Low' :
                         analysisResults.sibilance < 1.6 ? 'Moderate' :
                         analysisResults.sibilance < 2.0 ? 'High' : 'Very High'}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-purple-500"
                        style={{ width: `${Math.min(100, analysisResults.sibilance * 50)}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Customize Preset Tab */}
      {activeTab === 'customize' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Preset Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-green-400">Select Preset to Customize</h3>

            {/* Genre Selection */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mb-4">
              {Object.keys(presets).map((genre) => (
                <button
                  key={genre}
                  onClick={() => setSelectedGenre(genre)}
                  className={`p-2 rounded-lg border ${
                    selectedGenre === genre
                      ? 'bg-green-600/50 border-green-500'
                      : 'bg-gray-700/30 border-gray-600 hover:bg-gray-700/50'
                  } transition-colors`}
                >
                  <div className="font-medium capitalize">{genre}</div>
                </button>
              ))}
            </div>

            {/* Preset Selection */}
            {selectedGenre && presets[selectedGenre] && (
              <div className="bg-gray-700/30 p-3 rounded-lg">
                <p className="text-sm font-medium text-green-400 mb-2">Available Presets</p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {presets[selectedGenre].map((preset) => (
                    <button
                      key={preset.id}
                      onClick={() => setSelectedPresetId(preset.id)}
                      className={`p-2 text-left rounded-lg border ${
                        selectedPresetId === preset.id
                          ? 'bg-green-600/20 border-green-500'
                          : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                      } transition-colors`}
                    >
                      <div className="font-medium text-white">{preset.name}</div>
                      <div className="text-xs text-gray-400 truncate">{preset.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Customize Button */}
          <div className="mb-6">
            <button
              onClick={handleCustomize}
              disabled={customizing || !audioFile || !selectedPresetId}
              className="w-full py-3 bg-green-600 hover:bg-green-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
            >
              {customizing ? 'Customizing Preset...' : 'Customize Preset for My Voice'}
            </button>
          </div>

          {/* Customization Results */}
          {customizedPreset && (
            <div className="bg-gray-700/30 p-4 rounded-lg">
              <h3 className="text-lg font-medium mb-3 text-green-400">Customized Preset</h3>

              <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700 mb-4">
                <p className="text-lg font-medium text-white">{customizedPreset.name}</p>
                <p className="text-gray-400 mb-2">{customizedPreset.description}</p>
              </div>

              {/* Key Adjustments */}
              <div className="bg-gray-800/50 p-3 rounded-lg border border-gray-700 mb-4">
                <p className="text-sm font-medium text-green-400 mb-2">Key Adjustments</p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {/* EQ Adjustments */}
                  {customizedPreset.eq && customizedPreset.eq.presence && (
                    <div className="bg-gray-700/30 p-2 rounded border border-gray-600">
                      <p className="text-xs font-medium text-white mb-1">EQ Presence</p>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <p className="text-sm text-gray-300">
                          {customizedPreset.eq.presence.gain > 0 ? '+' : ''}
                          {customizedPreset.eq.presence.gain} dB
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Compression Adjustments */}
                  {customizedPreset.compression && (
                    <div className="bg-gray-700/30 p-2 rounded border border-gray-600">
                      <p className="text-xs font-medium text-white mb-1">Compression</p>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <p className="text-sm text-gray-300">
                          Ratio: {customizedPreset.compression.ratio}:1
                        </p>
                      </div>
                    </div>
                  )}

                  {/* De-esser Adjustments */}
                  {customizedPreset.de_esser && (
                    <div className="bg-gray-700/30 p-2 rounded border border-gray-600">
                      <p className="text-xs font-medium text-white mb-1">De-esser</p>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <p className="text-sm text-gray-300">
                          Threshold: {customizedPreset.de_esser.threshold} dB
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Pitch Correction Adjustments */}
                  {customizedPreset.pitch_correction && (
                    <div className="bg-gray-700/30 p-2 rounded border border-gray-600">
                      <p className="text-xs font-medium text-white mb-1">Pitch Correction</p>
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <p className="text-sm text-gray-300">
                          Amount: {customizedPreset.pitch_correction.amount}%
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="mt-4">
                <button className="w-full py-2 bg-green-600 hover:bg-green-500 rounded-lg text-white font-medium transition-colors">
                  Apply Customized Preset
                </button>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
}