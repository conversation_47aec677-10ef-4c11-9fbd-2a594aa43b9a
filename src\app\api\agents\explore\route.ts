/**
 * EXPLORE Division API Route
 * Handles content discovery and curation requests
 * Part of the VINN Ecosystem - A Place In Time Entertainment
 */

import { NextRequest, NextResponse } from 'next/server';
import { ExploreDivision, ExploreRequest } from '@/lib/agents/explore/explore-division';

// Initialize the EXPLORE Division
const exploreDivision = new ExploreDivision();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data, userId } = body;

    console.log('[EXPLORE_API] Received request:', { action, userId });

    switch (action) {
      case 'discover':
        return await handleDiscover(data, userId);

      case 'trending':
        return await handleTrending(data, userId);

      case 'personalized':
        return await handlePersonalized(data, userId);

      case 'curated':
        return await handleCurated(data, userId);

      case 'playlist':
        return await handlePlaylist(data, userId);

      case 'get_status':
        return await handleGetStatus();

      case 'get_agent_info':
        return await handleGetAgentInfo();

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('[EXPLORE_API] Error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId') || undefined;

    switch (action) {
      case 'status':
        return await handleGetStatus();

      case 'agent_info':
        return await handleGetAgentInfo();

      case 'capabilities':
        return await handleGetCapabilities();

      case 'trending':
        return await handleTrending({}, userId);

      case 'discover':
        return await handleDiscover({}, userId);

      default:
        return NextResponse.json(
          { success: false, error: 'Action parameter required' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('[EXPLORE_API] GET Error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

async function handleDiscover(data: any, userId?: string) {
  try {
    const exploreRequest: ExploreRequest = {
      userId,
      exploreMode: 'discover',
      parameters: {
        contentTypes: data.contentTypes || ['music', 'lyrics'],
        genres: data.genres,
        moods: data.moods,
        writers: data.writers,
        limit: data.limit || 20,
        diversityLevel: data.diversityLevel || 0.8,
        freshness: data.freshness || 0.7,
      },
      filters: {
        qualityThreshold: data.qualityThreshold || 70,
        excludeContent: data.excludeContent,
        includeExplicit: data.includeExplicit !== false,
      },
      context: {
        deviceType: data.deviceType,
        timeOfDay: data.timeOfDay,
      },
    };

    const result = await exploreDivision.processTask(exploreRequest);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      processingTime: result.processingTime,
      quality: result.quality,
      metadata: result.metadata,
    });
  } catch (error) {
    console.error('[EXPLORE_API] Discover error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Discovery failed'
      },
      { status: 500 }
    );
  }
}

async function handleTrending(data: any, userId?: string) {
  try {
    const exploreRequest: ExploreRequest = {
      userId,
      exploreMode: 'trending',
      parameters: {
        contentTypes: data.contentTypes || ['music', 'lyrics'],
        genres: data.genres,
        moods: data.moods,
        limit: data.limit || 15,
        diversityLevel: data.diversityLevel || 0.6,
      },
      filters: {
        qualityThreshold: data.qualityThreshold || 60,
        dateRange: data.dateRange,
      },
    };

    const result = await exploreDivision.processTask(exploreRequest);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      processingTime: result.processingTime,
      quality: result.quality,
      metadata: result.metadata,
    });
  } catch (error) {
    console.error('[EXPLORE_API] Trending error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Trending analysis failed'
      },
      { status: 500 }
    );
  }
}

async function handlePersonalized(data: any, userId?: string) {
  try {
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID required for personalized content' },
        { status: 400 }
      );
    }

    const exploreRequest: ExploreRequest = {
      userId,
      exploreMode: 'personalized',
      parameters: {
        contentTypes: data.contentTypes || ['music', 'lyrics'],
        limit: data.limit || 20,
        diversityLevel: data.diversityLevel || 0.5,
      },
      filters: {
        qualityThreshold: data.qualityThreshold || 65,
        excludeContent: data.excludeContent,
      },
      context: {
        recentActivity: data.recentActivity,
        currentSession: data.sessionId,
        deviceType: data.deviceType,
      },
    };

    const result = await exploreDivision.processTask(exploreRequest);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      processingTime: result.processingTime,
      quality: result.quality,
      metadata: result.metadata,
    });
  } catch (error) {
    console.error('[EXPLORE_API] Personalized error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Personalized content failed'
      },
      { status: 500 }
    );
  }
}

async function handleCurated(data: any, userId?: string) {
  try {
    const exploreRequest: ExploreRequest = {
      userId,
      exploreMode: 'curated',
      parameters: {
        contentTypes: data.contentTypes || ['music', 'lyrics', 'playlists'],
        genres: data.genres,
        moods: data.moods,
        writers: data.writers,
        limit: data.limit || 25,
        diversityLevel: data.diversityLevel || 0.7,
      },
      filters: {
        qualityThreshold: data.qualityThreshold || 75,
        excludeContent: data.excludeContent,
      },
    };

    const result = await exploreDivision.processTask(exploreRequest);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      processingTime: result.processingTime,
      quality: result.quality,
      metadata: result.metadata,
    });
  } catch (error) {
    console.error('[EXPLORE_API] Curated error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Curated content failed'
      },
      { status: 500 }
    );
  }
}

async function handlePlaylist(data: any, userId?: string) {
  try {
    const exploreRequest: ExploreRequest = {
      userId,
      exploreMode: 'playlist',
      parameters: {
        genres: data.genres,
        moods: data.moods,
        writers: data.writers,
        limit: data.trackLimit || 15,
      },
      context: {
        timeOfDay: data.timeOfDay,
        deviceType: data.deviceType,
      },
    };

    const result = await exploreDivision.processTask(exploreRequest);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      processingTime: result.processingTime,
      quality: result.quality,
      metadata: result.metadata,
    });
  } catch (error) {
    console.error('[EXPLORE_API] Playlist error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Playlist creation failed'
      },
      { status: 500 }
    );
  }
}

async function handleGetStatus() {
  try {
    const status = exploreDivision.getStatus();

    return NextResponse.json({
      success: true,
      data: {
        division: 'EXPLORE',
        status: status,
        agents: {
          content_curator: 'active',
          trend_analyzer: 'active',
          recommendation_engine: 'active',
          playlist_architect: 'active',
        },
        capabilities: [
          'content_discovery',
          'trend_analysis',
          'personalized_recommendations',
          'playlist_creation',
          'market_intelligence',
          'user_behavior_analysis',
        ],
        supportedModes: [
          'discover',
          'trending',
          'personalized',
          'curated',
          'playlist',
        ],
      },
    });
  } catch (error) {
    console.error('[EXPLORE_API] Get status error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Status retrieval failed'
      },
      { status: 500 }
    );
  }
}

async function handleGetAgentInfo() {
  try {
    const agentInfo = exploreDivision.getStatus();
    const specialization = exploreDivision.getSpecialization();

    return NextResponse.json({
      success: true,
      data: {
        agentInfo,
        specialization,
        division: 'EXPLORE',
        description: 'Content discovery and curation system',
        agents: {
          content_curator: {
            name: 'CONTENT_CURATOR',
            description: 'Music discovery and intelligent content curation',
            capabilities: ['content_filtering', 'quality_assessment', 'diversity_optimization'],
          },
          trend_analyzer: {
            name: 'TREND_ANALYZER',
            description: 'Market trend analysis and prediction',
            capabilities: ['trend_detection', 'predictive_analytics', 'market_intelligence'],
          },
          recommendation_engine: {
            name: 'RECOMMENDATION_ENGINE',
            description: 'Personalized content recommendations',
            capabilities: ['collaborative_filtering', 'personalization', 'user_modeling'],
          },
          playlist_architect: {
            name: 'PLAYLIST_ARCHITECT',
            description: 'Intelligent playlist creation and curation',
            capabilities: ['playlist_optimization', 'flow_analysis', 'mood_sequencing'],
          },
        },
      },
    });
  } catch (error) {
    console.error('[EXPLORE_API] Get agent info error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Agent info retrieval failed'
      },
      { status: 500 }
    );
  }
}

async function handleGetCapabilities() {
  try {
    const specialization = exploreDivision.getSpecialization();

    return NextResponse.json({
      success: true,
      data: {
        division: 'EXPLORE',
        primaryFocus: specialization.primaryFocus,
        expertiseAreas: specialization.expertiseAreas,
        collaborationModes: specialization.collaborationModes,
        supportedInputs: [
          'user_preferences',
          'content_filters',
          'exploration_parameters',
          'context_data',
        ],
        supportedOutputs: [
          'curated_content',
          'trending_insights',
          'personalized_recommendations',
          'intelligent_playlists',
          'market_analysis',
          'discovery_paths',
        ],
        explorationModes: [
          'discover',
          'trending',
          'personalized',
          'curated',
          'playlist',
        ],
        contentTypes: [
          'music',
          'lyrics',
          'playlists',
          'artists',
          'genres',
        ],
      },
    });
  } catch (error) {
    console.error('[EXPLORE_API] Get capabilities error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Capabilities retrieval failed'
      },
      { status: 500 }
    );
  }
}
