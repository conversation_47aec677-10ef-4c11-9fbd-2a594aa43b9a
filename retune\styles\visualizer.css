/* Styles for the audio visualizer */

.visualizer-container {
  width: 100%;
  height: 200px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin-bottom: 2rem;
}

.waveform-visualizer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.waveform-bar {
  background: linear-gradient(to top, #1890ff, #36cfc9);
  width: 4px;
  height: 5%;
  border-radius: 2px;
  transition: height 0.1s ease;
}

.spectrum-visualizer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 0 10px;
}

.spectrum-bar {
  background: linear-gradient(to top, #ff4d4f, #ff7a45);
  width: 8px;
  height: 5%;
  border-radius: 4px 4px 0 0;
  transition: height 0.1s ease;
}

.visualizer-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 1.2rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.visualizer-overlay.visible {
  opacity: 1;
}

.visualizer-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.visualizer-button {
  background: none;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.visualizer-button:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.visualizer-button.active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

/* Time axis */
.time-axis {
  width: 100%;
  height: 20px;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  font-size: 0.8rem;
  color: #999;
}

.time-marker {
  position: relative;
  width: 1px;
}

.time-marker::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 0;
  width: 1px;
  height: 5px;
  background-color: #d9d9d9;
}

.time-label {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

/* Responsive styles */
@media (max-width: 768px) {
  .visualizer-container {
    height: 150px;
  }
  
  .waveform-bar {
    width: 3px;
  }
  
  .spectrum-bar {
    width: 6px;
  }
  
  .time-axis {
    font-size: 0.7rem;
  }
}
