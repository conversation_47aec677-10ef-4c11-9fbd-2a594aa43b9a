'use client';

import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { EmotionType, ToneType } from '@/utils/emotionDetection';
import { ThemeButton, ThemeCard, ThemeHeading, ThemeSelect, ThemeSlider, ThemeToggle } from '@/components/ui';

interface EmotionDetectionProps {
  onEmotionDetected?: (emotions: any) => void;
  onEmotionAdjusted?: (audioUrl: string) => void;
}

const EmotionDetection: React.FC<EmotionDetectionProps> = ({ 
  onEmotionDetected, 
  onEmotionAdjusted 
}) => {
  // State
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [detectedEmotions, setDetectedEmotions] = useState<any>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [adjustedAudioUrl, setAdjustedAudioUrl] = useState<string | null>(null);
  const [targetEmotion, setTargetEmotion] = useState<EmotionType>(EmotionType.CONFIDENT);
  const [emotionIntensity, setEmotionIntensity] = useState<number>(50);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  
  // Advanced options
  const [includeConfidenceScores, setIncludeConfidenceScores] = useState(true);
  const [includeTimestamps, setIncludeTimestamps] = useState(false);
  const [enhanceSensitivity, setEnhanceSensitivity] = useState(false);
  const [language, setLanguage] = useState<string>('en');
  
  // Refs
  const audioInputRef = useRef<HTMLInputElement>(null);
  const audioPlayerRef = useRef<HTMLAudioElement>(null);
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setAudioFile(file);
      
      // Create URL for audio preview
      const url = URL.createObjectURL(file);
      setAudioUrl(url);
      
      // Reset previous results
      setDetectedEmotions(null);
      setAdjustedAudioUrl(null);
    }
  };
  
  // Handle emotion detection
  const handleDetectEmotions = async () => {
    if (!audioFile) {
      toast.error('Please select an audio file');
      return;
    }
    
    try {
      setIsProcessing(true);
      const toastId = toast.loading('Detecting emotions...');
      
      // Create form data
      const formData = new FormData();
      formData.append('audio', audioFile);
      
      // Add options
      formData.append('includeConfidenceScores', includeConfidenceScores.toString());
      formData.append('includeTimestamps', includeTimestamps.toString());
      formData.append('enhanceSensitivity', enhanceSensitivity.toString());
      formData.append('language', language);
      
      // Send request
      const response = await fetch('/api/retune/emotion/detect', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to detect emotions');
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to detect emotions');
      }
      
      // Update state with detected emotions
      setDetectedEmotions(data);
      
      toast.success('Emotions detected successfully', { id: toastId });
      
      // Call callback if provided
      if (onEmotionDetected) {
        onEmotionDetected(data);
      }
    } catch (error) {
      console.error('Error detecting emotions:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to detect emotions');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle emotion adjustment
  const handleAdjustEmotions = async () => {
    if (!audioFile) {
      toast.error('Please select an audio file');
      return;
    }
    
    try {
      setIsProcessing(true);
      const toastId = toast.loading('Adjusting emotions...');
      
      // Create form data
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('targetEmotion', targetEmotion);
      formData.append('intensity', emotionIntensity.toString());
      
      // Send request
      const response = await fetch('/api/retune/emotion/adjust', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to adjust emotions');
      }
      
      // The response is the audio blob
      const audioBlob = await response.blob();
      const url = URL.createObjectURL(audioBlob);
      setAdjustedAudioUrl(url);
      
      toast.success('Emotions adjusted successfully', { id: toastId });
      
      // Call callback if provided
      if (onEmotionAdjusted) {
        onEmotionAdjusted(url);
      }
    } catch (error) {
      console.error('Error adjusting emotions:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to adjust emotions');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) URL.revokeObjectURL(audioUrl);
      if (adjustedAudioUrl) URL.revokeObjectURL(adjustedAudioUrl);
    };
  }, [audioUrl, adjustedAudioUrl]);
  
  // Render emotion score bar
  const renderEmotionScore = (emotion: string, score: number) => (
    <div key={emotion} className="mb-2">
      <div className="flex justify-between items-center mb-1">
        <span className="text-sm capitalize">{emotion}</span>
        <span className="text-xs font-medium">{Math.round(score * 100)}%</span>
      </div>
      <div className="w-full bg-black/30 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-[#00FFEE]/70 to-[#C600FF]/70 h-2 rounded-full"
          style={{ width: `${score * 100}%` }}
        ></div>
      </div>
    </div>
  );
  
  return (
    <ThemeCard className="p-6">
      <ThemeHeading level={3} className="mb-4">Imentiv AI Emotion Detection</ThemeHeading>
      
      <div className="mb-6">
        <p className="text-sm text-gray-300 mb-4">
          Analyze and adjust the emotional tone of your voice recordings using advanced AI technology.
          Detect emotions, analyze voice quality, and enhance specific emotional qualities.
        </p>
        
        {/* Audio Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Select Audio File</label>
          <input
            type="file"
            ref={audioInputRef}
            onChange={handleFileChange}
            accept="audio/*"
            className="hidden"
          />
          <div className="flex items-center gap-3">
            <ThemeButton
              onClick={() => audioInputRef.current?.click()}
              disabled={isProcessing}
              variant="secondary"
            >
              Browse Files
            </ThemeButton>
            <span className="text-sm truncate">
              {audioFile ? audioFile.name : 'No file selected'}
            </span>
          </div>
        </div>
        
        {/* Audio Player */}
        {audioUrl && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Preview Audio</label>
            <audio
              controls
              src={audioUrl}
              className="w-full"
            ></audio>
          </div>
        )}
        
        {/* Advanced Options Toggle */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Show Advanced Options</label>
            <ThemeToggle
              checked={showAdvancedOptions}
              onChange={setShowAdvancedOptions}
            />
          </div>
        </div>
        
        {/* Advanced Options */}
        {showAdvancedOptions && (
          <div className="mb-4 p-4 border border-gray-700 rounded-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Language</label>
                <ThemeSelect
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  disabled={isProcessing}
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                  <option value="ja">Japanese</option>
                  <option value="ko">Korean</option>
                  <option value="zh">Chinese</option>
                </ThemeSelect>
              </div>
              
              <div className="flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Include Confidence Scores</label>
                  <ThemeToggle
                    checked={includeConfidenceScores}
                    onChange={setIncludeConfidenceScores}
                    disabled={isProcessing}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Include Timestamps</label>
                  <ThemeToggle
                    checked={includeTimestamps}
                    onChange={setIncludeTimestamps}
                    disabled={isProcessing}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Enhance Sensitivity</label>
                  <ThemeToggle
                    checked={enhanceSensitivity}
                    onChange={setEnhanceSensitivity}
                    disabled={isProcessing}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <ThemeButton
            onClick={handleDetectEmotions}
            disabled={!audioFile || isProcessing}
            className="flex-1"
          >
            {isProcessing ? 'Processing...' : 'Detect Emotions'}
          </ThemeButton>
          
          <ThemeButton
            onClick={handleAdjustEmotions}
            disabled={!audioFile || isProcessing}
            className="flex-1"
            variant="secondary"
          >
            {isProcessing ? 'Processing...' : 'Adjust Emotions'}
          </ThemeButton>
        </div>
      </div>
      
      {/* Results Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Emotion Detection Results */}
        {detectedEmotions && (
          <div className="p-4 bg-black/30 rounded-lg border border-[#00FFEE]/20">
            <h4 className="text-lg font-medium mb-3 text-[#00FFEE]">Detected Emotions</h4>
            
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-3 h-3 rounded-full bg-[#00FFEE]"></div>
                <span className="text-sm font-medium">
                  Dominant Emotion: <span className="capitalize text-[#00FFEE]">{detectedEmotions.emotions.dominant}</span>
                </span>
              </div>
              
              <div className="space-y-1">
                {Object.entries(detectedEmotions.emotions.scores).map(([emotion, score]) => 
                  renderEmotionScore(emotion, score as number)
                )}
              </div>
            </div>
            
            {detectedEmotions.tones && (
              <div className="mt-4">
                <h5 className="text-sm font-medium mb-2 text-[#C600FF]">Detected Tones</h5>
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-3 h-3 rounded-full bg-[#C600FF]"></div>
                  <span className="text-sm font-medium">
                    Dominant Tone: <span className="capitalize text-[#C600FF]">{detectedEmotions.tones.dominant}</span>
                  </span>
                </div>
                
                <div className="space-y-1">
                  {Object.entries(detectedEmotions.tones.scores).map(([tone, score]) => 
                    renderEmotionScore(tone, score as number)
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Emotion Adjustment Controls */}
        <div className="p-4 bg-black/30 rounded-lg border border-[#C600FF]/20">
          <h4 className="text-lg font-medium mb-3 text-[#C600FF]">Emotion Adjustment</h4>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Target Emotion</label>
            <ThemeSelect
              value={targetEmotion}
              onChange={(e) => setTargetEmotion(e.target.value as EmotionType)}
              disabled={isProcessing}
            >
              <option value={EmotionType.HAPPY}>Happy</option>
              <option value={EmotionType.SAD}>Sad</option>
              <option value={EmotionType.ANGRY}>Angry</option>
              <option value={EmotionType.FEARFUL}>Fearful</option>
              <option value={EmotionType.DISGUSTED}>Disgusted</option>
              <option value={EmotionType.SURPRISED}>Surprised</option>
              <option value={EmotionType.NEUTRAL}>Neutral</option>
              <option value={EmotionType.CONFIDENT}>Confident</option>
              <option value={EmotionType.PASSIONATE}>Passionate</option>
              <option value={EmotionType.ENERGETIC}>Energetic</option>
              <option value={EmotionType.CALM}>Calm</option>
              <option value={EmotionType.NOSTALGIC}>Nostalgic</option>
            </ThemeSelect>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              Intensity: {emotionIntensity}%
            </label>
            <ThemeSlider
              min={0}
              max={100}
              step={5}
              value={emotionIntensity}
              onChange={(e) => setEmotionIntensity(parseInt(e.target.value))}
              disabled={isProcessing}
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>Subtle</span>
              <span>Strong</span>
            </div>
          </div>
          
          {/* Adjusted Audio Player */}
          {adjustedAudioUrl && (
            <div className="mt-4">
              <label className="block text-sm font-medium mb-2">Adjusted Audio</label>
              <audio
                controls
                src={adjustedAudioUrl}
                className="w-full"
              ></audio>
              <div className="flex justify-end mt-2">
                <ThemeButton
                  onClick={() => {
                    const a = document.createElement('a');
                    a.href = adjustedAudioUrl;
                    a.download = `emotion_adjusted_${audioFile?.name || 'audio'}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }}
                  variant="secondary"
                  size="sm"
                >
                  Download
                </ThemeButton>
              </div>
            </div>
          )}
        </div>
      </div>
    </ThemeCard>
  );
};

export default EmotionDetection;
