import { NextRequest, NextResponse } from 'next/server';

/**
 * Simple API endpoint to check if a model is available
 * Always returns true to avoid API calls that might fail
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ modelId: string }> }
) {
  // Always return true for all models to avoid API calls
  // This simplifies the implementation and avoids potential errors

  // Ensure params is properly handled for Next.js dynamic routes
  const params = await context.params;
  const modelId = params.modelId;

  return NextResponse.json({
    isAvailable: true,
    model: modelId,
    status: 'operational'
  });
}
