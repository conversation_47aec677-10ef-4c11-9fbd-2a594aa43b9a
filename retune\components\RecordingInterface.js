import React, { useState, useEffect, useRef } from 'react';
import AudioVisualizer from './AudioVisualizer';
import VoiceAnalysisDisplay from './VoiceAnalysisDisplay';
import EnhancementControls from './EnhancementControls';
import { 
  createAudioContext, 
  requestMicrophoneAccess, 
  startRecording,
  audioChunksToWav,
  createAudioBlob,
  createAudioUrl
} from '../utils/audioUtils';
import { 
  saveRecording,
  getRecordings,
  loadRecording,
  exportRecording
} from '../utils/storageUtils';
import {
  analyzeRecording,
  enhanceRecording
} from '../utils/apiUtils';
import '../styles/recording.css';

const RecordingInterface = () => {
  // State for recording
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioContext, setAudioContext] = useState(null);
  const [stream, setStream] = useState(null);
  const [audioUrl, setAudioUrl] = useState(null);
  const [audioBlob, setAudioBlob] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasRecording, setHasRecording] = useState(false);
  
  // State for analysis and enhancement
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancedAudioUrl, setEnhancedAudioUrl] = useState(null);
  const [enhancedAudioBlob, setEnhancedAudioBlob] = useState(null);
  const [isComparingAudio, setIsComparingAudio] = useState(false);
  const [currentAudio, setCurrentAudio] = useState('original'); // 'original' or 'enhanced'
  
  // Refs
  const recorderRef = useRef(null);
  const timerRef = useRef(null);
  const audioPlayerRef = useRef(null);
  
  // Initialize audio context
  useEffect(() => {
    const initAudio = async () => {
      try {
        const context = createAudioContext();
        setAudioContext(context);
      } catch (error) {
        console.error('Error initializing audio context:', error);
        alert('Could not initialize audio. Please check your browser permissions.');
      }
    };
    
    initAudio();
    
    return () => {
      if (audioContext) {
        audioContext.close();
      }
    };
  }, []);
  
  // Handle recording timer
  useEffect(() => {
    if (isRecording) {
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }
    
    return () => {
      clearInterval(timerRef.current);
    };
  }, [isRecording]);
  
  // Handle audio playback
  useEffect(() => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.onended = () => {
        setIsPlaying(false);
      };
    }
  }, [audioUrl, enhancedAudioUrl]);
  
  // Start recording
  const handleStartRecording = async () => {
    if (!audioContext) {
      alert('Audio context not initialized. Please refresh the page.');
      return;
    }
    
    try {
      // Request microphone access
      const { success, stream: micStream, error } = await requestMicrophoneAccess();
      
      if (!success) {
        alert(`Could not access microphone: ${error.message}`);
        return;
      }
      
      setStream(micStream);
      
      // Start recording
      const recorder = startRecording(micStream, audioContext);
      recorderRef.current = recorder;
      
      setIsRecording(true);
      setRecordingTime(0);
      setAudioUrl(null);
      setAudioBlob(null);
      setHasRecording(false);
      setAnalysisData(null);
      setEnhancedAudioUrl(null);
      setEnhancedAudioBlob(null);
      
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not start recording. Please check your browser permissions.');
    }
  };
  
  // Stop recording
  const handleStopRecording = async () => {
    if (!recorderRef.current) return;
    
    try {
      // Stop recording and get audio chunks
      const chunks = recorderRef.current.stop();
      
      // Convert chunks to WAV
      const wavBuffer = audioChunksToWav(chunks, audioContext.sampleRate);
      
      // Create blob and URL
      const blob = createAudioBlob(wavBuffer);
      const url = createAudioUrl(blob);
      
      setAudioBlob(blob);
      setAudioUrl(url);
      setHasRecording(true);
      setIsRecording(false);
      
      // Automatically analyze the recording
      handleAnalyzeRecording(blob);
      
    } catch (error) {
      console.error('Error stopping recording:', error);
      alert('Could not process recording.');
      setIsRecording(false);
    }
  };
  
  // Play/pause audio
  const handlePlayAudio = () => {
    if (!audioPlayerRef.current) return;
    
    const url = currentAudio === 'original' ? audioUrl : enhancedAudioUrl;
    
    if (!url) {
      alert('No recording available to play.');
      return;
    }
    
    if (isPlaying) {
      audioPlayerRef.current.pause();
      setIsPlaying(false);
    } else {
      audioPlayerRef.current.src = url;
      audioPlayerRef.current.play();
      setIsPlaying(true);
    }
  };
  
  // Analyze recording
  const handleAnalyzeRecording = async (blob) => {
    if (!blob) {
      alert('No recording available to analyze.');
      return;
    }
    
    setIsAnalyzing(true);
    
    try {
      const { success, data, error } = await analyzeRecording(blob);
      
      if (!success) {
        throw new Error(error);
      }
      
      setAnalysisData(data);
    } catch (error) {
      console.error('Error analyzing recording:', error);
      alert('Could not analyze recording. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // Apply enhancement
  const handleApplyEnhancement = async (parameters) => {
    if (!audioBlob) {
      alert('No recording available to enhance.');
      return;
    }
    
    setIsEnhancing(true);
    
    try {
      const { success, enhancedAudio, url, error } = await enhanceRecording(audioBlob, parameters);
      
      if (!success) {
        throw new Error(error);
      }
      
      setEnhancedAudioBlob(enhancedAudio);
      setEnhancedAudioUrl(url);
      setCurrentAudio('enhanced');
      
      // Automatically play the enhanced audio
      setTimeout(() => {
        if (audioPlayerRef.current) {
          audioPlayerRef.current.src = url;
          audioPlayerRef.current.play();
          setIsPlaying(true);
        }
      }, 500);
      
    } catch (error) {
      console.error('Error enhancing recording:', error);
      alert('Could not enhance recording. Please try again.');
    } finally {
      setIsEnhancing(false);
    }
  };
  
  // Toggle between original and enhanced audio
  const handleToggleAudio = () => {
    if (!audioUrl || !enhancedAudioUrl) {
      alert('Both original and enhanced recordings must be available to compare.');
      return;
    }
    
    const newAudio = currentAudio === 'original' ? 'enhanced' : 'original';
    setCurrentAudio(newAudio);
    
    if (isPlaying) {
      if (audioPlayerRef.current) {
        audioPlayerRef.current.src = newAudio === 'original' ? audioUrl : enhancedAudioUrl;
        audioPlayerRef.current.play();
      }
    }
  };
  
  // Save recording
  const handleSaveRecording = async () => {
    if (!audioBlob) {
      alert('No recording available to save.');
      return;
    }
    
    try {
      const fileName = `recording_${new Date().toISOString().replace(/[:.]/g, '-')}.wav`;
      const { success, error } = await saveRecording(audioBlob, fileName);
      
      if (!success) {
        throw new Error(error);
      }
      
      alert('Recording saved successfully!');
    } catch (error) {
      console.error('Error saving recording:', error);
      alert('Could not save recording. Please try again.');
    }
  };
  
  // Export recording
  const handleExportRecording = () => {
    if (!audioBlob && !enhancedAudioBlob) {
      alert('No recording available to export.');
      return;
    }
    
    const blob = currentAudio === 'original' ? audioBlob : enhancedAudioBlob;
    const fileName = `retune_${currentAudio}_${new Date().toISOString().replace(/[:.]/g, '-')}.wav`;
    
    exportRecording(fileName, blob);
  };
  
  // Format recording time
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="recording-container">
      <div className="recording-header">
        <h2>Retune Voice Enhancement</h2>
        <p>Record your voice and let our AI enhance its best qualities</p>
      </div>
      
      <AudioVisualizer 
        audioContext={audioContext}
        stream={stream}
        isRecording={isRecording}
        audioUrl={currentAudio === 'original' ? audioUrl : enhancedAudioUrl}
        isPlaying={isPlaying}
      />
      
      <div className="recording-status">
        {isRecording ? (
          <>
            <span className="recording-indicator">Recording</span>
            <span className="recording-timer">{formatTime(recordingTime)}</span>
          </>
        ) : hasRecording ? (
          <span>
            {currentAudio === 'original' ? 'Original Recording' : 'Enhanced Recording'}
          </span>
        ) : (
          <span>Ready to Record</span>
        )}
      </div>
      
      <div className="recording-controls">
        {!isRecording ? (
          <button 
            className="recording-button record-button"
            onClick={handleStartRecording}
            disabled={isAnalyzing || isEnhancing}
          >
            Record
          </button>
        ) : (
          <button 
            className="recording-button stop-button"
            onClick={handleStopRecording}
          >
            Stop
          </button>
        )}
        
        {hasRecording && (
          <button 
            className="recording-button play-button"
            onClick={handlePlayAudio}
            disabled={isAnalyzing || isEnhancing}
          >
            {isPlaying ? 'Pause' : 'Play'}
          </button>
        )}
        
        {enhancedAudioUrl && (
          <button 
            className="recording-button toggle-button"
            onClick={handleToggleAudio}
            disabled={isAnalyzing || isEnhancing}
          >
            Switch to {currentAudio === 'original' ? 'Enhanced' : 'Original'}
          </button>
        )}
      </div>
      
      {hasRecording && (
        <div className="recording-actions">
          <button 
            className="action-button"
            onClick={handleSaveRecording}
            disabled={isAnalyzing || isEnhancing}
          >
            Save Recording
          </button>
          
          <button 
            className="action-button"
            onClick={handleExportRecording}
            disabled={isAnalyzing || isEnhancing || (!audioBlob && !enhancedAudioBlob)}
          >
            Export {currentAudio === 'original' ? 'Original' : 'Enhanced'}
          </button>
        </div>
      )}
      
      {/* Hidden audio player for playback */}
      <audio ref={audioPlayerRef} style={{ display: 'none' }} />
      
      {hasRecording && !isAnalyzing && analysisData && (
        <div className="analysis-enhancement-container">
          <div className="analysis-section">
            <VoiceAnalysisDisplay 
              analysisData={analysisData}
              isLoading={isAnalyzing}
            />
          </div>
          
          <div className="enhancement-section">
            <EnhancementControls 
              onApplyEnhancement={handleApplyEnhancement}
              isProcessing={isEnhancing}
            />
          </div>
        </div>
      )}
      
      {!hasRecording && (
        <div className="recording-guide">
          <h3>Tips for Best Results</h3>
          <ul>
            <li>Find a quiet environment with minimal background noise</li>
            <li>Position yourself 6-12 inches from your microphone</li>
            <li>Speak clearly and at a consistent volume</li>
            <li>Try reading a paragraph of text for a good sample</li>
            <li>For best results, record at least 10-15 seconds of audio</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default RecordingInterface;
