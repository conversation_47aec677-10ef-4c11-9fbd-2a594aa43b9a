'use client';

import { ThemeHeading } from '@/components/ui';
import React from 'react';

interface VoiceProfile {
  id: string;
  name: string;
  confidence: number;
  vocalRange: number;
  emotionalExpression: number;
  clarity: number;
  baseModel: string | null;
  createdAt: string;
  samples: string[];
  qualityScore: number;
}

interface SuperhumanContextPanelProps {
  currentStep: 1 | 2 | 3;
  voiceProfile: VoiceProfile | null;
  enhancementModel?: 'rvc' | 'bark' | 'autovc';
  applicationMode?: 'tts' | 'enhance' | 'collaborate';
}

const SuperhumanContextPanel: React.FC<SuperhumanContextPanelProps> = ({
  currentStep,
  voiceProfile,
  enhancementModel,
  applicationMode
}) => {
  // Render content based on current step
  const renderContent = () => {
    switch (currentStep) {
      case 1:
        return renderStep1Content();
      case 2:
        return renderStep2Content();
      case 3:
        return renderStep3Content();
      default:
        return renderStep1Content();
    }
  };

  // Step 1: Voice Input & Profile Creation
  const renderStep1Content = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4 retune-text-glow-cyan">Voice Profile Tips</ThemeHeading>

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Recording Best Practices</h4>
          <ul className="text-xs text-white/80 space-y-2">
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Record in a quiet environment with minimal background noise</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Use a good quality microphone if available</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Maintain consistent distance from the microphone</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Include a variety of vocal expressions and tones</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Aim for 30-60 seconds of clear speech or singing</span>
            </li>
          </ul>
        </div>

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Sample Quality</h4>
          <p className="text-xs text-white/80 mb-3">
            Higher quality samples lead to better voice profiles. For optimal results:
          </p>
          <ul className="text-xs text-white/80 space-y-2">
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Upload at least 3 different samples</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Include both speaking and singing if possible</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Use WAV format for highest quality</span>
            </li>
          </ul>
        </div>

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Voice Parameters Explained</h4>
          <div className="space-y-3">
            <div>
              <p className="text-xs font-medium text-white/90">Confidence</p>
              <p className="text-xs text-white/70">
                Controls how assertive and bold your voice sounds. Higher values create a more commanding presence.
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-white/90">Vocal Range</p>
              <p className="text-xs text-white/70">
                Determines the pitch range of your voice. Higher values allow for more dynamic performances.
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-white/90">Emotional Expression</p>
              <p className="text-xs text-white/70">
                Controls how much emotion comes through in your voice. Higher values create more expressive performances.
              </p>
            </div>
            <div>
              <p className="text-xs font-medium text-white/90">Clarity/Articulation</p>
              <p className="text-xs text-white/70">
                Affects how clearly words are pronounced. Higher values improve intelligibility.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Step 2: Voice Enhancement Engine
  const renderStep2Content = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4 retune-text-glow-cyan">Enhancement Details</ThemeHeading>

        {voiceProfile && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Voice Profile Analysis</h4>
            <div className="retune-glass-container p-3 mb-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-white/80">Quality Score</span>
                <span className={`text-xs font-medium ${voiceProfile.qualityScore >= 85 ? 'text-[#00FFEE]' :
                    voiceProfile.qualityScore >= 70 ? 'text-yellow-300' :
                      'text-red-400'
                  }`}>
                  {voiceProfile.qualityScore}%
                </span>
              </div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs text-white/80">Samples</span>
                <span className="text-xs font-medium text-white">{voiceProfile.samples.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-white/80">Base Model</span>
                <span className="text-xs font-medium text-white">{voiceProfile.baseModel || 'None'}</span>
              </div>
            </div>
            <p className="text-xs text-white/70">
              {voiceProfile.qualityScore >= 85
                ? 'Your voice profile is excellent and will produce high-quality results.'
                : voiceProfile.qualityScore >= 70
                  ? 'Your voice profile is good. Enhancement will improve the quality.'
                  : 'Your voice profile needs improvement. Enhancement will help, but consider adding more samples.'}
            </p>
          </div>
        )}

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Enhancement Models</h4>
          <div className="space-y-3">
            <div className={enhancementModel === 'rvc' ? 'retune-glass-container p-3' : ''}>
              <p className="text-xs font-medium text-white/90">RVC (Retrieval-based Voice Conversion)</p>
              <p className="text-xs text-white/70">
                A state-of-the-art voice conversion model that preserves the nuances of your voice while enhancing quality. Best for maintaining your unique vocal characteristics.
              </p>
            </div>
            <div className={enhancementModel === 'bark' ? 'retune-glass-container p-3' : ''}>
              <p className="text-xs font-medium text-white/90">Bark</p>
              <p className="text-xs text-white/70">
                A powerful text-to-audio model that excels at generating expressive speech and singing. Best for creating highly emotional performances.
              </p>
            </div>
            <div className={enhancementModel === 'autovc' ? 'retune-glass-container p-3' : ''}>
              <p className="text-xs font-medium text-white/90">AutoVC</p>
              <p className="text-xs text-white/70">
                A zero-shot voice conversion model that offers fast processing with good quality. Best for quick results with decent quality.
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Enhancement Process</h4>
          <p className="text-xs text-white/80 mb-3">
            The enhancement process involves several steps:
          </p>
          <ol className="text-xs text-white/80 space-y-2 list-decimal pl-4">
            <li>Analyzing your voice profile characteristics</li>
            <li>Applying the selected enhancement model</li>
            <li>Integrating reference voice characteristics (if selected)</li>
            <li>Applying pitch correction and tone enhancement</li>
            <li>Fine-tuning emotional expression and clarity</li>
            <li>Generating the enhanced voice profile</li>
          </ol>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Before/After Comparison</h4>
          <div className="retune-glass-container p-3">
            <p className="text-xs text-white/80 mb-2">
              Enhancement typically improves these aspects:
            </p>
            <div className="space-y-2">
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span>Tone Quality</span>
                  <span>+30-40%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-white/60 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-[#00FFEE]/70 rounded-full" style={{ width: '90%' }}></div>
                  </div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span>Clarity</span>
                  <span>+20-30%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-white/60 rounded-full" style={{ width: '70%' }}></div>
                  </div>
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-[#00FFEE]/70 rounded-full" style={{ width: '95%' }}></div>
                  </div>
                </div>
              </div>
              <div>
                <div className="flex justify-between text-xs mb-1">
                  <span>Expression</span>
                  <span>+25-35%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-white/60 rounded-full" style={{ width: '65%' }}></div>
                  </div>
                  <div className="h-1.5 bg-white/30 rounded-full w-full">
                    <div className="h-full bg-[#00FFEE]/70 rounded-full" style={{ width: '95%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Step 3: Voice Application Interface
  const renderStep3Content = () => {
    return (
      <div>
        <ThemeHeading level={3} className="mb-4 retune-text-glow-cyan">Application Guide</ThemeHeading>

        {applicationMode === 'tts' && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Text-to-Speech Tips</h4>
            <ul className="text-xs text-white/80 space-y-2">
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Use punctuation to control pacing and emphasis</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Add emphasis with italics or ALL CAPS for key words</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>For singing, structure lyrics with line breaks</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Select the appropriate emotion for your content</span>
              </li>
            </ul>
          </div>
        )}

        {applicationMode === 'enhance' && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Voice Enhancement Tips</h4>
            <ul className="text-xs text-white/80 space-y-2">
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Upload the highest quality recording available</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>For best results, use WAV format audio</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Enhancement works best on clean vocals with minimal background noise</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Processing time depends on the length of your audio</span>
              </li>
            </ul>
          </div>
        )}

        {applicationMode === 'collaborate' && (
          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Collaboration Tips</h4>
            <ul className="text-xs text-white/80 space-y-2">
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Select voices that complement each other tonally</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>For harmonies, choose voices with different ranges</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Structure lyrics to indicate which voice sings which part</span>
              </li>
              <li className="flex items-start">
                <span className="text-[#00FFEE] mr-2">•</span>
                <span>Processing time increases with more voices</span>
              </li>
            </ul>
          </div>
        )}

        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Recent Outputs</h4>
          <p className="text-xs text-white/70 mb-3">
            Your recent voice outputs will appear here
          </p>
          <div className="retune-glass-container p-3 text-center">
            <p className="text-xs text-white/50">No recent outputs</p>
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Integration Options</h4>
          <p className="text-xs text-white/80 mb-3">
            Your enhanced voice can be used across the platform:
          </p>
          <ul className="text-xs text-white/80 space-y-2">
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Connect to the Lyric Generator to perform your lyrics</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Export to professional DAWs for further production</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Share with collaborators for joint projects</span>
            </li>
            <li className="flex items-start">
              <span className="text-[#00FFEE] mr-2">•</span>
              <span>Use with AI-generated instrumentals for complete tracks</span>
            </li>
          </ul>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <ThemeHeading level={2} className="mb-4">Context Panel</ThemeHeading>
      <div className="mb-6">
        {renderContent()}
      </div>
    </div>
  );
};

export default SuperhumanContextPanel;
