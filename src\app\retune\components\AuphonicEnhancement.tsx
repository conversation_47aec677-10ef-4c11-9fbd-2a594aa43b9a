'use client';

import React, { useState, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { AuphonicPresetType } from '@/utils/audioEnhancement';
import { ThemeButton, ThemeCard, ThemeHeading, ThemeSelect, ThemeSlider, ThemeToggle } from '@/components/ui';

interface AuphonicEnhancementProps {
  onEnhancementComplete?: (audioUrl: string) => void;
}

const AuphonicEnhancement: React.FC<AuphonicEnhancementProps> = ({ onEnhancementComplete }) => {
  // State
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [productionId, setProductionId] = useState<string | null>(null);
  const [processingStatus, setProcessingStatus] = useState<string | null>(null);
  const [enhancedAudioUrl, setEnhancedAudioUrl] = useState<string | null>(null);
  const [mode, setMode] = useState<'simple' | 'advanced'>('simple');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  
  // Enhancement options
  const [presetType, setPresetType] = useState<AuphonicPresetType>(AuphonicPresetType.LOUDNESS_NORMALIZATION);
  const [targetLoudness, setTargetLoudness] = useState(-16);
  const [noiseReduction, setNoiseReduction] = useState(true);
  const [levelingAlgorithm, setLevelingAlgorithm] = useState<'dynamic' | 'adaptive'>('dynamic');
  const [outputFormat, setOutputFormat] = useState<'mp3' | 'wav' | 'ogg' | 'aac' | 'flac'>('mp3');
  const [outputQuality, setOutputQuality] = useState<'low' | 'medium' | 'high'>('high');
  
  // Refs
  const audioInputRef = useRef<HTMLInputElement>(null);
  const audioPlayerRef = useRef<HTMLAudioElement>(null);
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setAudioFile(files[0]);
      setEnhancedAudioUrl(null);
      setProductionId(null);
      setProcessingStatus(null);
    }
  };
  
  // Handle enhancement
  const handleEnhance = async () => {
    if (!audioFile) {
      toast.error('Please select an audio file');
      return;
    }
    
    try {
      setIsProcessing(true);
      const toastId = toast.loading('Enhancing audio...');
      
      // Create form data
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('mode', mode);
      
      // Add options
      const options = {
        title: audioFile.name.replace(/\.[^/.]+$/, ''), // Remove extension
        presetType,
        targetLoudness,
        noiseReduction,
        levelingAlgorithm,
        outputFormat,
        outputQuality
      };
      
      formData.append('options', JSON.stringify(options));
      
      // Send request
      const response = await fetch('/api/retune/auphonic', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance audio');
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to enhance audio');
      }
      
      // Update state with production ID
      setProductionId(data.productionId);
      setProcessingStatus(data.status);
      
      toast.success('Audio enhancement started', { id: toastId });
      
      // Start polling for status
      if (data.productionId) {
        pollStatus(data.productionId);
      }
    } catch (error) {
      console.error('Error enhancing audio:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to enhance audio');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Poll for status
  const pollStatus = async (id: string) => {
    try {
      const response = await fetch(`/api/retune/auphonic?id=${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to check status');
      }
      
      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to check status');
      }
      
      setProcessingStatus(data.status);
      
      // If processing is complete, get the audio URL
      if (data.status === 'done' && data.audioUrl) {
        setEnhancedAudioUrl(data.audioUrl);
        toast.success('Audio enhancement complete!');
        
        // Call callback if provided
        if (onEnhancementComplete) {
          onEnhancementComplete(data.audioUrl);
        }
      } else if (data.status === 'error') {
        toast.error('Error enhancing audio');
      } else if (data.status !== 'done') {
        // Continue polling if not done
        setTimeout(() => pollStatus(id), 5000);
      }
    } catch (error) {
      console.error('Error polling status:', error);
      toast.error('Failed to check enhancement status');
    }
  };
  
  return (
    <ThemeCard className="p-6">
      <ThemeHeading level={3} className="mb-4">Auphonic Audio Enhancement</ThemeHeading>
      
      <div className="mb-6">
        <p className="text-sm text-gray-300 mb-4">
          Enhance your audio with professional-grade processing using Auphonic technology.
          Normalize loudness, reduce noise, and improve overall audio quality.
        </p>
        
        {/* Audio Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Select Audio File</label>
          <input
            type="file"
            ref={audioInputRef}
            onChange={handleFileChange}
            accept="audio/*"
            className="hidden"
          />
          <div className="flex items-center gap-3">
            <ThemeButton
              onClick={() => audioInputRef.current?.click()}
              disabled={isProcessing}
              variant="secondary"
            >
              Browse Files
            </ThemeButton>
            <span className="text-sm truncate">
              {audioFile ? audioFile.name : 'No file selected'}
            </span>
          </div>
        </div>
        
        {/* Mode Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Enhancement Mode</label>
          <div className="flex gap-3">
            <ThemeButton
              onClick={() => setMode('simple')}
              variant={mode === 'simple' ? 'primary' : 'secondary'}
              size="sm"
            >
              Simple
            </ThemeButton>
            <ThemeButton
              onClick={() => setMode('advanced')}
              variant={mode === 'advanced' ? 'primary' : 'secondary'}
              size="sm"
            >
              Advanced
            </ThemeButton>
          </div>
        </div>
        
        {/* Advanced Options Toggle */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Show Advanced Options</label>
            <ThemeToggle
              checked={showAdvancedOptions}
              onChange={setShowAdvancedOptions}
            />
          </div>
        </div>
        
        {/* Advanced Options */}
        {showAdvancedOptions && (
          <div className="mb-4 p-4 border border-gray-700 rounded-md">
            {/* Preset Type */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Enhancement Type</label>
              <ThemeSelect
                value={presetType}
                onChange={(e) => setPresetType(e.target.value as AuphonicPresetType)}
                disabled={isProcessing}
              >
                <option value={AuphonicPresetType.LOUDNESS_NORMALIZATION}>Loudness Normalization</option>
                <option value={AuphonicPresetType.NOISE_REDUCTION}>Noise Reduction</option>
                <option value={AuphonicPresetType.VOICE_ENHANCEMENT}>Voice Enhancement</option>
                <option value={AuphonicPresetType.MUSIC_MASTERING}>Music Mastering</option>
                <option value={AuphonicPresetType.PODCAST_MASTERING}>Podcast Mastering</option>
              </ThemeSelect>
            </div>
            
            {/* Target Loudness */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Target Loudness: {targetLoudness} LUFS
              </label>
              <ThemeSlider
                min={-23}
                max={-9}
                step={1}
                value={targetLoudness}
                onChange={(e) => setTargetLoudness(parseInt(e.target.value))}
                disabled={isProcessing}
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>Quieter (-23)</span>
                <span>Louder (-9)</span>
              </div>
            </div>
            
            {/* Noise Reduction */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Noise Reduction</label>
                <ThemeToggle
                  checked={noiseReduction}
                  onChange={setNoiseReduction}
                  disabled={isProcessing}
                />
              </div>
            </div>
            
            {/* Output Format */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Output Format</label>
              <ThemeSelect
                value={outputFormat}
                onChange={(e) => setOutputFormat(e.target.value as any)}
                disabled={isProcessing}
              >
                <option value="mp3">MP3</option>
                <option value="wav">WAV</option>
                <option value="ogg">OGG</option>
                <option value="aac">AAC</option>
                <option value="flac">FLAC</option>
              </ThemeSelect>
            </div>
          </div>
        )}
        
        {/* Enhance Button */}
        <ThemeButton
          onClick={handleEnhance}
          disabled={!audioFile || isProcessing}
          className="w-full"
        >
          {isProcessing ? 'Processing...' : 'Enhance Audio'}
        </ThemeButton>
      </div>
      
      {/* Processing Status */}
      {processingStatus && (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-3 h-3 rounded-full ${
              processingStatus === 'done' ? 'bg-green-500' :
              processingStatus === 'error' ? 'bg-red-500' :
              'bg-yellow-500 animate-pulse'
            }`}></div>
            <span className="text-sm font-medium">
              Status: {processingStatus.charAt(0).toUpperCase() + processingStatus.slice(1)}
            </span>
          </div>
          {processingStatus !== 'done' && processingStatus !== 'error' && (
            <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
              <div className="bg-theme-accent h-2 rounded-full animate-pulse w-1/2"></div>
            </div>
          )}
        </div>
      )}
      
      {/* Enhanced Audio Player */}
      {enhancedAudioUrl && (
        <div className="mt-6">
          <ThemeHeading level={4} className="mb-2">Enhanced Audio</ThemeHeading>
          <audio
            ref={audioPlayerRef}
            src={enhancedAudioUrl}
            controls
            className="w-full"
          ></audio>
          <div className="flex justify-end mt-2">
            <ThemeButton
              onClick={() => {
                const a = document.createElement('a');
                a.href = enhancedAudioUrl;
                a.download = `enhanced_${audioFile?.name || 'audio'}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }}
              variant="secondary"
              size="sm"
            >
              Download
            </ThemeButton>
          </div>
        </div>
      )}
    </ThemeCard>
  );
};

export default AuphonicEnhancement;
