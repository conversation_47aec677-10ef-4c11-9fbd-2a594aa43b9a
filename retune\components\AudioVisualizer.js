import React, { useEffect, useRef, useState } from 'react';
import { calculateAudioLevels } from '../utils/audioUtils';
import '../styles/visualizer.css';

const AudioVisualizer = ({ 
  audioContext, 
  stream, 
  isRecording, 
  audioUrl,
  isPlaying 
}) => {
  const [visualizerType, setVisualizerType] = useState('waveform'); // 'waveform' or 'spectrum'
  const [levels, setLevels] = useState([]);
  const animationRef = useRef(null);
  const analyserRef = useRef(null);
  const sourceRef = useRef(null);
  const audioRef = useRef(null);
  
  // Number of bars in the visualizer
  const numBars = 64;
  
  // Initialize the visualizer
  useEffect(() => {
    if (!audioContext) return;
    
    // Create analyzer node
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048;
    analyserRef.current = analyser;
    
    // Initialize levels array
    setLevels(Array(numBars).fill(0.05));
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      if (sourceRef.current) {
        sourceRef.current.disconnect();
      }
    };
  }, [audioContext]);
  
  // Handle recording visualization
  useEffect(() => {
    if (!audioContext || !analyserRef.current || !stream || !isRecording) return;
    
    // Connect stream to analyzer
    const source = audioContext.createMediaStreamSource(stream);
    source.connect(analyserRef.current);
    sourceRef.current = source;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateLevels = () => {
      analyserRef.current.getByteTimeDomainData(dataArray);
      
      // Convert to Float32Array for our utility function
      const floatData = new Float32Array(bufferLength);
      for (let i = 0; i < bufferLength; i++) {
        floatData[i] = (dataArray[i] - 128) / 128;
      }
      
      const newLevels = calculateAudioLevels(floatData, numBars);
      setLevels(newLevels);
      
      animationRef.current = requestAnimationFrame(updateLevels);
    };
    
    updateLevels();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      if (sourceRef.current) {
        sourceRef.current.disconnect();
      }
    };
  }, [audioContext, stream, isRecording]);
  
  // Handle playback visualization
  useEffect(() => {
    if (!audioContext || !analyserRef.current || !audioUrl || !isPlaying) return;
    
    // Create audio element for playback
    const audio = new Audio(audioUrl);
    audio.loop = false;
    audioRef.current = audio;
    
    // Connect audio element to analyzer
    const source = audioContext.createMediaElementSource(audio);
    source.connect(analyserRef.current);
    analyserRef.current.connect(audioContext.destination);
    sourceRef.current = source;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateLevels = () => {
      if (visualizerType === 'waveform') {
        analyserRef.current.getByteTimeDomainData(dataArray);
        
        // Convert to Float32Array for our utility function
        const floatData = new Float32Array(bufferLength);
        for (let i = 0; i < bufferLength; i++) {
          floatData[i] = (dataArray[i] - 128) / 128;
        }
        
        const newLevels = calculateAudioLevels(floatData, numBars);
        setLevels(newLevels);
      } else {
        analyserRef.current.getByteFrequencyData(dataArray);
        
        // Calculate levels from frequency data
        const newLevels = [];
        const barWidth = Math.floor(bufferLength / numBars);
        
        for (let i = 0; i < numBars; i++) {
          let sum = 0;
          const start = i * barWidth;
          const end = start + barWidth;
          
          for (let j = start; j < end; j++) {
            sum += dataArray[j];
          }
          
          const average = sum / barWidth / 255;
          newLevels.push(average);
        }
        
        setLevels(newLevels);
      }
      
      animationRef.current = requestAnimationFrame(updateLevels);
    };
    
    audio.play();
    updateLevels();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      if (sourceRef.current) {
        sourceRef.current.disconnect();
      }
      
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, [audioContext, audioUrl, isPlaying, visualizerType]);
  
  return (
    <div className="visualizer-wrapper">
      <div className="visualizer-controls">
        <button 
          className={`visualizer-button ${visualizerType === 'waveform' ? 'active' : ''}`}
          onClick={() => setVisualizerType('waveform')}
        >
          Waveform
        </button>
        <button 
          className={`visualizer-button ${visualizerType === 'spectrum' ? 'active' : ''}`}
          onClick={() => setVisualizerType('spectrum')}
        >
          Spectrum
        </button>
      </div>
      
      <div className="visualizer-container">
        {visualizerType === 'waveform' ? (
          <div className="waveform-visualizer">
            {levels.map((level, index) => (
              <div 
                key={index}
                className="waveform-bar"
                style={{ height: `${Math.max(5, level * 100)}%` }}
              />
            ))}
          </div>
        ) : (
          <div className="spectrum-visualizer">
            {levels.map((level, index) => (
              <div 
                key={index}
                className="spectrum-bar"
                style={{ height: `${Math.max(5, level * 100)}%` }}
              />
            ))}
          </div>
        )}
        
        <div className={`visualizer-overlay ${!isRecording && !isPlaying ? 'visible' : ''}`}>
          {!isRecording && !isPlaying && 'Press Record to Start'}
        </div>
      </div>
      
      <div className="time-axis">
        {[0, 1, 2, 3, 4].map((time) => (
          <div key={time} className="time-marker" style={{ left: `${time * 25}%` }}>
            <span className="time-label">{time}s</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AudioVisualizer;
