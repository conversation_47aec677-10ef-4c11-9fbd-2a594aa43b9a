// API route for enhancing voice recordings
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import formidable from 'formidable';
import { exec } from 'child_process';
import util from 'util';

// Promisify exec
const execPromise = util.promisify(exec);

// Disable body parsing for this route
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Create temp directory if it doesn't exist
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Parse form with formidable
    const form = new formidable.IncomingForm();
    form.uploadDir = tempDir;
    form.keepExtensions = true;

    form.parse(req, async (err, fields, files) => {
      if (err) {
        console.error('Error parsing form:', err);
        return res.status(500).json({ error: 'Error processing upload' });
      }

      if (!files.audio) {
        return res.status(400).json({ error: 'No audio file provided' });
      }

      try {
        // Generate unique filenames
        const fileId = uuidv4();
        const inputPath = files.audio.filepath;
        const outputPath = path.join(tempDir, `${fileId}_enhanced.wav`);
        
        // Extract enhancement parameters
        const params = {
          clarity: parseFloat(fields.clarity || 0.5),
          resonance: parseFloat(fields.resonance || 0.5),
          confidence: parseFloat(fields.confidence || 0.5),
          stability: parseFloat(fields.stability || 0.5),
          warmth: parseFloat(fields.warmth || 0.5),
          preset: fields.preset || 'custom'
        };
        
        // In a real implementation, we would call the Python enhancement script
        // For now, we'll just copy the file as a placeholder
        
        // Example of how you would call the Python script:
        // const pythonScript = path.join(process.cwd(), 'models', 'enhance.py');
        // const paramsString = Object.entries(params)
        //   .map(([key, value]) => `--${key} ${value}`)
        //   .join(' ');
        // await execPromise(`python ${pythonScript} --input "${inputPath}" --output "${outputPath}" ${paramsString}`);
        
        // For now, just copy the file
        fs.copyFileSync(inputPath, outputPath);
        
        // Set response headers for file download
        res.setHeader('Content-Type', 'audio/wav');
        res.setHeader('Content-Disposition', `attachment; filename="enhanced_${fileId}.wav"`);
        
        // Stream the file to the response
        const fileStream = fs.createReadStream(outputPath);
        fileStream.pipe(res);
        
        // Clean up temp files when done
        fileStream.on('end', () => {
          try {
            fs.unlinkSync(inputPath);
            fs.unlinkSync(outputPath);
          } catch (error) {
            console.error('Error cleaning up temp files:', error);
          }
        });
      } catch (error) {
        console.error('Error enhancing audio:', error);
        res.status(500).json({ error: 'Error enhancing audio' });
      }
    });
  } catch (error) {
    console.error('Error handling enhancement:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
