import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import os from 'os';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Validate required parameters
    const { modelA, modelB, blendRatio, operation, accent, ageFactor, strength } = body;
    
    if (!operation) {
      return NextResponse.json(
        { error: 'Operation is required (morph, age, or accent)' },
        { status: 400 }
      );
    }
    
    // Create a temporary directory for output
    const tempDir = path.join(os.tmpdir(), 'apit-retune');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Get the path to the Python script
    const scriptDir = path.join(process.cwd(), 'python', 'retune');
    const scriptPath = path.join(scriptDir, 'voice_morphing.py');
    
    // Create a temporary file for the output
    const outputPath = path.join(tempDir, `output_${Date.now()}.json`);
    
    // Build the command based on the operation
    let pythonCommand = '';
    
    if (operation === 'morph') {
      // Validate morph parameters
      if (!modelA || !modelB) {
        return NextResponse.json(
          { error: 'Both modelA and modelB are required for morphing' },
          { status: 400 }
        );
      }
      
      // Build morph command
      pythonCommand = `python "${scriptPath}" morph "${modelA}" "${modelB}" --ratio ${blendRatio || 0.5} --output-dir "${tempDir}"`;
    } 
    else if (operation === 'age') {
      // Validate age parameters
      if (!modelA) {
        return NextResponse.json(
          { error: 'Model is required for aging' },
          { status: 400 }
        );
      }
      
      // Build age command
      pythonCommand = `python "${scriptPath}" age "${modelA}" --factor ${ageFactor || 0.5} --output-dir "${tempDir}"`;
    } 
    else if (operation === 'accent') {
      // Validate accent parameters
      if (!modelA || !accent) {
        return NextResponse.json(
          { error: 'Both model and accent are required for accent transformation' },
          { status: 400 }
        );
      }
      
      // Build accent command
      pythonCommand = `python "${scriptPath}" accent "${modelA}" "${accent}" --strength ${strength || 0.5} --output-dir "${tempDir}"`;
    } 
    else {
      return NextResponse.json(
        { error: 'Invalid operation. Must be morph, age, or accent' },
        { status: 400 }
      );
    }
    
    // Execute the command
    const { stdout, stderr } = await execAsync(pythonCommand);
    
    if (stderr) {
      console.error('Python script error:', stderr);
    }
    
    // Parse the output from stdout
    const outputLines = stdout.trim().split('\n');
    const lastLine = outputLines[outputLines.length - 1];
    
    // Extract the model info from the last line
    const modelInfoMatch = lastLine.match(/Created .+ model: (.+)/);
    
    if (!modelInfoMatch) {
      return NextResponse.json(
        { error: 'Failed to create model' },
        { status: 500 }
      );
    }
    
    // Parse the model info
    const modelInfo = JSON.parse(modelInfoMatch[1].replace(/'/g, '"'));
    
    // Return the model info
    return NextResponse.json({
      success: true,
      operation,
      modelInfo
    });
  } catch (error) {
    console.error('Error in voice morphing:', error);
    return NextResponse.json(
      { error: 'Failed to process voice morphing request' },
      { status: 500 }
    );
  }
}
