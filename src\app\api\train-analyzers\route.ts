import { NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';

export async function POST(request: Request): Promise<Response> {
  console.log("Received request to train analyzers...");

  // Define the path to the Python script
  const scriptPath = path.resolve(process.cwd(), 'apit-mvp/python/neural_network/lyrics_analyzer.py');
  const pythonExecutable = process.platform === 'win32' ? 'python' : 'python3'; // Adjust for OS

  // Arguments to trigger both genre and style training
  const scriptArgs = ['--train-genre', '--train-style'];

  return new Promise<Response>((resolve) => {
    console.log(`Executing training script: ${pythonExecutable} ${scriptPath} ${scriptArgs.join(' ')}`);
    const pythonProcess = spawn(pythonExecutable, [scriptPath, ...scriptArgs]);

    let stdoutData = '';
    let stderrData = '';

    // Collect stdout
    pythonProcess.stdout.on('data', (data) => {
      stdoutData += data.toString();
    });

    // Collect stderr
    pythonProcess.stderr.on('data', (data) => {
      stderrData += data.toString();
      console.error(`Training Script stderr: ${data.toString()}`); // Log stderr immediately
    });

    // Handle script exit
    pythonProcess.on('close', (code) => {
      console.log(`Training script finished with code ${code}.`);
      if (code === 0) {
        try {
          const result = JSON.parse(stdoutData);
          console.log("Training script output parsed successfully.");
          resolve(NextResponse.json({ success: true, message: "Analyzers training process completed.", details: result }, { status: 200 }));
        } catch (parseError) {
          console.error(`Error parsing training script output: ${parseError}`);
          console.error(`Raw stdout: ${stdoutData}`);
          resolve(NextResponse.json({ success: false, error: "Failed to parse training script output.", rawOutput: stdoutData }, { status: 500 }));
        }
      } else {
        console.error(`Training script failed. Code: ${code}`);
        console.error(`Stderr: ${stderrData}`);
        resolve(NextResponse.json({ success: false, error: `Training script failed with code ${code}.`, stderr: stderrData }, { status: 500 }));
      }
    });

    // Handle errors spawning the process itself
    pythonProcess.on('error', (spawnError) => {
      console.error(`Failed to start training script: ${spawnError.message}`);
      resolve(NextResponse.json({ success: false, error: `Failed to start training script: ${spawnError.message}` }, { status: 500 }));
    });
  });
}

// Optional: Add a GET handler for basic status check or manual trigger info
export async function GET(request: Request) {
  return NextResponse.json({ message: "Send a POST request to this endpoint to trigger analyzer training." });
}