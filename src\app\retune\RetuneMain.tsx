'use client';

import React from 'react';

export default function RetuneMain() {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 to-black text-white p-4">
            <h1 className="text-5xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 animate-pulse">
                Retune Studio
            </h1>
            <p className="text-lg text-gray-300 mb-8 text-center max-w-2xl">
                Your advanced voice transformation and enhancement studio.
                Upload your audio and explore powerful AI-driven tools for voice cloning,
                pitch correction, and vocal effects.
            </p>
            <div className="bg-gray-800 bg-opacity-70 backdrop-blur-sm p-8 rounded-lg shadow-xl border border-gray-700 w-full max-w-md text-center">
                <h2 className="text-2xl font-semibold mb-4 text-purple-300">Coming Soon!</h2>
                <p className="text-gray-400">
                    We're actively building out the full functionality of the Retune Studio.
                    Stay tuned for exciting updates!
                </p>
                <div className="mt-6">
                    <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold rounded-full shadow-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 ease-in-out transform hover:scale-105">
                        Learn More
                    </button>
                </div>
            </div>
        </div>
    );
}