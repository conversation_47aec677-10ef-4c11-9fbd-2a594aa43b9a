/**
 * Audio utility functions for Retune
 * Handles audio recording, playback, and processing
 */

// Initialize audio context
const createAudioContext = () => {
  // Use AudioContext with fallback for older browsers
  return new (window.AudioContext || window.webkitAudioContext)();
};

// Request microphone access
const requestMicrophoneAccess = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    return { success: true, stream };
  } catch (error) {
    console.error('Error accessing microphone:', error);
    return { success: false, error };
  }
};

// Start recording audio
const startRecording = (stream, audioContext) => {
  const source = audioContext.createMediaStreamSource(stream);
  const processor = audioContext.createScriptProcessor(4096, 1, 1);
  const chunks = [];

  source.connect(processor);
  processor.connect(audioContext.destination);

  processor.onaudioprocess = (e) => {
    const inputData = e.inputBuffer.getChannelData(0);
    const audioData = new Float32Array(inputData);
    chunks.push(audioData);
  };

  return {
    processor,
    source,
    chunks,
    stop: () => {
      source.disconnect();
      processor.disconnect();
      return chunks;
    }
  };
};

// Convert audio chunks to WAV format
const audioChunksToWav = (chunks, sampleRate) => {
  // Calculate total length
  let totalLength = 0;
  for (const chunk of chunks) {
    totalLength += chunk.length;
  }

  // Create a single Float32Array with all audio data
  const audioData = new Float32Array(totalLength);
  let offset = 0;
  for (const chunk of chunks) {
    audioData.set(chunk, offset);
    offset += chunk.length;
  }

  // Convert to WAV format
  const wavBuffer = createWavFromAudioData(audioData, sampleRate);
  return wavBuffer;
};

// Create WAV file from audio data
const createWavFromAudioData = (audioData, sampleRate) => {
  const numChannels = 1;
  const bitsPerSample = 16;
  const bytesPerSample = bitsPerSample / 8;
  const blockAlign = numChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  const dataSize = audioData.length * bytesPerSample;
  const buffer = new ArrayBuffer(44 + dataSize);
  const view = new DataView(buffer);

  // Write WAV header
  // "RIFF" chunk descriptor
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + dataSize, true);
  writeString(view, 8, 'WAVE');

  // "fmt " sub-chunk
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true); // fmt chunk size
  view.setUint16(20, 1, true); // audio format (1 for PCM)
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);

  // "data" sub-chunk
  writeString(view, 36, 'data');
  view.setUint32(40, dataSize, true);

  // Write audio data
  floatTo16BitPCM(view, 44, audioData);

  return buffer;
};

// Helper function to write a string to a DataView
const writeString = (view, offset, string) => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

// Convert Float32Array to 16-bit PCM
const floatTo16BitPCM = (output, offset, input) => {
  for (let i = 0; i < input.length; i++, offset += 2) {
    const s = Math.max(-1, Math.min(1, input[i]));
    output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
  }
};

// Create a Blob from audio buffer
const createAudioBlob = (buffer) => {
  return new Blob([buffer], { type: 'audio/wav' });
};

// Create an object URL for audio playback
const createAudioUrl = (blob) => {
  return URL.createObjectURL(blob);
};

// Calculate audio levels for visualization
const calculateAudioLevels = (audioData, numBands = 32) => {
  // Simple implementation - in a real app, you'd use FFT
  const bandSize = Math.floor(audioData.length / numBands);
  const levels = [];

  for (let i = 0; i < numBands; i++) {
    const start = i * bandSize;
    const end = start + bandSize;
    let sum = 0;

    for (let j = start; j < end; j++) {
      sum += Math.abs(audioData[j]);
    }

    levels.push(sum / bandSize);
  }

  return levels;
};

export {
  createAudioContext,
  requestMicrophoneAccess,
  startRecording,
  audioChunksToWav,
  createAudioBlob,
  createAudioUrl,
  calculateAudioLevels
};
