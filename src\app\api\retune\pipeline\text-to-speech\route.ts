import apiService, { elevenlabsAPI, supabaseService } from '@/services/api';
import { NextRequest, NextResponse } from 'next/server';

/**
 * API route for text-to-speech
 *
 * This route handles the generation of speech from text using the specified
 * voice profile.
 *
 * @param req - The request object
 * @returns A response with the generated audio
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();

    // Extract request data
    const { text, profileId, style, emotion } = body;

    // Validate required fields
    if (!text || !profileId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    console.log(`Processing TTS request for profile ${profileId} with text: ${text.substring(0, 50)}...`);

    try {
      // Get the voice profile from Supabase
      const profile = await supabaseService.getVoiceProfile(profileId);

      if (!profile) {
        return NextResponse.json(
          { error: `Voice profile with ID ${profileId} not found` },
          { status: 404 }
        );
      }

      console.log(`Using voice profile: ${profile.name} with provider: ${profile.provider}`);

      // Generate the speech based on the provider
      let audioData;
      let audioUrl;

      switch (profile.provider) {
        case 'elevenlabs':
          console.log('Using ElevenLabs for TTS');

          // Set voice settings based on profile parameters
          const voiceSettings = {
            stability: profile.clarity / 100, // Convert to 0-1 range
            similarity_boost: profile.confidence / 100,
            style: profile.emotional_expression / 100,
            use_speaker_boost: true
          };

          // Call ElevenLabs API
          audioData = await elevenlabsAPI.textToSpeech({
            text,
            voice_id: profile.provider_voice_id,
            model_id: 'eleven_monolingual_v1',
            voice_settings: voiceSettings
          });

          // Convert ArrayBuffer to Blob
          const audioBlob = new Blob([audioData], { type: 'audio/mpeg' });

          // Save the output to Supabase storage
          const fileName = `tts_${Date.now()}.mp3`;
          const filePath = `voice_outputs/${profile.id}/${fileName}`;

          // Upload to Supabase storage
          const { data: storageData } = await supabaseService.getClient().storage
            .from('audio')
            .upload(filePath, audioBlob, {
              contentType: 'audio/mpeg',
              cacheControl: '3600'
            });

          if (storageData) {
            // Get public URL
            const { data: urlData } = supabaseService.getClient().storage
              .from('audio')
              .getPublicUrl(filePath);

            audioUrl = urlData.publicUrl;

            // Save output record to database
            await supabaseService.getClient()
              .from('voice_outputs')
              .insert({
                profile_id: profileId,
                output_type: 'tts',
                storage_path: filePath,
                text_content: text,
                settings: { style, emotion }
              });

            // Return the URL
            return NextResponse.json({ audioUrl });
          } else {
            // If storage upload failed, return the audio directly
            const response = new NextResponse(audioBlob);
            response.headers.set('Content-Type', 'audio/mpeg');
            return response;
          }

        case 'resemble':
        case 'voicemod':
          // For other providers, use the unified API service
          console.log(`Using ${profile.provider} for TTS via unified API`);
          const result = await apiService.textToSpeech({
            text,
            voice_id: profile.provider_voice_id,
            provider: profile.provider,
            options: { style, emotion }
          });

          if (typeof result === 'string') {
            return NextResponse.json({ audioUrl: result });
          } else {
            const blob = new Blob([result], { type: 'audio/mpeg' });
            const response = new NextResponse(blob);
            response.headers.set('Content-Type', 'audio/mpeg');
            return response;
          }

        default:
          throw new Error(`Unsupported provider: ${profile.provider}`);
      }
    } catch (apiError) {
      console.error('API Error:', apiError);

      // For development, create a mock TTS response
      console.log('Falling back to mock TTS response');

      // Create a simple sine wave audio as mock
      const audioContext = new AudioContext();
      const sampleRate = audioContext.sampleRate;
      const duration = 3; // 3 seconds
      const frameCount = sampleRate * duration;

      const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
      const channelData = audioBuffer.getChannelData(0);

      // Generate a simple sine wave
      for (let i = 0; i < frameCount; i++) {
        channelData[i] = Math.sin(440 * Math.PI * 2 * i / sampleRate) * 0.5;
      }

      // Convert to WAV
      const offlineContext = new OfflineAudioContext(1, frameCount, sampleRate);
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(offlineContext.destination);
      source.start();

      const renderedBuffer = await offlineContext.startRendering();

      // Convert to WAV format
      const wavBlob = new Blob(['mock audio data'], { type: 'audio/wav' });

      // Return as audio
      const response = new NextResponse(wavBlob);
      response.headers.set('Content-Type', 'audio/wav');
      return response;
    }
  } catch (error) {
    console.error('Error generating speech:', error);
    return NextResponse.json(
      { error: 'Failed to generate speech' },
      { status: 500 }
    );
  }
}
