/**
 * AI Writer Dataset Service - DISABLED FOR VERCEL DEPLOYMENT
 * Dataset loading disabled for cloud deployment - using cloud APIs only
 */

// DISABLED FOR VERCEL DEPLOYMENT
// import {
//   writerDatasetConfigs,
//   getWriterDatasetConfig,
//   getGenreDatasets,
//   getWriterCapabilities,
//   getTotalDatasetStats,
//   WriterDatasetConfig
// } from '../config/ai-writer-datasets';

export interface DatasetLoadResult {
  success: boolean;
  writerId: string;
  loadedModels: string[];
  availableData: {
    styleModels: number;
    trainingFiles: number;
    analysisFiles: number;
  };
  error?: string;
}

export interface WriterModelInfo {
  writerId: string;
  writerName: string;
  genre: string;
  isModelLoaded: boolean;
  capabilities: string[];
  datasetPaths: string[];
  lastTrainingUpdate: string;
  modelPerformance?: {
    accuracy?: number;
    trainingLoss?: number;
    validationLoss?: number;
  };
}

// Cache for loaded datasets to avoid repeated loading
const datasetCache = new Map<string, DatasetLoadResult>();
const modelCache = new Map<string, any>();

/**
 * Load datasets for a specific AI writer - DISABLED FOR VERCEL DEPLOYMENT
 */
export async function loadWriterDatasets(writerId: string): Promise<DatasetLoadResult> {
  console.log(`⚠️ Dataset loading disabled for cloud deployment. Writer: ${writerId}`);

  return {
    success: false,
    writerId,
    loadedModels: [],
    availableData: {
      styleModels: 0,
      trainingFiles: 0,
      analysisFiles: 0
    },
    error: 'Dataset loading disabled for cloud deployment'
  };
}

/**
 * Get model information for all writers - DISABLED FOR VERCEL DEPLOYMENT
 */
export function getAllWriterModelInfo(): WriterModelInfo[] {
  console.log('⚠️ Writer model info disabled for cloud deployment');
  return [];
}

/**
 * Get writer by genre with dataset info
 */
export function getWriterByGenreWithDatasets(genre: string): WriterModelInfo | null {
  const configs = getGenreDatasets(genre);
  if (configs.length === 0) return null;

  const config = configs[0]; // Take the first match
  return {
    writerId: config.writerId,
    writerName: config.writerName,
    genre: config.primaryGenre,
    isModelLoaded: config.modelCapabilities.styleTransfer || config.modelCapabilities.classification,
    capabilities: getWriterCapabilities(config.writerId),
    datasetPaths: [
      ...config.assignedDatasets.ultimateStyleModels,
      ...config.assignedDatasets.trainedDataPaths
    ],
    lastTrainingUpdate: config.datasetStats.lastUpdated
  };
}

/**
 * Check if a writer has specific model capability
 */
export function hasModelCapability(writerId: string, capability: keyof WriterDatasetConfig['modelCapabilities']): boolean {
  const config = getWriterDatasetConfig(writerId);
  return config ? config.modelCapabilities[capability] : false;
}

/**
 * Get dataset statistics summary
 */
export function getDatasetSummary() {
  const stats = getTotalDatasetStats();
  const writerInfo = getAllWriterModelInfo();

  return {
    overview: stats,
    writers: writerInfo,
    capabilities: {
      styleTransfer: writerDatasetConfigs.filter(c => c.modelCapabilities.styleTransfer).length,
      classification: writerDatasetConfigs.filter(c => c.modelCapabilities.classification).length,
      ensemble: writerDatasetConfigs.filter(c => c.modelCapabilities.ensemble).length,
      experimental: writerDatasetConfigs.filter(c => c.modelCapabilities.experimental).length
    },
    datasetPaths: {
      ultimateStyleModels: "ULTIMATE_STYLE_MODELS",
      trainedData: "TRAINED_DATA",
      analysisData: "a place in time trained data",
      trainingLogs: "training_logs"
    }
  };
}

/**
 * Validate dataset paths exist (for development/debugging)
 */
export async function validateDatasetPaths(writerId: string): Promise<{
  valid: boolean;
  missingPaths: string[];
  availablePaths: string[];
}> {
  const config = getWriterDatasetConfig(writerId);
  if (!config) {
    return {
      valid: false,
      missingPaths: [],
      availablePaths: []
    };
  }

  // In a real implementation, you would check if these paths exist
  // For now, we'll assume they exist based on the configuration
  const allPaths = [
    ...config.assignedDatasets.ultimateStyleModels,
    ...config.assignedDatasets.trainedDataPaths,
    ...config.assignedDatasets.analysisDataPaths
  ];

  return {
    valid: true,
    missingPaths: [],
    availablePaths: allPaths
  };
}

/**
 * Get recommended model for a specific task
 */
export function getRecommendedModel(writerId: string, taskType: 'generation' | 'analysis' | 'classification'): string | null {
  const config = getWriterDatasetConfig(writerId);
  if (!config) return null;

  switch (taskType) {
    case 'generation':
      if (config.modelCapabilities.styleTransfer) return 'Style Transfer';
      if (config.modelCapabilities.ensemble) return 'Ensemble';
      return null;

    case 'analysis':
      if (config.modelCapabilities.classification) return 'Classification';
      if (config.modelCapabilities.styleTransfer) return 'Style Transfer';
      return null;

    case 'classification':
      if (config.modelCapabilities.classification) return 'Classification';
      return null;

    default:
      return null;
  }
}

/**
 * Cache management functions for performance optimization
 */
export function clearDatasetCache(writerId?: string): void {
  if (writerId) {
    datasetCache.delete(writerId);
    console.log(`🗑️ Cleared cache for writer: ${writerId}`);
  } else {
    datasetCache.clear();
    modelCache.clear();
    console.log(`🗑️ Cleared all dataset caches`);
  }
}

export function getCacheStats(): { datasetCacheSize: number; modelCacheSize: number } {
  return {
    datasetCacheSize: datasetCache.size,
    modelCacheSize: modelCache.size
  };
}

/**
 * Preload datasets for frequently used writers
 */
export async function preloadFrequentWriters(): Promise<void> {
  const frequentWriters = ['ava_clarke', 'nova_sinclair', 'jay_carter', 'zane_mercer'];

  console.log('🚀 Preloading frequent writers...');
  await Promise.all(
    frequentWriters.map(writerId => loadWriterDatasets(writerId))
  );
  console.log('✅ Preloading complete');
}

/**
 * Batch load multiple writers for better performance
 */
export async function batchLoadWriters(writerIds: string[]): Promise<DatasetLoadResult[]> {
  console.log(`🔄 Batch loading ${writerIds.length} writers...`);

  const results = await Promise.all(
    writerIds.map(writerId => loadWriterDatasets(writerId))
  );

  console.log(`✅ Batch loading complete: ${results.filter(r => r.success).length}/${results.length} successful`);
  return results;
}

export default {
  loadWriterDatasets,
  getAllWriterModelInfo,
  getWriterByGenreWithDatasets,
  hasModelCapability,
  getDatasetSummary,
  validateDatasetPaths,
  getRecommendedModel,
  clearDatasetCache,
  getCacheStats,
  preloadFrequentWriters,
  batchLoadWriters
};
