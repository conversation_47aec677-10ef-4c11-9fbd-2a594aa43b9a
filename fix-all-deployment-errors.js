#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 COMPREHENSIVE DEPLOYMENT ERROR FIX');
console.log('=====================================');

// All known problematic files and their fixes
const FIXES = [
  // 1. Next.js 15 Route Parameter Issues
  {
    file: 'src/app/api/model-status/[modelId]/route.ts',
    search: /{ params }: { params: { modelId: string } }/g,
    replace: 'context: { params: Promise<{ modelId: string }> }',
    description: 'Fix Next.js 15 route params'
  },
  {
    file: 'src/app/api/model-status/[modelId]/route.ts',
    search: /const modelId = params\.modelId;/g,
    replace: 'const params = await context.params;\n  const modelId = params.modelId;',
    description: 'Fix async params access'
  },
  {
    file: 'src/app/api/suno-generations/[taskId]/[file]/route.ts',
    search: /{ params }: { params: { taskId: string; file: string } }/g,
    replace: 'context: { params: Promise<{ taskId: string; file: string }> }',
    description: 'Fix Next.js 15 route params'
  },
  {
    file: 'src/app/api/suno-generations/[taskId]/[file]/route.ts',
    search: /const { taskId, file } = params;/g,
    replace: 'const params = await context.params;\n    const { taskId, file } = params;',
    description: 'Fix async params access'
  },
  {
    file: 'src/app/api/vinn/audio/[filename]/route.ts',
    search: /{ params }: { params: { filename: string } }/g,
    replace: 'context: { params: Promise<{ filename: string }> }',
    description: 'Fix Next.js 15 route params'
  },
  {
    file: 'src/app/api/vinn/audio/[filename]/route.ts',
    search: /const filename = await params\.filename;/g,
    replace: 'const params = await context.params;\n    const filename = params.filename;',
    description: 'Fix async params access'
  },

  // 2. Return Type Issues
  {
    file: 'src/app/api/train-analyzers/route.ts',
    search: /export async function POST\(request: Request\)/g,
    replace: 'export async function POST(request: Request): Promise<Response>',
    description: 'Fix return type'
  },
  {
    file: 'src/app/api/train-analyzers/route.ts',
    search: /return new Promise\(/g,
    replace: 'return new Promise<Response>(',
    description: 'Fix Promise return type'
  },

  // 3. Import Issues
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /import { GroqClient } from 'groq-sdk';/g,
    replace: 'import Groq from \'groq-sdk\';',
    description: 'Fix Groq import'
  },
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /new GroqClient\(/g,
    replace: 'new Groq(',
    description: 'Fix Groq constructor'
  },
  {
    file: 'src/app/api/analyze/route.ts',
    search: /import { analyzeLyrics } from '@\/utils\/claudeService';/g,
    replace: 'import { analyzeLyrics } from \'@/utils/groqService\';',
    description: 'Fix analyzeLyrics import'
  },

  // 4. TypeScript Null Safety
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /text = result\.text;/g,
    replace: 'text = result.text || \'\';',
    description: 'Add null safety'
  },
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /response\.content\[0\]\.text/g,
    replace: 'response.content[0]?.text || \'\'',
    description: 'Add optional chaining'
  },
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /response\.usage\.output_tokens/g,
    replace: 'response.usage?.output_tokens || 0',
    description: 'Add optional chaining'
  },
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /response\.choices\[0\]\.message\.content/g,
    replace: 'response.choices[0]?.message?.content || \'\'',
    description: 'Add optional chaining'
  },
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /response\.usage\.completion_tokens/g,
    replace: 'response.usage?.completion_tokens || 0',
    description: 'Add optional chaining'
  },

  // 5. Array Typing
  {
    file: 'pages/api/lyrics/collaborative.ts',
    search: /const collaborativeResults = \[\];/g,
    replace: `const collaborativeResults: Array<{
      genre: string;
      writer: string;
      text?: string;
      generationTime?: number;
      tokenCount?: number;
      model?: string;
      error?: string;
    }> = [];`,
    description: 'Add array typing'
  },

  // 6. Remove Export from Enum
  {
    file: 'src/app/api/retune/emotion/route.ts',
    search: /export enum EmotionType/g,
    replace: 'enum EmotionType',
    description: 'Remove export from enum'
  }
];

// Function to apply a single fix
function applyFix(fix) {
  const filePath = fix.file;
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    content = content.replace(fix.search, fix.replace);
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ ${fix.description} in ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes needed in ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Apply all fixes
console.log('\n🔧 Applying fixes...\n');

let fixedCount = 0;
let totalFixes = FIXES.length;

for (const fix of FIXES) {
  if (applyFix(fix)) {
    fixedCount++;
  }
}

console.log(`\n📊 Summary: ${fixedCount}/${totalFixes} fixes applied\n`);

// Test build
console.log('🧪 Testing build...');
try {
  execSync('npm run build:production', { stdio: 'inherit' });
  console.log('✅ Build successful!');
} catch (error) {
  console.log('❌ Build failed. Check output above for remaining errors.');
  process.exit(1);
}

console.log('\n🎉 All deployment errors fixed! Ready to deploy.');
