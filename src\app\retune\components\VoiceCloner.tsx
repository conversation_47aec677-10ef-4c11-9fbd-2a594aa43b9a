'use client';

import { useEffect, useRef, useState } from 'react';
import { analyzeAudioChunk, analyzeAudioType } from '../../../utils/audioAnalysis';

interface VoiceClonerProps {
  onVoiceModelCreated: (model: any, score: number) => void;
}

export default function VoiceCloner({ onVoiceModelCreated }: VoiceClonerProps) {
  // State for recording
  const [isRecording, setIsRecording] = useState(false);
  const [recordedSamples, setRecordedSamples] = useState<{ blob: Blob; url: string; text: string }[]>([]);
  const [currentPrompt, setCurrentPrompt] = useState(0);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [waveformData, setWaveformData] = useState<number[]>([]);

  // Timer ref for recording duration
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // State for processing
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState(1);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [cloneQualityScore, setCloneQualityScore] = useState<number | null>(null);
  const [coverageScore, setCoverageScore] = useState<number>(0);
  const [prosodyScore, setProsodyScore] = useState<number>(0);
  const [spectralScore, setSpectralScore] = useState<number>(0);
  const [recordingQualityScore, setRecordingQualityScore] = useState<number>(0);

  // State for voice model details
  const [modelName, setModelName] = useState('My Voice');
  const [voiceType, setVoiceType] = useState<'natural' | 'singing' | 'character'>('natural');
  const [qualitySetting, setQualitySetting] = useState<'standard' | 'high' | 'ultra'>('high');

  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Phoneme coverage script - designed to cover all 44 American English phonemes
  const samplePrompts = [
    "The quick brown fox jumps over the lazy dog and feels joy.", // Basic coverage
    "My voice has unique qualities that make it distinctly mine.", // Vowel variety
    "She sells seashells by the seashore while watching the waves.", // Sibilants
    "Peter Piper picked a peck of pickled peppers for dinner.", // Plosives
    "How now, brown cow? The owl howls at midnight.", // Diphthongs
    "Truly rural areas require rugged vehicles for transportation.", // R-colored vowels
    "The measure of a treasure is not in its weight but in its worth.", // Voiced fricatives
    "That thing over there is the one I think they want.", // Dental fricatives
    "A journey of a thousand miles begins with a single step forward.", // Nasals
    "The rain in Spain stays mainly in the plain, creating puddles.", // Pitch variation
    "To be or not to be, that is the question we must answer.", // Prosody exercise
    "All that glitters is not gold; all who wander are not lost.", // Rhythm variation
    "Please raise your voice slightly higher than your normal speaking tone.", // Pitch range
    "Now speak in a lower register, as deep as comfortably possible.", // Low range
    "Sing this simple melody: do, re, mi, fa, sol, la, ti, do.", // Melodic content
  ];

  // Function to generate random waveform data for visualization
  const generateWaveformData = () => {
    // Generate random waveform data based on audio level
    const newData = Array.from({ length: 50 }, () => Math.random() * audioLevel);
    setWaveformData(prev => [...prev.slice(-150), ...newData]);
  };

  // Start recording function
  const startRecording = async () => {
    audioChunksRef.current = [];
    setWaveformData([]);
    setRecordingTime(0);
    setAudioLevel(0);

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      // Set up audio analyzer for visualizations
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const audioSource = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      audioSource.connect(analyser);

      // Start timer for recording duration
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);

        // Update audio level and waveform visualization
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        analyser.getByteFrequencyData(dataArray);

        // Calculate average level
        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        const normalizedLevel = Math.min(100, average * 1.5); // Scale for better visualization
        setAudioLevel(normalizedLevel);

        // Generate waveform data
        generateWaveformData();
      }, 100);

      mediaRecorder.ondataavailable = async (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);

          // Real-time analysis of audio chunk
          try {
            const analysis = await analyzeAudioChunk(event.data);

            // In a real implementation, we would use this data to update the visualization
            // For now, we're using the simulated data from the timer
          } catch (error) {
            console.error('Error analyzing audio chunk:', error);
          }
        }
      };

      mediaRecorder.onstop = () => {
        // Clear recording timer
        if (recordingTimerRef.current) {
          clearInterval(recordingTimerRef.current);
          recordingTimerRef.current = null;
        }

        // Reset visualization data
        setAudioLevel(0);
        setRecordingTime(0);

        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Add to recorded samples
        setRecordedSamples(prev => [
          ...prev,
          { blob: audioBlob, url: audioUrl, text: samplePrompts[currentPrompt] }
        ]);

        // Move to next prompt if available
        if (currentPrompt < samplePrompts.length - 1) {
          setCurrentPrompt(prev => prev + 1);
        }

        // Update coverage score - 30% weight in final score
        const newCoverageScore = Math.min(100, ((recordedSamples.length + 1) / samplePrompts.length) * 100);
        setCoverageScore(newCoverageScore);

        // Analyze audio for other metrics
        // In a real implementation, these would come from actual audio analysis
        // For now, we'll simulate with increasing scores as more samples are recorded

        // Prosody & Pitch - 25% weight in final score
        // Measures pitch stability and natural intonation patterns
        const newProsodyScore = Math.min(100, 50 + (recordedSamples.length + 1) * 3);
        setProsodyScore(newProsodyScore);

        // Spectral & Timbre Features - 25% weight in final score
        // Measures consistency in vocal timbre and spectral characteristics
        const newSpectralScore = Math.min(100, 60 + (recordedSamples.length + 1) * 2.5);
        setSpectralScore(newSpectralScore);

        // Recording Quality & SNR - 20% weight in final score
        // Measures signal-to-noise ratio and overall recording clarity
        const newRecordingQualityScore = Math.min(100, 70 + (recordedSamples.length + 1) * 2);
        setRecordingQualityScore(newRecordingQualityScore);

        // Calculate weighted Clone Quality Score (0-100)
        const weightedScore = (
          (newCoverageScore * 0.30) +  // Phoneme Coverage: 30%
          (newProsodyScore * 0.25) +   // Pitch Stability: 25%
          (newSpectralScore * 0.25) +  // Timbre Consistency: 25%
          (newRecordingQualityScore * 0.20)  // Noise & Clarity: 20%
        );

        setCloneQualityScore(Math.round(weightedScore));
      };

      mediaRecorder.start(100); // Collect data every 100ms for smoother visualization
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Error accessing your microphone. Please check permissions and try again.');
    }
  };

  // Stop recording function
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      // Clear recording timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Stop all audio tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  };

  // State for audio content type
  const [isSingingContent, setIsSingingContent] = useState(false);

  // Create voice model function
  const createVoiceModel = async () => {
    if (recordedSamples.length === 0) {
      alert('Please record at least one sample before creating a voice model.');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessingStep(1);
    setProcessingProgress(0);

    try {
      // Step 1: Audio Analysis
      setProcessingStep(1);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      // Analyze all recorded samples to determine if singing or speech
      const audioAnalysisPromises = recordedSamples.map(sample =>
        analyzeAudioType(sample.blob)
      );

      const audioAnalysisResults = await Promise.all(audioAnalysisPromises);
      setProcessingProgress(100);

      // Calculate average melodic content
      const avgMelodicContent = audioAnalysisResults.reduce(
        (sum, result) => sum + result.melodicContent, 0
      ) / audioAnalysisResults.length;

      // Determine if content is predominantly singing by analyzing melodic content
      // Higher melodic content indicates singing, lower indicates speech/rap
      const isSinging = avgMelodicContent > 0.5;
      setIsSingingContent(isSinging);

      // Select appropriate model type based on content analysis
      // VocalSynth™ (so-vits-svc) for singing voice synthesis
      // FlowClone™ (RVC) for rap and spoken word with authentic flow
      const modelType = isSinging ? 'vocalsync' : 'flowclone';

      console.log(`Content analysis complete: ${isSinging ? 'Singing' : 'Speech/Rap'} content detected`);
      console.log(`Selected model: ${isSinging ? 'VocalSynth™' : 'FlowClone™'} (${modelType})`);

      // Step 2: Feature Extraction
      setProcessingStep(2);
      setProcessingProgress(0);

      // Simulate feature extraction progress
      const featureExtractionInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 95) {
            clearInterval(featureExtractionInterval);
            return prev;
          }
          return prev + 2;
        });
      }, 200);

      // Simulate feature extraction delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      setProcessingProgress(100);

      // Step 3: Model Training
      setProcessingStep(3);
      setProcessingProgress(0);

      // Simulate model training progress
      const modelTrainingInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 95) {
            clearInterval(modelTrainingInterval);
            return prev;
          }
          return prev + 1;
        });
      }, 150);

      // Create a FormData object to send the audio files
      const formData = new FormData();
      recordedSamples.forEach((sample, index) => {
        formData.append(`audio_${index}`, sample.blob);
        formData.append(`text_${index}`, sample.text);
      });

      // Add metadata about recording type
      formData.append('is_singing', isSinging.toString());
      formData.append('model_type', modelType);
      formData.append('name', modelName);
      formData.append('voice_type', voiceType);
      formData.append('quality_setting', qualitySetting);

      // Send to backend for processing with our local repositories
      const response = await fetch('/api/retune/clone', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        clearInterval(modelTrainingInterval);
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create voice model');
      }

      const data = await response.json();
      setProcessingProgress(100);

      // Step 4: Finalizing
      setProcessingStep(4);
      setProcessingProgress(0);

      // Simulate finalizing progress
      const finalizingInterval = setInterval(() => {
        setProcessingProgress(prev => {
          if (prev >= 95) {
            clearInterval(finalizingInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 100);

      // Simulate finalizing delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      clearInterval(finalizingInterval);
      setProcessingProgress(100);

      // Call the callback function with the model and score
      onVoiceModelCreated(data.model, data.qualityScore);

      // Short delay before completing
      await new Promise(resolve => setTimeout(resolve, 500));
      setIsProcessing(false);
    } catch (error) {
      console.error('Error creating voice model:', error);
      setError(`Error creating your voice model: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsProcessing(false);
      setProcessingStep(1);
      setProcessingProgress(0);
    }
  };

  // Reset function
  const resetRecording = () => {
    // Revoke object URLs to prevent memory leaks
    recordedSamples.forEach(sample => URL.revokeObjectURL(sample.url));

    setRecordedSamples([]);
    setCurrentPrompt(0);
    setCoverageScore(0);
    setProsodyScore(0);
    setSpectralScore(0);
    setRecordingQualityScore(0);
    setCloneQualityScore(null);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      recordedSamples.forEach(sample => URL.revokeObjectURL(sample.url));
    };
  }, [recordedSamples]);

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
      <h2 className="text-2xl font-semibold mb-4">Voice Cloning</h2>
      <p className="text-gray-300 mb-2">
        Create a digital model of your voice by reading the provided phrases. For best results, record in a quiet environment and speak naturally.
      </p>
      <div className="bg-blue-900/20 border border-blue-800/30 rounded-lg p-4 mb-6">
        <h3 className="text-sm font-medium text-blue-400 mb-2">Billboard-Ready Voice Cloning Technology</h3>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="px-2 py-1 bg-blue-900/30 text-xs rounded border border-blue-700/30">VocalSynth™ (Singing)</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">FlowClone™ (Rap/Spoken)</span>
          <span className="px-2 py-1 bg-green-900/30 text-xs rounded border border-green-700/30">Vinn AI Enhancement</span>
        </div>
        <p className="text-xs text-gray-400 mb-3">
          Our proprietary system uses VocalSynth™ technology for singing voice synthesis and FlowClone™ technology for rap and spoken word,
          enhanced with Vinn AI's advanced voice modeling for natural-sounding results.
        </p>

        <h4 className="text-xs font-medium text-blue-400 mb-1">Voice Cloning Process:</h4>
        <ol className="text-xs text-gray-400 list-decimal pl-5 mb-3 space-y-1">
          <li>Record standardized script (captures all 44 American English phonemes)</li>
          <li>Extract acoustic features (pitch, timbre, spectral characteristics)</li>
          <li>Compute Clone Quality Score based on four weighted metrics</li>
          <li>Generate voice model using VocalSynth™ or FlowClone™ based on content type</li>
        </ol>

        <h4 className="text-xs font-medium text-blue-400 mb-1">Clone Quality Score Components:</h4>
        <ul className="text-xs text-gray-400 grid grid-cols-2 gap-x-2 gap-y-1 mb-2">
          <li><span className="text-green-400">Phoneme Coverage:</span> 30%</li>
          <li><span className="text-blue-400">Pitch Stability:</span> 25%</li>
          <li><span className="text-purple-400">Timbre Consistency:</span> 25%</li>
          <li><span className="text-yellow-400">Noise & Clarity:</span> 20%</li>
        </ul>

        <div className="mt-1 text-xs text-gray-400 bg-gray-800/50 p-2 rounded border border-gray-700">
          <span className="text-blue-400 font-medium">Quality Threshold:</span> A Clone Quality Score of ≥85 is required for optimal results
        </div>
      </div>

      {/* Recording Interface */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Left: Prompt and Recording Controls */}
          <div className="flex-1 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
            <h3 className="text-lg font-medium mb-3 text-blue-400">Recording Script</h3>

            {/* Current Prompt with Numbered Indicator */}
            <div className="flex items-center mb-2">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-bold mr-2">
                {currentPrompt + 1}
              </div>
              <div className="text-sm text-gray-400">
                Sample {currentPrompt + 1} of {samplePrompts.length}
              </div>
            </div>

            <div className="bg-gray-800/70 p-4 rounded-lg mb-4 min-h-[100px] flex items-center justify-center relative">
              <p className="text-xl text-center">
                {currentPrompt < samplePrompts.length ? samplePrompts[currentPrompt] : "All prompts completed!"}
              </p>

              {/* Recording Indicator */}
              {isRecording && (
                <div className="absolute top-2 right-2 flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse mr-1"></div>
                  <span className="text-xs text-red-400">
                    {Math.floor(recordingTime / 10)}:{(recordingTime % 10).toString().padStart(1, '0')}
                  </span>
                </div>
              )}
            </div>

            {/* Waveform Visualization */}
            {isRecording && (
              <div className="mb-4 bg-gray-900/70 rounded-lg p-2 h-20 overflow-hidden">
                <div className="flex items-center justify-center h-full">
                  {waveformData.map((value, index) => (
                    <div
                      key={index}
                      className="w-1 mx-px bg-blue-500"
                      style={{
                        height: `${value}%`,
                        opacity: index / waveformData.length + 0.3
                      }}
                    ></div>
                  ))}
                </div>
              </div>
            )}

            {/* Audio Level Meter */}
            {isRecording && (
              <div className="mb-4">
                <div className="flex justify-between text-xs text-gray-400 mb-1">
                  <span>Audio Level</span>
                  <span>{Math.round(audioLevel)}%</span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className={`h-full ${audioLevel > 80 ? 'bg-red-500' : audioLevel > 50 ? 'bg-green-500' : 'bg-blue-500'}`}
                    style={{ width: `${audioLevel}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Recording Controls */}
            <div className="flex justify-center gap-4">
              {!isRecording ? (
                <button
                  onClick={startRecording}
                  disabled={currentPrompt >= samplePrompts.length}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-medium transition-colors disabled:opacity-50 flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  Start Recording
                </button>
              ) : (
                <button
                  onClick={stopRecording}
                  className="px-6 py-3 bg-red-600 hover:bg-red-500 text-white rounded-lg font-medium transition-colors flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                  </svg>
                  Stop Recording
                </button>
              )}

              <button
                onClick={resetRecording}
                disabled={isRecording || recordedSamples.length === 0}
                className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50 flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Reset
              </button>
            </div>

            {/* Progress Indicator */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-400 mb-1">
                <span>Progress</span>
                <span>{recordedSamples.length} / {samplePrompts.length} samples</span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600"
                  style={{ width: `${(recordedSamples.length / samplePrompts.length) * 100}%` }}
                ></div>
              </div>
              <div className="flex justify-between mt-1">
                <div className="text-xs text-gray-500">
                  {recordedSamples.length === 0 ? 'No samples recorded' :
                    recordedSamples.length < 5 ? 'Keep going! More samples = better quality' :
                      recordedSamples.length < 10 ? 'Good progress! A few more for best results' :
                        'Great job! You have enough samples for high quality'}
                </div>
                <div className="text-xs text-blue-400">
                  {Math.min(100, Math.round((recordedSamples.length / samplePrompts.length) * 100))}%
                </div>
              </div>
            </div>
          </div>

          {/* Right: Quality Metrics */}
          <div className="flex-1 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
            <h3 className="text-lg font-medium mb-3 text-blue-400">Clone Quality Score</h3>

            {/* Overall Score */}
            <div className="mb-4 text-center">
              <div
                className={`inline-block w-28 h-28 rounded-full border-4 flex items-center justify-center mb-2 ${cloneQualityScore !== null
                  ? cloneQualityScore >= 85
                    ? 'border-green-500 bg-green-900/20'
                    : cloneQualityScore >= 70
                      ? 'border-yellow-500 bg-yellow-900/20'
                      : 'border-red-500 bg-red-900/20'
                  : 'border-gray-600 bg-gray-900/20'
                  }`}
              >
                <div className="text-center">
                  <span className="text-4xl font-bold block">
                    {cloneQualityScore !== null ? cloneQualityScore : '—'}
                  </span>
                  <span className="text-xs text-gray-400 block mt-1">Clone Score</span>
                </div>
              </div>
              <div className="text-sm">
                {cloneQualityScore !== null ? (
                  <span className={`font-medium ${cloneQualityScore >= 85 ? 'text-green-400' :
                    cloneQualityScore >= 70 ? 'text-yellow-400' :
                      'text-red-400'
                    }`}>
                    {cloneQualityScore >= 85 ? 'Excellent' :
                      cloneQualityScore >= 70 ? 'Good' :
                        cloneQualityScore >= 50 ? 'Average' : 'Poor'}
                  </span>
                ) : (
                  <span className="text-gray-400">Not yet scored</span>
                )}
              </div>
              {cloneQualityScore !== null && (
                <div className="mt-2 text-xs text-gray-400">
                  {cloneQualityScore >= 85 ? (
                    <span className="text-green-400">✓ Ready for voice cloning</span>
                  ) : (
                    <span className="text-yellow-400">⚠ Need more samples (target: ≥85)</span>
                  )}
                </div>
              )}
            </div>

            {/* Individual Metrics */}
            <div className="space-y-3">
              {/* Phoneme Coverage - 30% weight */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <span className="text-green-400 font-medium">Phoneme Coverage</span>
                    <span className="ml-2 text-xs text-gray-500 bg-gray-800 px-1.5 py-0.5 rounded">30%</span>
                  </span>
                  <span className="text-gray-400">{Math.round(coverageScore)}%</span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-green-500"
                    style={{ width: `${coverageScore}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Measures complete phoneme inventory coverage (all 44 sounds)
                </div>
              </div>

              {/* Prosody & Pitch - 25% weight */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <span className="text-blue-400 font-medium">Prosody & Pitch</span>
                    <span className="ml-2 text-xs text-gray-500 bg-gray-800 px-1.5 py-0.5 rounded">25%</span>
                  </span>
                  <span className="text-gray-400">{Math.round(prosodyScore)}%</span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-blue-500"
                    style={{ width: `${prosodyScore}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Measures pitch stability, intonation patterns, and melodic range
                </div>
              </div>

              {/* Spectral Features - 25% weight */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <span className="text-purple-400 font-medium">Timbre Consistency</span>
                    <span className="ml-2 text-xs text-gray-500 bg-gray-800 px-1.5 py-0.5 rounded">25%</span>
                  </span>
                  <span className="text-gray-400">{Math.round(spectralScore)}%</span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-purple-500"
                    style={{ width: `${spectralScore}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Measures spectral characteristics and vocal timbre consistency
                </div>
              </div>

              {/* Recording Quality - 20% weight */}
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <span className="text-yellow-400 font-medium">Recording Quality</span>
                    <span className="ml-2 text-xs text-gray-500 bg-gray-800 px-1.5 py-0.5 rounded">20%</span>
                  </span>
                  <span className="text-gray-400">{Math.round(recordingQualityScore)}%</span>
                </div>
                <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-yellow-500"
                    style={{ width: `${recordingQualityScore}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Measures signal-to-noise ratio (SNR) and audio clarity
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recorded Samples */}
      {recordedSamples.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-3 text-blue-400">Recorded Samples</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recordedSamples.map((sample, index) => (
              <div key={index} className="bg-gray-900/50 p-3 rounded-lg border border-gray-800">
                <p className="text-sm text-gray-300 mb-2 line-clamp-1">{sample.text}</p>
                <audio controls className="w-full h-8">
                  <source src={sample.url} type="audio/wav" />
                  Your browser does not support the audio element.
                </audio>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-800 rounded-lg text-red-200 text-sm">
          {error}
        </div>
      )}

      {/* Create Voice Model Button */}
      <div className="mt-8 bg-gray-900/50 p-6 rounded-lg border border-gray-800">
        <div className="text-center mb-4">
          <h3 className="text-xl font-semibold mb-2">Ready to Create Your Voice Model?</h3>
          <p className="text-gray-400 text-sm mb-4">
            {cloneQualityScore !== null && cloneQualityScore >= 85 ? (
              "Your voice samples have reached the quality threshold. You're ready to create your voice model!"
            ) : recordedSamples.length > 0 ? (
              "Continue recording samples to improve your Clone Quality Score for best results."
            ) : (
              "Record voice samples above to create your digital voice model."
            )}
          </p>

          {/* Voice Model Settings */}
          {!isProcessing && recordedSamples.length > 0 && (
            <div className="mb-6 max-w-md mx-auto">
              <div className="bg-gray-800/70 rounded-lg p-4 mb-4">
                <h4 className="text-sm font-medium text-blue-400 mb-3">Voice Model Settings</h4>

                <div className="mb-3">
                  <label htmlFor="model-name" className="block text-sm text-gray-300 mb-1">Model Name</label>
                  <input
                    type="text"
                    id="model-name"
                    value={modelName}
                    onChange={(e) => setModelName(e.target.value)}
                    className="w-full bg-black/30 border border-gray-700 rounded px-3 py-2 text-white text-sm"
                    placeholder="My Voice"
                  />
                </div>

                <div className="mb-3">
                  <label className="block text-sm text-gray-300 mb-1">Voice Type</label>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      onClick={() => setVoiceType('natural')}
                      className={`py-2 px-3 text-xs rounded-lg ${voiceType === 'natural'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      Natural
                    </button>
                    <button
                      onClick={() => setVoiceType('singing')}
                      className={`py-2 px-3 text-xs rounded-lg ${voiceType === 'singing'
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      Singing
                    </button>
                    <button
                      onClick={() => setVoiceType('character')}
                      className={`py-2 px-3 text-xs rounded-lg ${voiceType === 'character'
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      Character
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm text-gray-300 mb-1">Quality Setting</label>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      onClick={() => setQualitySetting('standard')}
                      className={`py-2 px-3 text-xs rounded-lg ${qualitySetting === 'standard'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      Standard
                    </button>
                    <button
                      onClick={() => setQualitySetting('high')}
                      className={`py-2 px-3 text-xs rounded-lg ${qualitySetting === 'high'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      High
                    </button>
                    <button
                      onClick={() => setQualitySetting('ultra')}
                      className={`py-2 px-3 text-xs rounded-lg ${qualitySetting === 'ultra'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                        }`}
                    >
                      Ultra
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Processing Steps Visualization */}
          {isProcessing && (
            <div className="mb-6 max-w-md mx-auto">
              <div className="bg-gray-800/70 rounded-lg p-4 mb-4">
                <h4 className="text-sm font-medium text-blue-400 mb-3">Creating Voice Model</h4>

                <div className="space-y-4">
                  {/* Step 1: Audio Analysis */}
                  <div>
                    <div className="flex items-center mb-1">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${processingStep >= 1
                          ? processingStep === 1 ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
                          : 'bg-gray-700 text-gray-400'
                        }`}>
                        {processingStep > 1 ? '✓' : '1'}
                      </div>
                      <span className={`text-sm font-medium ${processingStep >= 1
                          ? processingStep === 1 ? 'text-blue-400' : 'text-green-400'
                          : 'text-gray-500'
                        }`}>
                        Audio Analysis
                      </span>
                      {processingStep === 1 && (
                        <span className="ml-auto text-xs text-gray-400">{processingProgress}%</span>
                      )}
                    </div>
                    {processingStep === 1 && (
                      <div className="h-1 bg-gray-700 rounded-full overflow-hidden ml-8">
                        <div
                          className="h-full bg-blue-600"
                          style={{ width: `${processingProgress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>

                  {/* Step 2: Feature Extraction */}
                  <div>
                    <div className="flex items-center mb-1">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${processingStep >= 2
                          ? processingStep === 2 ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
                          : 'bg-gray-700 text-gray-400'
                        }`}>
                        {processingStep > 2 ? '✓' : '2'}
                      </div>
                      <span className={`text-sm font-medium ${processingStep >= 2
                          ? processingStep === 2 ? 'text-blue-400' : 'text-green-400'
                          : 'text-gray-500'
                        }`}>
                        Feature Extraction
                      </span>
                      {processingStep === 2 && (
                        <span className="ml-auto text-xs text-gray-400">{processingProgress}%</span>
                      )}
                    </div>
                    {processingStep === 2 && (
                      <div className="h-1 bg-gray-700 rounded-full overflow-hidden ml-8">
                        <div
                          className="h-full bg-blue-600"
                          style={{ width: `${processingProgress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>

                  {/* Step 3: Model Training */}
                  <div>
                    <div className="flex items-center mb-1">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${processingStep >= 3
                          ? processingStep === 3 ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
                          : 'bg-gray-700 text-gray-400'
                        }`}>
                        {processingStep > 3 ? '✓' : '3'}
                      </div>
                      <span className={`text-sm font-medium ${processingStep >= 3
                          ? processingStep === 3 ? 'text-blue-400' : 'text-green-400'
                          : 'text-gray-500'
                        }`}>
                        Model Training
                      </span>
                      {processingStep === 3 && (
                        <span className="ml-auto text-xs text-gray-400">{processingProgress}%</span>
                      )}
                    </div>
                    {processingStep === 3 && (
                      <div className="h-1 bg-gray-700 rounded-full overflow-hidden ml-8">
                        <div
                          className="h-full bg-blue-600"
                          style={{ width: `${processingProgress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>

                  {/* Step 4: Finalizing */}
                  <div>
                    <div className="flex items-center mb-1">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${processingStep >= 4
                          ? processingStep === 4 ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'
                          : 'bg-gray-700 text-gray-400'
                        }`}>
                        {processingStep > 4 ? '✓' : '4'}
                      </div>
                      <span className={`text-sm font-medium ${processingStep >= 4
                          ? processingStep === 4 ? 'text-blue-400' : 'text-green-400'
                          : 'text-gray-500'
                        }`}>
                        Finalizing
                      </span>
                      {processingStep === 4 && (
                        <span className="ml-auto text-xs text-gray-400">{processingProgress}%</span>
                      )}
                    </div>
                    {processingStep === 4 && (
                      <div className="h-1 bg-gray-700 rounded-full overflow-hidden ml-8">
                        <div
                          className="h-full bg-blue-600"
                          style={{ width: `${processingProgress}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-4 text-center text-sm text-gray-400">
                  {processingStep === 1 && "Analyzing audio samples and determining voice characteristics..."}
                  {processingStep === 2 && "Extracting acoustic features from your voice samples..."}
                  {processingStep === 3 && "Training your voice model with advanced AI technology..."}
                  {processingStep === 4 && "Finalizing your voice model and preparing for use..."}
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button
              onClick={createVoiceModel}
              disabled={isProcessing || recordedSamples.length === 0}
              className={`px-8 py-4 rounded-lg font-medium transition-colors flex items-center justify-center ${isProcessing
                  ? 'bg-gray-600 text-gray-300'
                  : cloneQualityScore !== null && cloneQualityScore >= 85
                    ? 'bg-green-600 hover:bg-green-500 text-white'
                    : 'bg-blue-600 hover:bg-blue-500 text-white'
                }`}
            >
              {isProcessing ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Voice Model...
                </span>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  Create Voice Model
                </>
              )}
            </button>
          </div>
        </div>

        {/* Next Steps - Connection to Retune */}
        <div className="mt-4 border-t border-gray-800 pt-4">
          <h4 className="text-sm font-medium text-blue-400 mb-2">Next Steps After Voice Cloning</h4>
          <div className="flex items-start gap-4">
            <div className="flex-1">
              <p className="text-xs text-gray-400 mb-2">
                After creating your voice model, you'll be able to enhance it with Retune to achieve professional,
                billboard-ready quality. Retune adds clarity, resonance, and confidence to your voice.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 bg-blue-900/30 text-xs rounded border border-blue-700/30">Clarity Enhancement</span>
                <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Resonance Boost</span>
                <span className="px-2 py-1 bg-green-900/30 text-xs rounded border border-green-700/30">Confidence Amplifier</span>
              </div>
            </div>
            <div className="hidden md:block">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-blue-500 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Voice Cloning Guide */}
      <div className="mt-8 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-medium text-blue-400 mb-3">Voice Cloning Guide</h3>

        <div className="space-y-4">
          <div>
            <h4 className="text-md font-medium text-white mb-1">How It Works</h4>
            <p className="text-sm text-gray-400">
              Our zero-shot voice cloning system uses two different proprietary technologies depending on your intended use:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2 mb-3">
              <div className="bg-blue-900/20 p-3 rounded-lg border border-blue-800/30">
                <h5 className="text-sm font-medium text-blue-400 mb-1">VocalSynth™</h5>
                <p className="text-xs text-gray-400 mb-1">For singing voice synthesis with natural pitch control</p>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Preserves pitch, vibrato, and vocal color</li>
                  <li>Supports cross-lingual voice conversion</li>
                  <li>Maintains musical expression and timing</li>
                  <li>Ideal for melody-focused content</li>
                </ul>
              </div>
              <div className="bg-purple-900/20 p-3 rounded-lg border border-purple-800/30">
                <h5 className="text-sm font-medium text-purple-400 mb-1">FlowClone™</h5>
                <p className="text-xs text-gray-400 mb-1">For rap and spoken word with authentic flow</p>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Preserves rapid prosody changes essential for rap</li>
                  <li>Maintains natural flow and rhythm</li>
                  <li>Protects voiceless consonants for clear articulation</li>
                  <li>Ideal for rhythm-focused content</li>
                </ul>
              </div>
            </div>

            <h5 className="text-sm font-medium text-white mb-1">Technical Implementation Details</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
              <div>
                <h6 className="text-xs font-medium text-green-400 mb-1">1. Phoneme Coverage (30%)</h6>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Phoneme Inventory: 44 sounds (24 consonants, 20 vowels/diphthongs)</li>
                  <li>Script Design: Covers each phoneme at least once</li>
                  <li>Alignment Tool: Verifies coverage and confidence scores</li>
                  <li>Target: ≥95% phoneme presence</li>
                </ul>
              </div>
              <div>
                <h6 className="text-xs font-medium text-blue-400 mb-1">2. Prosody & Pitch Mapping (25%)</h6>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Pitch Range: 80–300 Hz (male), 165–600 Hz (female)</li>
                  <li>Contour Extraction: Frame-wise fundamental frequency (F0)</li>
                  <li>Interval Coverage: Melodic steps, skips, and glides</li>
                  <li>Target: F0 variance &lt; 15%</li>
                </ul>
              </div>
              <div>
                <h6 className="text-xs font-medium text-purple-400 mb-1">3. Spectral & Timbre Features (25%)</h6>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Mel-Spectrogram: 80 bins, 25 ms window, 10 ms hop</li>
                  <li>MFCCs: 13 coefficients per frame for vocal identity</li>
                  <li>Spectral Centroid & Flux: Measures brightness and change rates</li>
                  <li>Target: Spectral drift &lt; 10%</li>
                </ul>
              </div>
              <div>
                <h6 className="text-xs font-medium text-yellow-400 mb-1">4. Recording Quality & SNR (20%)</h6>
                <ul className="text-xs text-gray-400 list-disc pl-4 space-y-0.5">
                  <li>Sample Rate: 16 kHz/16-bit (speech), 44.1 kHz/24-bit (singing)</li>
                  <li>Signal-to-Noise Ratio: Aim for ≥30 dB</li>
                  <li>Environment: Treated room, cardioid mic at 10–20 cm distance</li>
                  <li>Target: SNR ≥30 dB</li>
                </ul>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Tips for Better Results</h4>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li>Record in a quiet environment with minimal background noise</li>
              <li>Use a good quality microphone for clearer recordings</li>
              <li>Speak naturally with your normal voice and pace</li>
              <li>Include a variety of sentences with different sounds and intonations</li>
              <li>Record all sample phrases for best quality (minimum 10 recommended)</li>
              <li>For singing voice cloning, include melodic content with varied pitch</li>
              <li>For rap/spoken word, include rhythmic patterns with varied flow</li>
              <li>Aim for a Clone Quality Score of 85 or higher</li>
            </ul>
          </div>

          <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-800">
            <h4 className="text-md font-medium text-white mb-1">Workflow</h4>
            <ol className="text-sm text-gray-400 list-decimal pl-5 space-y-1">
              <li>Record Script → Export WAV</li>
              <li>Analyze Features → Compute metrics & score</li>
              <li>If Score &lt; 85 → Prompt user to re-record</li>
              <li>Clone with VocalSynth™ / FlowClone™ → Produce raw clone</li>
              <li>Deliver & Log metrics → Ready for Retune enhancement</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
