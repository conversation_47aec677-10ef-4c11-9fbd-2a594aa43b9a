'use client';

import VoicemodEffectsPanel from '@/components/retune/VoicemodEffectsPanel';
import VoiceModelSelector from '@/components/Retune/VoiceModelSelector';
import React, { useEffect, useState } from 'react';
import AuphonicEnhancement from './AuphonicEnhancement';
import EmotionDetection from './EmotionDetection';
import RetunePipeline from './RetunePipeline';

// Define voice model type
interface VoiceModel {
  id: string;
  name: string;
  type: 'vocalsync' | 'flowclone';
  qualityScore: number;
  createdAt: string;
}

interface VoiceEnhanceContentProps {
  voiceModel: VoiceModel;
  voiceModelScore: number | null;
}

const VoiceEnhanceContent: React.FC<VoiceEnhanceContentProps> = ({
  voiceModel,
  voiceModelScore
}) => {
  // State for audio file
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [enhancedAudioUrl, setEnhancedAudioUrl] = useState<string | null>(null);

  // State for processing
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for enhancement settings
  const [enhancementLevel, setEnhancementLevel] = useState<number>(50);
  const [selectedPreset, setSelectedPreset] = useState<string>('none');
  const [voiceType, setVoiceType] = useState<string>('auto');

  // State for active enhancement tab
  const [activeEnhancementTab, setActiveEnhancementTab] = useState<'vocalsynth' | 'auphonic' | 'imentiv' | 'pipeline' | 'voicemod'>('vocalsynth');

  // State for enhancement options
  const [enhancementOptions, setEnhancementOptions] = useState({
    pitchCorrection: false,
    noiseReduction: true,
    clarityBoost: true,
    warmth: false,
    presence: true,
    deEssing: false
  });

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAudioFile(file);

      // Create URL for audio preview
      const url = URL.createObjectURL(file);
      setAudioUrl(url);

      // Reset enhanced audio
      setEnhancedAudioUrl(null);
    }
  };

  // Handle enhancement option toggle
  const toggleEnhancementOption = (option: keyof typeof enhancementOptions) => {
    setEnhancementOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  // Handle preset selection
  const handlePresetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const preset = e.target.value;
    setSelectedPreset(preset);

    // Apply preset settings
    switch (preset) {
      case 'pop':
        setEnhancementOptions({
          pitchCorrection: true,
          noiseReduction: true,
          clarityBoost: true,
          warmth: false,
          presence: true,
          deEssing: true
        });
        setEnhancementLevel(65);
        break;
      case 'rap':
        setEnhancementOptions({
          pitchCorrection: false,
          noiseReduction: true,
          clarityBoost: true,
          warmth: true,
          presence: true,
          deEssing: false
        });
        setEnhancementLevel(70);
        break;
      case 'rnb':
        setEnhancementOptions({
          pitchCorrection: true,
          noiseReduction: true,
          clarityBoost: false,
          warmth: true,
          presence: true,
          deEssing: true
        });
        setEnhancementLevel(60);
        break;
      case 'rock':
        setEnhancementOptions({
          pitchCorrection: false,
          noiseReduction: true,
          clarityBoost: true,
          warmth: true,
          presence: true,
          deEssing: false
        });
        setEnhancementLevel(75);
        break;
      case 'podcast':
        setEnhancementOptions({
          pitchCorrection: false,
          noiseReduction: true,
          clarityBoost: true,
          warmth: false,
          presence: true,
          deEssing: true
        });
        setEnhancementLevel(55);
        break;
      default:
        // No preset - don't change current settings
        break;
    }
  };

  // Process audio enhancement
  const handleProcess = async () => {
    if (!audioFile) return;

    setIsProcessing(true);
    setError(null);

    try {
      // Simulate processing
      setTimeout(() => {
        // Create a dummy enhanced audio URL
        const dummyBlob = new Blob(['dummy enhanced audio data'], { type: 'audio/wav' });
        const url = URL.createObjectURL(dummyBlob);
        setEnhancedAudioUrl(url);
        setIsProcessing(false);
      }, 3000);

      // In a real implementation, you would:
      // 1. Create FormData with the audio file and settings
      // 2. Send it to your API endpoint
      // 3. Process the response and set the enhanced audio URL
    } catch (error) {
      console.error('Error enhancing audio:', error);
      setError(`Error enhancing audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsProcessing(false);
    }
  };

  // Handle Auphonic enhancement complete
  const handleAuphonicEnhancementComplete = (audioUrl: string) => {
    setEnhancedAudioUrl(audioUrl);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) URL.revokeObjectURL(audioUrl);
      if (enhancedAudioUrl) URL.revokeObjectURL(enhancedAudioUrl);
    };
  }, [audioUrl, enhancedAudioUrl]);

  // Enhancement Option Component
  const EnhancementOption = ({ label, isActive, onClick }: { label: string; isActive: boolean; onClick: () => void }) => (
    <button
      onClick={onClick}
      className={`px-3 py-1.5 text-xs rounded-md transition-all duration-300 ${isActive
        ? 'bg-black/40 border border-[#00FFEE]/60 shadow-[0_0_10px_rgba(0,255,238,0.3)] text-white'
        : 'bg-black/30 text-white/70 hover:bg-black/40 border border-white/10 hover:border-[#00FFEE]/30'
        }`}
      style={{
        transition: 'all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)'
      }}
    >
      {label}
    </button>
  );

  // Enhancement Tab Button Component
  const EnhancementTabButton = ({
    label,
    isActive,
    onClick
  }: {
    id?: 'vocalsynth' | 'auphonic' | 'imentiv' | 'pipeline' | 'voicemod';
    label: string;
    isActive: boolean;
    onClick: () => void
  }) => (
    <button
      onClick={onClick}
      className={`px-4 py-2 text-sm rounded-t-lg transition-all duration-300 ${isActive
        ? 'bg-black/40 border-t border-l border-r border-[#00FFEE]/30 text-[#00FFEE] font-medium'
        : 'bg-black/20 text-white/70 hover:bg-black/30 border-t border-l border-r border-white/10'
        }`}
    >
      {label}
    </button>
  );

  return (
    <div className="space-y-8">
      <div className="retune-glass-container p-6">
        <h2 className="retune-title text-2xl mb-4">VocalSynth™ Voice Enhancement</h2>
        <p className="text-white/80 mb-6 retune-subtitle">
          Enhance your {voiceModel.type === 'vocalsync' ? 'singing voice' : 'spoken voice'} with professional-grade processing.
          Your voice model has a quality score of {voiceModelScore}%.
        </p>

        {/* Voice Model Selector */}
        <div className="mb-6">
          <VoiceModelSelector type="enhancement" />
        </div>

        {/* Voice Model Information */}
        <div className="p-4 mb-6 bg-black/30 rounded-lg border border-[#00FFEE]/20">
          <h3 className="text-sm font-medium retune-text-glow-cyan mb-2">Voice Model Information</h3>
          <div className="flex flex-wrap gap-2 mb-3">
            <span className="px-2 py-1 bg-black/40 text-xs rounded-md border border-white/10">
              {voiceModel.type === 'vocalsync' ? 'VocalSynth™ (Singing)' : 'FlowClone™ (Spoken)'}
            </span>
            <span className={`px-2 py-1 text-xs rounded-md border ${voiceModelScore && voiceModelScore >= 85
              ? 'bg-black/40 border-[#00FFEE]/40 text-[#00FFEE]'
              : voiceModelScore && voiceModelScore >= 70
                ? 'bg-black/40 border-yellow-400/40 text-yellow-400'
                : 'bg-black/40 border-red-400/40 text-red-400'
              }`}>
              Quality Score: {voiceModelScore}%
            </span>
          </div>
          <p className="text-xs text-white/80">
            Your voice model "{voiceModel.name}" is ready for enhancement. Upload a recording to apply professional-grade
            processing tailored to your unique voice characteristics.
          </p>
        </div>

        {/* Enhancement Tabs */}
        <div className="mb-6 border-b border-white/10">
          <div className="flex">
            <EnhancementTabButton
              id="vocalsynth"
              label="VocalSynth™"
              isActive={activeEnhancementTab === 'vocalsynth'}
              onClick={() => setActiveEnhancementTab('vocalsynth')}
            />
            <EnhancementTabButton
              id="auphonic"
              label="Auphonic Pro"
              isActive={activeEnhancementTab === 'auphonic'}
              onClick={() => setActiveEnhancementTab('auphonic')}
            />
            <EnhancementTabButton
              id="voicemod"
              label="Voicemod"
              isActive={activeEnhancementTab === 'voicemod'}
              onClick={() => setActiveEnhancementTab('voicemod')}
            />
            <EnhancementTabButton
              id="imentiv"
              label="Emotion AI"
              isActive={activeEnhancementTab === 'imentiv'}
              onClick={() => setActiveEnhancementTab('imentiv')}
            />
            <EnhancementTabButton
              id="pipeline"
              label="Full Pipeline"
              isActive={activeEnhancementTab === 'pipeline'}
              onClick={() => setActiveEnhancementTab('pipeline')}
            />
          </div>
        </div>

        {/* VocalSynth Tab Content */}
        {activeEnhancementTab === 'vocalsynth' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column: Upload and Settings */}
            <div>
              <h3 className="text-lg font-medium mb-4 retune-text-glow-cyan">Upload Vocal Recording</h3>
              <div className="retune-glass-container p-5 mb-6">
                <input
                  type="file"
                  accept="audio/*"
                  onChange={handleFileChange}
                  className="hidden"
                  id="vocal-enhance-upload"
                />
                <label
                  htmlFor="vocal-enhance-upload"
                  className="flex flex-col items-center justify-center h-36 border-2 border-dashed border-[#00FFEE]/30 rounded-lg cursor-pointer hover:border-[#00FFEE]/60 transition-all duration-300"
                  style={{
                    transition: 'all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)'
                  }}
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[#00FFEE]/60 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.4))' }}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="mb-2 text-sm text-white/80">
                      <span className="font-medium retune-text-glow-cyan">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-white/60">
                      MP3, WAV, or AIFF (max. 10MB)
                    </p>
                  </div>
                </label>
                {audioFile && (
                  <div className="mt-4 p-3 bg-black/40 rounded-lg border border-[#00FFEE]/20">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-[#00FFEE] mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.4))' }}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                      </svg>
                      <div>
                        <p className="text-sm text-white retune-text-glow-cyan">{audioFile.name}</p>
                        <p className="text-xs text-white/60">{(audioFile.size / (1024 * 1024)).toFixed(2)} MB</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <h3 className="text-lg font-medium mb-4 retune-text-glow-cyan">Enhancement Settings</h3>
              <div className="retune-glass-container p-5">
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="text-white/80 text-sm">Enhancement Level</label>
                      <span className="text-[#00FFEE] text-sm font-medium retune-text-glow-cyan">{enhancementLevel}%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-white/60 text-xs mr-3">Subtle</span>
                      <div className="flex-1 relative">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={enhancementLevel}
                          onChange={(e) => setEnhancementLevel(parseInt(e.target.value))}
                          className="retune-slider w-full"
                        />
                      </div>
                      <span className="text-white/60 text-xs ml-3">Strong</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label className="text-white/80 text-sm block">Enhancement Options</label>
                    <div className="flex flex-wrap gap-2">
                      <EnhancementOption
                        label="Pitch Correction"
                        isActive={enhancementOptions.pitchCorrection}
                        onClick={() => toggleEnhancementOption('pitchCorrection')}
                      />
                      <EnhancementOption
                        label="Noise Reduction"
                        isActive={enhancementOptions.noiseReduction}
                        onClick={() => toggleEnhancementOption('noiseReduction')}
                      />
                      <EnhancementOption
                        label="Clarity Boost"
                        isActive={enhancementOptions.clarityBoost}
                        onClick={() => toggleEnhancementOption('clarityBoost')}
                      />
                      <EnhancementOption
                        label="Warmth"
                        isActive={enhancementOptions.warmth}
                        onClick={() => toggleEnhancementOption('warmth')}
                      />
                      <EnhancementOption
                        label="Presence"
                        isActive={enhancementOptions.presence}
                        onClick={() => toggleEnhancementOption('presence')}
                      />
                      <EnhancementOption
                        label="De-essing"
                        isActive={enhancementOptions.deEssing}
                        onClick={() => toggleEnhancementOption('deEssing')}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-white/80 text-sm block mb-2">Voice Type</label>
                    <select
                      className="w-full bg-black/40 border border-white/10 rounded-lg p-2.5 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300"
                      value={voiceType}
                      onChange={(e) => setVoiceType(e.target.value)}
                      style={{ backdropFilter: 'blur(10px)' }}
                    >
                      <option value="auto">Auto-detect</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                      <option value="child">Child</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-white/80 text-sm block mb-2">Genre Preset</label>
                    <select
                      className="w-full bg-black/40 border border-white/10 rounded-lg p-2.5 text-white focus:border-[#00FFEE]/60 focus:outline-none focus:ring-1 focus:ring-[#00FFEE]/30 transition-all duration-300"
                      value={selectedPreset}
                      onChange={handlePresetChange}
                      style={{ backdropFilter: 'blur(10px)' }}
                    >
                      <option value="none">No Preset</option>
                      <option value="pop">Pop</option>
                      <option value="rap">Rap / Hip-Hop</option>
                      <option value="rnb">R&B</option>
                      <option value="rock">Rock</option>
                      <option value="podcast">Podcast</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column: Preview & Process */}
            <div>
              <h3 className="text-lg font-medium mb-4 retune-text-glow-cyan">Preview & Process</h3>
              <div className="retune-glass-container p-5 h-full flex flex-col">
                {!audioFile ? (
                  <div className="flex flex-col items-center justify-center flex-1 py-12 text-white/50">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 mb-5 text-[#00FFEE]/30" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5} style={{ filter: 'drop-shadow(0 0 5px rgba(0, 255, 238, 0.2))' }}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                    <p className="text-white/70">Upload a vocal recording to begin</p>

                    {/* Decorative 3D waveform */}
                    <div className="mt-8 w-full h-12 flex items-center justify-center">
                      <div className="flex items-center justify-center space-x-1">
                        {[...Array(20)].map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-gradient-to-t from-[#00FFEE]/10 to-[#C600FF]/10 rounded-full"
                            style={{
                              height: `${Math.sin(i * 0.5) * 15 + 20}px`,
                              opacity: 0.3
                            }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : isProcessing ? (
                  <div className="flex flex-col items-center justify-center flex-1 py-12">
                    <div className="w-20 h-20 mb-5 relative">
                      <div className="absolute inset-0 rounded-full border-4 border-[#00FFEE]/20"></div>
                      <div className="absolute inset-0 rounded-full border-t-4 border-[#00FFEE] animate-spin" style={{ filter: 'drop-shadow(0 0 8px rgba(0, 255, 238, 0.7))' }}></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-[#00FFEE] text-sm font-mono retune-text-glow-cyan">{Math.floor(Math.random() * 100)}%</span>
                      </div>
                    </div>
                    <p className="text-white retune-text-glow-cyan mb-2">Enhancing vocals...</p>
                    <p className="text-white/60 text-sm">Applying {voiceModel.type === 'vocalsync' ? 'VocalSynth™' : 'FlowClone™'} processing</p>

                    {/* Neural network visualization */}
                    <div className="mt-6 w-full h-16 flex items-center justify-center overflow-hidden">
                      <div className="grid grid-cols-10 gap-1.5 relative">
                        {[...Array(40)].map((_, i) => (
                          <div
                            key={i}
                            className={`w-1.5 h-1.5 rounded-full ${Math.random() > 0.5
                              ? 'bg-[#00FFEE]'
                              : 'bg-[#C600FF]'
                              }`}
                            style={{
                              opacity: Math.random() * 0.8 + 0.2,
                              filter: `drop-shadow(0 0 3px ${Math.random() > 0.5 ? 'rgba(0, 255, 238, 0.7)' : 'rgba(198, 0, 255, 0.7)'})`
                            }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="h-full flex flex-col">
                    <div className="flex-1 space-y-6">
                      <div>
                        <h4 className="text-white/80 text-sm font-medium mb-3 retune-text-glow-cyan">Original Audio</h4>
                        <div className="h-24 bg-black/30 rounded-lg border border-white/10 flex items-center justify-center p-3 relative overflow-hidden">
                          {audioUrl ? (
                            <>
                              <audio controls className="w-full relative z-10">
                                <source src={audioUrl} type="audio/wav" />
                                Your browser does not support the audio element.
                              </audio>
                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                <div className="w-full h-12 flex items-center justify-center opacity-20">
                                  <div className="flex items-center justify-center space-x-1">
                                    {[...Array(40)].map((_, i) => (
                                      <div
                                        key={i}
                                        className="w-1 bg-white rounded-full"
                                        style={{
                                          height: `${Math.sin(i * 0.3) * 20 + 25}px`,
                                        }}
                                      ></div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </>
                          ) : (
                            <p className="text-white/50">Original waveform will appear here</p>
                          )}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-white/80 text-sm font-medium mb-3 retune-text-glow-cyan">Enhanced Audio</h4>
                        <div className="h-24 bg-black/30 rounded-lg border border-white/10 flex items-center justify-center p-3 relative overflow-hidden">
                          {enhancedAudioUrl ? (
                            <>
                              <audio controls className="w-full relative z-10">
                                <source src={enhancedAudioUrl} type="audio/wav" />
                                Your browser does not support the audio element.
                              </audio>
                              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                                <div className="w-full h-12 flex items-center justify-center opacity-20">
                                  <div className="flex items-center justify-center space-x-1">
                                    {[...Array(40)].map((_, i) => (
                                      <div
                                        key={i}
                                        className="w-1 bg-gradient-to-t from-[#00FFEE] to-[#C600FF] rounded-full"
                                        style={{
                                          height: `${Math.sin(i * 0.3) * 25 + 30}px`,
                                        }}
                                      ></div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </>
                          ) : (
                            <p className="text-white/50">Enhanced audio will appear after processing</p>
                          )}
                        </div>
                      </div>

                      {enhancedAudioUrl && (
                        <div className="bg-black/30 rounded-lg border border-white/10 p-4">
                          <div className="flex justify-between items-center">
                            <div className="text-white/80 text-sm retune-text-glow-cyan">Before/After Comparison</div>
                            <div className="flex space-x-3">
                              <button className="px-3 py-1.5 bg-black/40 text-white/80 text-xs rounded-md border border-white/10 hover:border-white/30 transition-all duration-300">
                                Play Original
                              </button>
                              <button className="px-3 py-1.5 bg-black/40 text-[#00FFEE] text-xs rounded-md border border-[#00FFEE]/30 hover:border-[#00FFEE]/60 hover:shadow-[0_0_10px_rgba(0,255,238,0.3)] transition-all duration-300">
                                Play Enhanced
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {error && (
                      <div className="mb-5 p-4 bg-black/30 border border-red-500/30 rounded-lg text-red-400 text-sm">
                        {error}
                      </div>
                    )}

                    <button
                      onClick={handleProcess}
                      disabled={!audioFile || isProcessing}
                      className="retune-button w-full py-3 px-4 mt-6 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isProcessing ? 'Processing...' : 'Enhance Voice'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Auphonic Tab Content */}
        {activeEnhancementTab === 'auphonic' && (
          <div>
            <AuphonicEnhancement onEnhancementComplete={handleAuphonicEnhancementComplete} />
          </div>
        )}

        {/* Voicemod Tab Content */}
        {activeEnhancementTab === 'voicemod' && (
          <div>
            <div className="mb-4">
              <p className="text-white/80 mb-4">
                Apply real-time voice effects and transformations to your recordings using Voicemod's powerful audio processing technology.
              </p>

              {audioFile ? (
                <VoicemodEffectsPanel
                  audioBlob={audioFile}
                  onProcessed={(processedAudio: Blob) => {
                    const url = URL.createObjectURL(processedAudio);
                    setEnhancedAudioUrl(url);
                    handleAuphonicEnhancementComplete(url);
                  }}
                />
              ) : (
                <div className="bg-black/30 border border-white/10 rounded-lg p-6 text-center">
                  <p className="text-white/70 mb-4">Please upload an audio file to apply Voicemod effects.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Imentiv Emotion AI Tab Content */}
        {activeEnhancementTab === 'imentiv' && (
          <div>
            <EmotionDetection
              onEmotionDetected={(emotions: any) => console.log('Emotions detected:', emotions)}
              onEmotionAdjusted={handleAuphonicEnhancementComplete}
            />
          </div>
        )}

        {/* Full Pipeline Tab Content */}
        {activeEnhancementTab === 'pipeline' && (
          <div>
            <RetunePipeline
              onProcessingComplete={handleAuphonicEnhancementComplete}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceEnhanceContent;
