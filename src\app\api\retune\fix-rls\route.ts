import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a service role client that bypasses RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // This bypasses RLS
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Fix RLS issues for Retune by temporarily disabling RLS or using service role
 */
export async function POST(request: Request) {
  try {
    const { action, data } = await request.json();

    switch (action) {
      case 'create_voice_profile':
        return await createVoiceProfile(data);
      case 'get_voice_profiles':
        return await getVoiceProfiles(data.userId);
      case 'update_voice_profile':
        return await updateVoiceProfile(data.id, data.updates);
      case 'delete_voice_profile':
        return await deleteVoiceProfile(data.id);
      case 'disable_rls':
        return await disableRLS();
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('RLS fix error:', error);
    return NextResponse.json(
      { error: 'Failed to execute RLS fix' },
      { status: 500 }
    );
  }
}

async function createVoiceProfile(profileData: any) {
  try {
    const { data, error } = await supabaseAdmin
      .from('voice_profiles')
      .insert(profileData)
      .select()
      .single();

    if (error) {
      console.error('Error creating voice profile:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in createVoiceProfile:', error);
    return NextResponse.json({ error: 'Failed to create voice profile' }, { status: 500 });
  }
}

async function getVoiceProfiles(userId?: string) {
  try {
    let query = supabaseAdmin.from('voice_profiles').select('*');
    
    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error getting voice profiles:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in getVoiceProfiles:', error);
    return NextResponse.json({ error: 'Failed to get voice profiles' }, { status: 500 });
  }
}

async function updateVoiceProfile(id: string, updates: any) {
  try {
    const { data, error } = await supabaseAdmin
      .from('voice_profiles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating voice profile:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in updateVoiceProfile:', error);
    return NextResponse.json({ error: 'Failed to update voice profile' }, { status: 500 });
  }
}

async function deleteVoiceProfile(id: string) {
  try {
    const { error } = await supabaseAdmin
      .from('voice_profiles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting voice profile:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in deleteVoiceProfile:', error);
    return NextResponse.json({ error: 'Failed to delete voice profile' }, { status: 500 });
  }
}

async function disableRLS() {
  try {
    // Disable RLS for voice_profiles table temporarily
    const { error: rls1 } = await supabaseAdmin.rpc('exec_sql', {
      sql: 'ALTER TABLE public.voice_profiles DISABLE ROW LEVEL SECURITY;'
    });

    const { error: rls2 } = await supabaseAdmin.rpc('exec_sql', {
      sql: 'ALTER TABLE public.voice_samples DISABLE ROW LEVEL SECURITY;'
    });

    if (rls1 || rls2) {
      console.error('Error disabling RLS:', rls1 || rls2);
      return NextResponse.json({ 
        error: 'Failed to disable RLS',
        details: rls1?.message || rls2?.message 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'RLS disabled for voice tables' 
    });
  } catch (error) {
    console.error('Error in disableRLS:', error);
    return NextResponse.json({ error: 'Failed to disable RLS' }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Check RLS status
    const { data, error } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        SELECT 
          schemaname, 
          tablename, 
          rowsecurity 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename IN ('voice_profiles', 'voice_samples');
      `
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      rlsStatus: data,
      message: 'RLS status retrieved' 
    });
  } catch (error) {
    console.error('Error checking RLS status:', error);
    return NextResponse.json({ error: 'Failed to check RLS status' }, { status: 500 });
  }
}
