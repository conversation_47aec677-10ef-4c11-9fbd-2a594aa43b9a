import { NextRequest, NextResponse } from 'next/server';
import apiService from '@/services/api';

/**
 * API route for collaborative voice mixing
 * 
 * This route handles the generation of collaborative audio using multiple voice profiles.
 * 
 * @param req - The request object
 * @returns A response with the generated collaborative audio
 */
export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body = await req.json();
    
    // Extract request data
    const { text, primaryProfileId, collaboratorProfileIds, style } = body;
    
    // Validate required fields
    if (!text || !primaryProfileId || !collaboratorProfileIds || !Array.isArray(collaboratorProfileIds)) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Get the primary voice profile
    const primaryProfile = await apiService.supabaseService.getVoiceProfile(primaryProfileId);
    
    // Generate the primary speech
    const primaryAudio = await apiService.textToSpeech({
      text,
      voice_id: primaryProfile.provider_voice_id,
      provider: primaryProfile.provider,
      options: { style },
    });
    
    // If no collaborators, return the primary audio
    if (collaboratorProfileIds.length === 0) {
      if (typeof primaryAudio === 'string') {
        return NextResponse.json({ audioUrl: primaryAudio });
      }
      
      const audioBlob = new Blob([primaryAudio], { type: 'audio/mpeg' });
      const response = new NextResponse(audioBlob);
      response.headers.set('Content-Type', 'audio/mpeg');
      return response;
    }
    
    // For a real implementation, we would mix the audio from multiple voices
    // This is a simplified implementation that just returns the primary audio
    
    // Convert ArrayBuffer to Blob if needed
    if (typeof primaryAudio !== 'string') {
      const audioBlob = new Blob([primaryAudio], { type: 'audio/mpeg' });
      const response = new NextResponse(audioBlob);
      response.headers.set('Content-Type', 'audio/mpeg');
      return response;
    }
    
    // Return the URL if it's a string
    return NextResponse.json({ audioUrl: primaryAudio });
  } catch (error) {
    console.error('Error generating collaborative audio:', error);
    return NextResponse.json(
      { error: 'Failed to generate collaborative audio' },
      { status: 500 }
    );
  }
}
