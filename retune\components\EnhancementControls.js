import React, { useState } from 'react';

const EnhancementControls = ({ onApplyEnhancement, isProcessing }) => {
  const [enhancementParams, setEnhancementParams] = useState({
    clarity: 50,
    resonance: 50,
    confidence: 50,
    stability: 50,
    warmth: 50
  });

  const [naturalElements, setNaturalElements] = useState({
    add_hesitations: true,
    add_breaths: true,
    add_mouth_sounds: true,
    natural_pauses: true
  });

  const [voiceDynamics, setVoiceDynamics] = useState({
    fill_dynamics: true,
    enhance_monotone: false,
    dynamic_range: 70,
    noise_reduction: false,
    compression: false
  });

  const [artistCreation, setArtistCreation] = useState({
    create_artist: false,
    artist_genre: 'pop'
  });

  const [presetMode, setPresetMode] = useState('custom'); // 'custom', 'natural', 'professional', 'charismatic'

  // Handle slider changes
  const handleSliderChange = (param, value) => {
    setEnhancementParams(prev => ({
      ...prev,
      [param]: value
    }));

    // If user manually changes a slider, switch to custom mode
    setPresetMode('custom');
  };

  // Apply preset configurations
  const applyPreset = (preset) => {
    setPresetMode(preset);

    switch (preset) {
      case 'natural':
        setEnhancementParams({
          clarity: 60,
          resonance: 40,
          confidence: 50,
          stability: 70,
          warmth: 60
        });
        break;

      case 'professional':
        setEnhancementParams({
          clarity: 80,
          resonance: 60,
          confidence: 70,
          stability: 80,
          warmth: 40
        });
        break;

      case 'charismatic':
        setEnhancementParams({
          clarity: 70,
          resonance: 80,
          confidence: 90,
          stability: 60,
          warmth: 70
        });
        break;

      default:
        // Keep current values for custom
        break;
    }
  };

  // Handle apply button click
  const handleApply = () => {
    // Convert slider values (0-100) to parameter values (0-1)
    const normalizedParams = Object.entries(enhancementParams).reduce((acc, [key, value]) => {
      acc[key] = value / 100;
      return acc;
    }, {});

    // Add preset information
    normalizedParams.preset = presetMode;

    onApplyEnhancement(normalizedParams);
  };

  return (
    <div className="enhancement-controls">
      <h3>Voice Enhancement Settings</h3>

      <div className="preset-buttons">
        <button
          className={`preset-button ${presetMode === 'natural' ? 'active' : ''}`}
          onClick={() => applyPreset('natural')}
        >
          Natural
        </button>
        <button
          className={`preset-button ${presetMode === 'professional' ? 'active' : ''}`}
          onClick={() => applyPreset('professional')}
        >
          Professional
        </button>
        <button
          className={`preset-button ${presetMode === 'charismatic' ? 'active' : ''}`}
          onClick={() => applyPreset('charismatic')}
        >
          Charismatic
        </button>
        <button
          className={`preset-button ${presetMode === 'custom' ? 'active' : ''}`}
          onClick={() => applyPreset('custom')}
        >
          Custom
        </button>
      </div>

      <div className="enhancement-sliders">
        <div className="slider-control">
          <label htmlFor="clarity-slider">
            Clarity
            <span className="slider-value">{enhancementParams.clarity}%</span>
          </label>
          <input
            id="clarity-slider"
            type="range"
            min="0"
            max="100"
            value={enhancementParams.clarity}
            onChange={(e) => handleSliderChange('clarity', parseInt(e.target.value))}
          />
          <div className="slider-description">
            Improves the articulation and intelligibility of your speech
          </div>
        </div>

        <div className="slider-control">
          <label htmlFor="resonance-slider">
            Resonance
            <span className="slider-value">{enhancementParams.resonance}%</span>
          </label>
          <input
            id="resonance-slider"
            type="range"
            min="0"
            max="100"
            value={enhancementParams.resonance}
            onChange={(e) => handleSliderChange('resonance', parseInt(e.target.value))}
          />
          <div className="slider-description">
            Enhances the richness and fullness of your voice
          </div>
        </div>

        <div className="slider-control">
          <label htmlFor="confidence-slider">
            Confidence
            <span className="slider-value">{enhancementParams.confidence}%</span>
          </label>
          <input
            id="confidence-slider"
            type="range"
            min="0"
            max="100"
            value={enhancementParams.confidence}
            onChange={(e) => handleSliderChange('confidence', parseInt(e.target.value))}
          />
          <div className="slider-description">
            Boosts the authoritative and assured qualities of your voice
          </div>
        </div>

        <div className="slider-control">
          <label htmlFor="stability-slider">
            Stability
            <span className="slider-value">{enhancementParams.stability}%</span>
          </label>
          <input
            id="stability-slider"
            type="range"
            min="0"
            max="100"
            value={enhancementParams.stability}
            onChange={(e) => handleSliderChange('stability', parseInt(e.target.value))}
          />
          <div className="slider-description">
            Reduces wavering and improves consistency in your vocal delivery
          </div>
        </div>

        <div className="slider-control">
          <label htmlFor="warmth-slider">
            Warmth
            <span className="slider-value">{enhancementParams.warmth}%</span>
          </label>
          <input
            id="warmth-slider"
            type="range"
            min="0"
            max="100"
            value={enhancementParams.warmth}
            onChange={(e) => handleSliderChange('warmth', parseInt(e.target.value))}
          />
          <div className="slider-description">
            Adds a pleasant, inviting quality to your voice
          </div>
        </div>
      </div>

      <div className="enhancement-actions">
        <button
          className="apply-button"
          onClick={handleApply}
          disabled={isProcessing}
        >
          {isProcessing ? 'Processing...' : 'Apply Enhancement'}
        </button>
      </div>

      <div className="enhancement-note">
        <p>
          <strong>Note:</strong> Our AI enhancement preserves your natural voice while improving its best qualities.
          The result will still sound like you, just your best vocal self.
        </p>
      </div>
    </div>
  );
};

export default EnhancementControls;
