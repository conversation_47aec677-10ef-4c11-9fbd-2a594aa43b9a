import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    // Get the path to the Python script
    const scriptDir = path.join(process.cwd(), 'python', 'retune');
    const scriptPath = path.join(scriptDir, 'voice_morphing.py');
    
    // Execute the command to list models
    const pythonCommand = `python "${scriptPath}" list`;
    const { stdout, stderr } = await execAsync(pythonCommand);
    
    if (stderr) {
      console.error('Python script error:', stderr);
    }
    
    // Parse the output to extract model information
    const models = parseModelList(stdout);
    
    // Return the model list
    return NextResponse.json({
      success: true,
      models
    });
  } catch (error) {
    console.error('Error listing voice models:', error);
    return NextResponse.json(
      { error: 'Failed to list voice models' },
      { status: 500 }
    );
  }
}

/**
 * Parse the output of the list command to extract model information
 */
function parseModelList(output: string): any[] {
  const models = [];
  const lines = output.trim().split('\n');
  
  // Skip the first line (header)
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    // Parse the model information
    // Format: "  model_name: type (engine)"
    const match = line.match(/^\s+(.+?):\s+(.+?)\s+\((.+?)\)$/);
    if (match) {
      const [_, name, type, engine] = match;
      
      models.push({
        name,
        type,
        engine,
        // Add additional properties based on name patterns
        isMorphed: name.includes('_'),
        isAged: name.includes('_younger_') || name.includes('_older_'),
        isAccented: name.includes('_british_') || name.includes('_american_') || 
                   name.includes('_australian_') || name.includes('_indian_') ||
                   name.includes('_spanish_') || name.includes('_french_') ||
                   name.includes('_german_')
      });
    }
  }
  
  return models;
}
