# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Service API Keys
GROQ_API_KEY=your_groq_api_key
GEMINI_API_KEY=your_gemini_api_key
SUNO_API_KEY=your_suno_api_key
STABILITY_API_KEY=your_stability_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
LMNT_API_KEY=your_lmnt_api_key

# Optional AI Services
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Database (if using alternative to Supabase)
DATABASE_URL=your_database_url

# Vercel (for deployment)
VERCEL_URL=your_vercel_deployment_url

# Analytics
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_analytics_id

# Feature Flags
NEXT_PUBLIC_DEMO_MODE=false
NEXT_PUBLIC_ENABLE_CREDITS=true
NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS=true

# Development
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
