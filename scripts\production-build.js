/**
 * Production Build Script for A Place In Time
 * 
 * This script prepares the application for production deployment by:
 * 1. Using a simplified Babel configuration
 * 2. Cleaning the build directory
 * 3. Running the Next.js build
 * 4. Running post-build optimizations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

/**
 * Log a section header to the console
 */
function logSection(title) {
  console.log('\n' + colors.bright + colors.cyan + '▓▒░ ' + title + ' ░▒▓' + colors.reset + '\n');
}

/**
 * Log a success message to the console
 */
function logSuccess(message) {
  console.log(colors.green + '✓ ' + colors.reset + message);
}

/**
 * Log a warning message to the console
 */
function logWarning(message) {
  console.log(colors.yellow + '⚠ ' + colors.reset + message);
}

/**
 * Log an error message to the console
 */
function logError(message) {
  console.log(colors.red + '✗ ' + colors.reset + message);
}

/**
 * Log an info message to the console
 */
function logInfo(message) {
  console.log(colors.blue + 'ℹ ' + colors.reset + message);
}

/**
 * Execute a shell command and log the output
 */
function executeCommand(command, errorMessage) {
  try {
    logInfo(`Executing: ${command}`);
    const output = execSync(command, { encoding: 'utf8' });
    return output;
  } catch (error) {
    logError(errorMessage || `Command failed: ${command}`);
    logError(error.message);
    throw error;
  }
}

/**
 * Backup the current .babelrc file
 */
function backupBabelrc() {
  logSection('Backing up .babelrc');

  const babelrcPath = path.join(__dirname, '..', '.babelrc');
  const backupPath = path.join(__dirname, '..', '.babelrc.backup');

  if (fs.existsSync(babelrcPath)) {
    fs.copyFileSync(babelrcPath, backupPath);
    logSuccess('Backed up .babelrc to .babelrc.backup');
  } else {
    logWarning('.babelrc not found, skipping backup');
  }
}

/**
 * Use the production .babelrc file
 */
function useProductionBabelrc() {
  logSection('Using production Babel configuration');

  const babelrcPath = path.join(__dirname, '..', '.babelrc');
  const productionPath = path.join(__dirname, '..', '.babelrc.production');

  if (fs.existsSync(productionPath)) {
    fs.copyFileSync(productionPath, babelrcPath);
    logSuccess('Copied .babelrc.production to .babelrc');
  } else {
    logError('.babelrc.production not found');
    throw new Error('.babelrc.production not found');
  }
}

/**
 * Restore the original .babelrc file
 */
function restoreBabelrc() {
  logSection('Restoring original Babel configuration');

  const babelrcPath = path.join(__dirname, '..', '.babelrc');
  const backupPath = path.join(__dirname, '..', '.babelrc.backup');

  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, babelrcPath);
    fs.unlinkSync(backupPath);
    logSuccess('Restored original .babelrc and removed backup');
  } else {
    logWarning('.babelrc.backup not found, skipping restore');
  }
}

/**
 * Clean the build directory
 */
function cleanBuildDirectory() {
  logSection('Cleaning build directory');

  const nextDir = path.join(__dirname, '..', '.next');

  if (fs.existsSync(nextDir)) {
    try {
      // On Windows, we need to use rimraf or a similar approach
      // For simplicity, we'll use the recursive option of fs.rmSync if available
      if (fs.rmSync) {
        fs.rmSync(nextDir, { recursive: true, force: true });
      } else {
        // Fallback for older Node.js versions
        executeCommand(`rimraf "${nextDir}"`, 'Failed to clean .next directory');
      }
      logSuccess('Cleaned .next directory');
    } catch (error) {
      logWarning(`Could not clean .next directory: ${error.message}`);
      logInfo('Continuing with build process...');
    }
  } else {
    logInfo('.next directory does not exist, skipping clean');
  }
}

/**
 * Run the Next.js build
 */
function runNextBuild() {
  logSection('Running Next.js build');

  try {
    // Use less strict build for deployment
    executeCommand('next build --no-lint', 'Next.js build failed');
    logSuccess('Next.js build completed successfully');
  } catch (error) {
    throw error;
  }
}

/**
 * Run the post-build script
 */
function runPostBuild() {
  logSection('Running post-build optimizations');

  try {
    require('./post-build');
    logSuccess('Post-build optimizations completed successfully');
  } catch (error) {
    logError(`Post-build optimizations failed: ${error.message}`);
    throw error;
  }
}

/**
 * Main function
 */
function main() {
  logSection('Starting Production Build Process');

  try {
    // Backup the current .babelrc
    backupBabelrc();

    // Use the production .babelrc
    useProductionBabelrc();

    // Clean the build directory
    cleanBuildDirectory();

    // Run the Next.js build
    runNextBuild();

    // Run the post-build script
    runPostBuild();

    logSection('Production Build Completed Successfully');
  } catch (error) {
    logError(`Production build failed: ${error.message}`);
    process.exitCode = 1;
  } finally {
    // Always restore the original .babelrc
    restoreBabelrc();
  }
}

// Run the main function
main();
