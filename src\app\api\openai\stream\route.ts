import { NextResponse } from 'next/server';
import { openai } from 'ai';
import OpenAI from 'openai';

// This API route uses the Vercel AI SDK to stream the response from OpenAI
export async function POST(req: Request) {
  try {
    // Get the request body
    const {
      prompt,
      model = 'gpt-4-turbo',
      temperature = 0.7,
      maxTokens = 1000
    } = await req.json();

    // Validate the request
    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    // Get the API key from environment variables
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    // Initialize the OpenAI client
    const openai = new OpenAI({
      apiKey,
    });

    // Create the stream from OpenAI
    const response = await openai.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature,
      max_tokens: maxTokens,
      stream: true,
    });

    // Convert the response to a stream using the new ai package API
    const result = await openai.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature,
      max_tokens: maxTokens,
    });

    // Return the response
    return NextResponse.json({
      content: result.choices[0].message.content
    });
  } catch (error) {
    console.error('Error in OpenAI streaming API:', error);
    return NextResponse.json(
      { error: 'Failed to generate text' },
      { status: 500 }
    );
  }
}
