{"extends": "next/core-web-vitals", "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-require-imports": "off", "react/no-unescaped-entities": "off", "react-hooks/rules-of-hooks": "off", "react-hooks/exhaustive-deps": "off", "prefer-const": "off", "@next/next/no-img-element": "off", "@next/next/no-assign-module-variable": "off", "@next/next/no-html-link-for-pages": "off"}}