import { NextResponse } from 'next/server';
import path from 'path';
import fs from 'fs';

// Import utilities
import { executeInRepository, validateRepository } from '@/utils/repositoryManager';
import { createGenerationDirectory, saveFile, saveJsonFile, getPublicUrl } from '@/utils/fileManager';

export async function POST(request: Request) {
  try {
    // Get the form data from the request
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const voiceName = formData.get('voiceName') as string || 'custom_voice';
    const userId = formData.get('userId') as string || 'demo-user';

    if (!audioFile) {
      return NextResponse.json({ error: 'No audio file provided' }, { status: 400 });
    }

    if (!voiceName) {
      return NextResponse.json({ error: 'Voice name is required' }, { status: 400 });
    }

    console.log(`Voice cloning request for: ${voiceName}`);

    // Create a directory for this voice cloning session
    const { id: cloningId, path: outputDir } = createGenerationDirectory();

    // Save the audio file using the file manager
    const audioPath = await saveFile(audioFile, outputDir, `reference.wav`);
    const modelOutputPath = path.join(outputDir, `${voiceName.replace(/\s+/g, '_').toLowerCase()}_model`);
    const sampleOutputPath = path.join(outputDir, `sample.wav`);

    // Create initial status file using the file manager
    saveJsonFile({
      id: cloningId,
      status: 'processing',
      progress: 0,
      message: 'Starting voice cloning process...',
      timestamp: Date.now(),
      userId: userId,
      request: {
        voiceName,
        audioFile: audioFile.name
      }
    }, outputDir, 'status.json');

    // Validate RVC repository
    const rvcStatus = validateRepository('RVC');
    
    // Process the voice cloning in the background
    (async () => {
      try {
        if (!rvcStatus.valid) {
          throw new Error(`RVC repository issue: ${rvcStatus.error}`);
        }

        // Update status to extracting features
        saveJsonFile({
          id: cloningId,
          status: 'processing',
          progress: 10,
          message: 'Extracting voice features...',
          timestamp: Date.now(),
          userId: userId
        }, outputDir, 'status.json');

        // Step 1: Extract voice features
        const extractCommand = `python -m rvc.extract_features \
          --input "${audioPath}" \
          --output_dir "${outputDir}" \
          --sample_rate 44100`;

        const extractResult = await executeInRepository('RVC', extractCommand);
        
        if (!extractResult.success) {
          throw new Error(`Feature extraction failed: ${extractResult.stderr}`);
        }

        // Update status to training model
        saveJsonFile({
          id: cloningId,
          status: 'processing',
          progress: 30,
          message: 'Training voice model...',
          timestamp: Date.now(),
          userId: userId
        }, outputDir, 'status.json');

        // Step 2: Train the voice model
        const trainCommand = `python -m rvc.train \
          --input_dir "${outputDir}" \
          --output_dir "${modelOutputPath}" \
          --epochs 200 \
          --batch_size 8 \
          --save_every 20`;

        const trainResult = await executeInRepository('RVC', trainCommand);
        
        if (!trainResult.success) {
          throw new Error(`Model training failed: ${trainResult.stderr}`);
        }

        // Update status to generating sample
        saveJsonFile({
          id: cloningId,
          status: 'processing',
          progress: 80,
          message: 'Generating voice sample...',
          timestamp: Date.now(),
          userId: userId
        }, outputDir, 'status.json');

        // Step 3: Generate a sample using the trained model
        const sampleText = "This is a sample of my cloned voice. How does it sound?";
        const sampleCommand = `python -m rvc.generate \
          --text "${sampleText}" \
          --model "${modelOutputPath}" \
          --output "${sampleOutputPath}"`;

        const sampleResult = await executeInRepository('RVC', sampleCommand);
        
        if (!sampleResult.success) {
          throw new Error(`Sample generation failed: ${sampleResult.stderr}`);
        }

        // Update status to completed
        const modelUrl = `/generations/${cloningId}/${path.basename(modelOutputPath)}`;
        const sampleUrl = `/generations/${cloningId}/sample.wav`;
        
        saveJsonFile({
          id: cloningId,
          status: 'completed',
          progress: 100,
          message: 'Voice cloning completed',
          timestamp: Date.now(),
          userId: userId,
          voiceName: voiceName,
          modelUrl: modelUrl,
          sampleUrl: sampleUrl,
          metadata: {
            voiceName,
            createdAt: new Date().toISOString(),
            audioFile: audioFile.name
          }
        }, outputDir, 'status.json');

      } catch (error) {
        console.error('Error in voice cloning process:', error);

        // Update status file with error
        saveJsonFile({
          id: cloningId,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
          userId: userId
        }, outputDir, 'status.json');
      }
    })();

    // Return immediate response with cloning ID
    return NextResponse.json({
      id: cloningId,
      status: 'processing',
      message: 'Voice cloning process started'
    });

  } catch (error) {
    console.error('Error starting voice cloning:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to start voice cloning' },
      { status: 500 }
    );
  }
}

// Status check endpoint
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'No ID provided' }, { status: 400 });
    }

    const statusPath = path.join(process.cwd(), 'public', 'generations', id, 'status.json');
    
    if (!fs.existsSync(statusPath)) {
      return NextResponse.json({ error: 'Voice cloning session not found' }, { status: 404 });
    }

    const statusData = JSON.parse(fs.readFileSync(statusPath, 'utf-8'));
    return NextResponse.json(statusData);

  } catch (error) {
    console.error('Error checking voice cloning status:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to check voice cloning status' },
      { status: 500 }
    );
  }
}
