/**
 * Storage utility functions for Retune
 * Handles saving and retrieving audio recordings
 */

// Save audio blob to local storage (temporary solution)
const saveRecording = async (blob, fileName) => {
  try {
    // In a real app, you'd upload to a server
    // For now, we'll use localStorage as a demo
    const reader = new FileReader();
    
    return new Promise((resolve, reject) => {
      reader.onload = () => {
        try {
          const recordings = JSON.parse(localStorage.getItem('recordings') || '{}');
          recordings[fileName] = {
            data: reader.result,
            timestamp: new Date().toISOString(),
            type: blob.type
          };
          localStorage.setItem('recordings', JSON.stringify(recordings));
          resolve({ success: true, fileName });
        } catch (error) {
          reject({ success: false, error });
        }
      };
      
      reader.onerror = () => {
        reject({ success: false, error: reader.error });
      };
      
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error saving recording:', error);
    return { success: false, error };
  }
};

// Get list of saved recordings
const getRecordings = () => {
  try {
    const recordings = JSON.parse(localStorage.getItem('recordings') || '{}');
    return Object.keys(recordings).map(key => ({
      fileName: key,
      timestamp: recordings[key].timestamp,
      type: recordings[key].type
    }));
  } catch (error) {
    console.error('Error getting recordings:', error);
    return [];
  }
};

// Load a specific recording
const loadRecording = (fileName) => {
  try {
    const recordings = JSON.parse(localStorage.getItem('recordings') || '{}');
    const recording = recordings[fileName];
    
    if (!recording) {
      return { success: false, error: 'Recording not found' };
    }
    
    // Convert data URL back to blob
    const dataUrl = recording.data;
    const byteString = atob(dataUrl.split(',')[1]);
    const mimeType = dataUrl.split(',')[0].split(':')[1].split(';')[0];
    
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    
    const blob = new Blob([ab], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    return { 
      success: true, 
      recording: {
        fileName,
        url,
        blob,
        timestamp: recording.timestamp
      }
    };
  } catch (error) {
    console.error('Error loading recording:', error);
    return { success: false, error };
  }
};

// Delete a recording
const deleteRecording = (fileName) => {
  try {
    const recordings = JSON.parse(localStorage.getItem('recordings') || '{}');
    
    if (!recordings[fileName]) {
      return { success: false, error: 'Recording not found' };
    }
    
    delete recordings[fileName];
    localStorage.setItem('recordings', JSON.stringify(recordings));
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting recording:', error);
    return { success: false, error };
  }
};

// Export recording as a file
const exportRecording = (fileName, blob) => {
  try {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);
    
    return { success: true };
  } catch (error) {
    console.error('Error exporting recording:', error);
    return { success: false, error };
  }
};

export {
  saveRecording,
  getRecordings,
  loadRecording,
  deleteRecording,
  exportRecording
};
