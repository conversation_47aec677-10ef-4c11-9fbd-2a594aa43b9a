'use client';

/**
 * Utility functions for processing audio with voice cloning models
 */

/**
 * Process audio with VocalSynth™ technology for singing voice synthesis
 */
export async function processWithVocalSynth(
  audioBlob: Blob,
  modelId: string,
  options: {
    pitchShift?: number;
    formantShift?: number;
    breathiness?: number;
  } = {}
): Promise<Blob> {
  // In a real implementation, this would send the audio to the backend for processing
  // For now, we'll simulate the processing

  // Create form data
  const formData = new FormData();
  formData.append('audio', audioBlob);
  formData.append('modelId', modelId);
  formData.append('pitchShift', String(options.pitchShift || 0));
  formData.append('formantShift', String(options.formantShift || 0));
  formData.append('breathiness', String(options.breathiness || 0));

  // Send to backend
  const response = await fetch('/api/retune/process-vocalsync', {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error('Failed to process audio with VocalSynth™ technology');
  }

  // Get processed audio blob
  return await response.blob();
}

/**
 * Process audio with FlowClone™ technology for rap/spoken word
 */
export async function processWithFlowClone(
  audioBlob: Blob,
  modelId: string,
  options: {
    pitchShift?: number;
    indexRatio?: number;
    protectVoicelessConsontants?: boolean;
  } = {}
): Promise<Blob> {
  // In a real implementation, this would send the audio to the backend for processing
  // For now, we'll simulate the processing

  // Create form data
  const formData = new FormData();
  formData.append('audio', audioBlob);
  formData.append('modelId', modelId);
  formData.append('pitchShift', String(options.pitchShift || 0));
  formData.append('indexRatio', String(options.indexRatio || 0.5));
  formData.append('protectVoicelessConsontants', String(options.protectVoicelessConsontants || true));

  // Send to backend
  const response = await fetch('/api/retune/process-flowclone', {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error('Failed to process audio with FlowClone™ technology');
  }

  // Get processed audio blob
  return await response.blob();
}

/**
 * Determine which model to use based on audio content
 */
export function selectCloneModel(audioAnalysis: { isPredominantlySinging: boolean }): 'vocalsync' | 'flowclone' {
  return audioAnalysis.isPredominantlySinging ? 'vocalsync' : 'flowclone';
}
