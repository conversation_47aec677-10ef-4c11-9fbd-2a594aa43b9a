'use client';

import { useEffect, useRef, useState } from 'react';

// Define voice model type
interface VoiceModel {
  id: string;
  name: string;
  type: 'vocalsync' | 'flowclone';
  qualityScore: number;
  createdAt: string;
}

interface VoiceEnhancerProps {
  voiceModel: VoiceModel;
  voiceModelScore: number | null;
  onAudioEnhanced?: (enhancedUrl: string, originalUrl: string) => void;
}

export default function VoiceEnhancer({ voiceModel, voiceModelScore, onAudioEnhanced }: VoiceEnhancerProps) {
  // State for recording
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  // State for enhancement
  const [isProcessing, setIsProcessing] = useState(false);
  const [enhancedAudioUrl, setEnhancedAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Enhancement settings
  const [clarity, setClarity] = useState(50);
  const [resonance, setResonance] = useState(50);
  const [confidence, setConfidence] = useState(70);
  const [warmth, setWarmth] = useState(50);
  const [stability, setStability] = useState(60);

  // Sesame AI specific settings
  const [harmonics, setHarmonics] = useState(50);
  const [naturalness, setNaturalness] = useState(60);
  const [expressiveness, setExpressiveness] = useState(40);

  // Selected preset
  const [selectedPreset, setSelectedPreset] = useState<'podcast' | 'music' | 'speech' | 'custom'>('custom');

  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Apply preset
  const applyPreset = (preset: 'podcast' | 'music' | 'speech' | 'custom') => {
    setSelectedPreset(preset);

    switch (preset) {
      case 'podcast':
        setClarity(70);
        setResonance(60);
        setConfidence(80);
        setWarmth(65);
        setStability(75);
        setHarmonics(30);
        setNaturalness(80);
        setExpressiveness(50);
        break;
      case 'music':
        setClarity(65);
        setResonance(75);
        setConfidence(60);
        setWarmth(70);
        setStability(65);
        setHarmonics(70);
        setNaturalness(60);
        setExpressiveness(75);
        break;
      case 'speech':
        setClarity(80);
        setResonance(50);
        setConfidence(85);
        setWarmth(45);
        setStability(80);
        setHarmonics(20);
        setNaturalness(90);
        setExpressiveness(40);
        break;
      default:
        // Keep current values for custom
        break;
    }
  };

  // Start recording function
  const startRecording = async () => {
    audioChunksRef.current = [];

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);

        setAudioBlob(audioBlob);
        setAudioUrl(audioUrl);
        setEnhancedAudioUrl(null); // Reset enhanced audio when new recording is made
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('Error accessing your microphone. Please check permissions and try again.');
    }
  };

  // Stop recording function
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);

      // Stop all audio tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  };

  // Enhance audio function
  const enhanceAudio = async () => {
    if (!audioBlob || !voiceModel) return;

    setIsProcessing(true);

    try {
      // Create a FormData object to send the audio file
      const formData = new FormData();
      formData.append('audio', audioBlob);
      formData.append('voiceModelId', voiceModel.id);
      formData.append('voiceModelPath', voiceModel.path || '');
      formData.append('voiceModelType', voiceModel.type || 'rvc');

      // Enhancement parameters
      formData.append('clarity', clarity.toString());
      formData.append('resonance', resonance.toString());
      formData.append('confidence', confidence.toString());
      formData.append('warmth', warmth.toString());
      formData.append('stability', stability.toString());
      formData.append('preset', selectedPreset);

      // Sesame AI specific parameters
      formData.append('harmonics', harmonics.toString());
      formData.append('naturalness', naturalness.toString());
      formData.append('expressiveness', expressiveness.toString());

      // Send to backend for processing with our local repositories
      const response = await fetch('/api/retune/enhance', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance audio');
      }

      const data = await response.json();

      setEnhancedAudioUrl(data.enhancedAudioUrl);
      setIsProcessing(false);

      // Call the callback function with the enhanced audio URL
      onAudioEnhanced(data.enhancedAudioUrl, audioUrl as string);
    } catch (error) {
      console.error('Error enhancing audio:', error);
      setError(`Error enhancing your audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsProcessing(false);
    }
  };

  // Reset function
  const resetRecording = () => {
    if (audioUrl) URL.revokeObjectURL(audioUrl);
    if (enhancedAudioUrl && enhancedAudioUrl !== audioUrl) URL.revokeObjectURL(enhancedAudioUrl);

    setAudioBlob(null);
    setAudioUrl(null);
    setEnhancedAudioUrl(null);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) URL.revokeObjectURL(audioUrl);
      if (enhancedAudioUrl && enhancedAudioUrl !== audioUrl) URL.revokeObjectURL(enhancedAudioUrl);
    };
  }, [audioUrl, enhancedAudioUrl]);

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
      <h2 className="text-2xl font-semibold mb-4">VocalSynth™ Voice Enhancement</h2>
      <p className="text-gray-300 mb-6">
        Enhance your {voiceModel.type === 'vocalsync' ? 'singing voice' : 'spoken voice'} with professional-grade processing.
        Your voice model has a quality score of {voiceModelScore}%.
      </p>

      <div className="bg-purple-900/20 border border-purple-800/30 rounded-lg p-3 mb-6">
        <h3 className="text-sm font-medium text-purple-400 mb-1">Studio-Grade DSP Processing Chain</h3>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">High-Pass Filter</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Parametric EQ</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Compressor</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">De-esser</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Pitch Correction</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Saturation</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Spatial FX</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Chorus/Doubler</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">Limiter</span>
        </div>
        <p className="text-xs text-gray-400 mb-2">
          Our Retune process uses Vinn AI's proprietary voice technology to add natural human elements to your voice,
          combined with a professional-grade DSP chain targeting -14 LUFS integrated with -1 dBTP ceiling for broadcast-ready audio.
        </p>
        <div className="text-xs text-gray-500 mt-1">
          <span className="text-purple-400">Integration:</span> APIT Studio Suite™, SonicCore™, VocalFX™, PitchPerfect™, HarmonicEngine™
        </div>

        <div className="mt-2 border-t border-gray-700 pt-2">
          <div className="text-xs text-purple-400 font-medium mb-1">Genre-Specific Processing Settings:</div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs">
            <div className="bg-blue-900/20 p-2 rounded border border-blue-800/30">
              <div className="font-medium text-blue-400 mb-1">Pop</div>
              <ul className="text-gray-400 list-disc pl-4 space-y-1">
                <li>EQ: +3dB@2kHz, +4dB@12kHz</li>
                <li>Comp: -18dB, 3:1, 5ms, 100ms</li>
                <li>Reverb: Plate, 1.2s decay</li>
              </ul>
            </div>
            <div className="bg-purple-900/20 p-2 rounded border border-purple-800/30">
              <div className="font-medium text-purple-400 mb-1">Rap</div>
              <ul className="text-gray-400 list-disc pl-4 space-y-1">
                <li>EQ: +2dB@250Hz, +3dB@5kHz</li>
                <li>Comp: -15dB, 4:1, 3ms, 80ms</li>
                <li>Reverb: Plate, 0.8s decay</li>
              </ul>
            </div>
            <div className="bg-green-900/20 p-2 rounded border border-green-800/30">
              <div className="font-medium text-green-400 mb-1">R&B</div>
              <ul className="text-gray-400 list-disc pl-4 space-y-1">
                <li>EQ: +3dB@250Hz, +3dB@12kHz</li>
                <li>Comp: -20dB, 2.5:1, 10ms, 120ms</li>
                <li>Reverb: Hall, 1.5s decay</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Voice Model Info */}
      <div className="bg-blue-900/20 border border-blue-800/30 rounded-lg p-3 mb-6">
        <h3 className="text-sm font-medium text-blue-400 mb-1">Voice Model Information</h3>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="px-2 py-1 bg-blue-900/30 text-xs rounded border border-blue-700/30">
            {voiceModel.type === 'vocalsync' ? 'VocalSynth™ (Singing)' : 'FlowClone™ (Spoken)'}
          </span>
          <span className={`px-2 py-1 text-xs rounded border ${voiceModelScore && voiceModelScore >= 85
            ? 'bg-green-900/30 border-green-700/30'
            : voiceModelScore && voiceModelScore >= 70
              ? 'bg-yellow-900/30 border-yellow-700/30'
              : 'bg-red-900/30 border-red-700/30'
            }`}>
            Quality Score: {voiceModelScore}%
          </span>
        </div>
        <p className="text-xs text-gray-400">
          Your voice model "{voiceModel.name}" is ready for enhancement. Upload a recording to apply professional-grade
          processing tailored to your unique voice characteristics.
        </p>
      </div>

      {/* Recording Interface */}
      <div className="mb-8">
        <h3 className="text-lg font-medium mb-3 text-blue-400">Record Audio to Retune</h3>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Recording Controls */}
          <div className="flex-1 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
            {!isRecording ? (
              <button
                onClick={startRecording}
                className="w-full py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                Start Recording
              </button>
            ) : (
              <button
                onClick={stopRecording}
                className="w-full py-3 bg-red-600 hover:bg-red-500 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
                Stop Recording
              </button>
            )}

            {isRecording && (
              <div className="mt-4 flex items-center justify-center">
                <div className="w-4 h-4 rounded-full bg-red-500 animate-pulse mr-2"></div>
                <span className="text-red-400">Recording...</span>
              </div>
            )}

            {audioUrl && !isRecording && (
              <div className="mt-4">
                <p className="text-sm text-gray-400 mb-2">Your Recording:</p>
                <audio controls className="w-full">
                  <source src={audioUrl} type="audio/wav" />
                  Your browser does not support the audio element.
                </audio>

                <button
                  onClick={resetRecording}
                  className="mt-3 w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
                >
                  Record Again
                </button>
              </div>
            )}
          </div>

          {/* Upload Option */}
          <div className="flex-1 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
            <h4 className="text-md font-medium mb-3 text-gray-300">Or Upload Audio File</h4>

            <label className="flex flex-col items-center px-4 py-6 bg-gray-800/50 text-blue-400 rounded-lg border border-blue-400/30 border-dashed cursor-pointer hover:bg-gray-800/80 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <span className="mt-2 text-base">Select audio file</span>
              <span className="text-sm text-gray-400 mt-1">(WAV, MP3, OGG)</span>
              <input
                type="file"
                className="hidden"
                accept="audio/*"
                onChange={(e) => {
                  const files = e.target.files;
                  if (!files || files.length === 0) return;

                  const file = files[0];
                  const url = URL.createObjectURL(file);

                  setAudioBlob(file);
                  setAudioUrl(url);
                  setEnhancedAudioUrl(null);
                }}
              />
            </label>

            <p className="text-xs text-gray-400 mt-3 text-center">
              Supported formats: WAV, MP3, OGG, FLAC<br />
              Max file size: 50MB
            </p>
          </div>
        </div>
      </div>

      {/* Enhancement Settings */}
      {audioUrl && !isRecording && (
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-3 text-blue-400">Retune Settings</h3>

          {/* Presets */}
          <div className="grid grid-cols-3 gap-3 mb-6">
            <button
              onClick={() => applyPreset('podcast')}
              className={`p-3 rounded-lg border ${selectedPreset === 'podcast'
                ? 'bg-blue-600/50 border-blue-500'
                : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
            >
              <div className="font-medium">Podcast</div>
              <div className="text-xs text-gray-400">Clear, professional voice</div>
            </button>

            <button
              onClick={() => applyPreset('music')}
              className={`p-3 rounded-lg border ${selectedPreset === 'music'
                ? 'bg-purple-600/50 border-purple-500'
                : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
            >
              <div className="font-medium">Music</div>
              <div className="text-xs text-gray-400">Warm, resonant vocals</div>
            </button>

            <button
              onClick={() => applyPreset('speech')}
              className={`p-3 rounded-lg border ${selectedPreset === 'speech'
                ? 'bg-green-600/50 border-green-500'
                : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800/80'
                } transition-colors`}
            >
              <div className="font-medium">Speech</div>
              <div className="text-xs text-gray-400">Authoritative, clear delivery</div>
            </button>
          </div>

          {/* Sliders */}
          <div className="space-y-4">
            <h4 className="text-md font-medium mb-2 text-blue-400">Voice Quality</h4>
            {/* Clarity */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="clarity-slider" className="text-sm font-medium text-gray-300">Clarity</label>
                <span className="text-sm text-gray-400">{clarity}%</span>
              </div>
              <input
                id="clarity-slider"
                type="range"
                min="0"
                max="100"
                value={clarity}
                onChange={(e) => {
                  setClarity(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Resonance */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="resonance-slider" className="text-sm font-medium text-gray-300">Resonance</label>
                <span className="text-sm text-gray-400">{resonance}%</span>
              </div>
              <input
                id="resonance-slider"
                type="range"
                min="0"
                max="100"
                value={resonance}
                onChange={(e) => {
                  setResonance(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Confidence */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="confidence-slider" className="text-sm font-medium text-gray-300">Confidence</label>
                <span className="text-sm text-gray-400">{confidence}%</span>
              </div>
              <input
                id="confidence-slider"
                type="range"
                min="0"
                max="100"
                value={confidence}
                onChange={(e) => {
                  setConfidence(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Warmth */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="warmth-slider" className="text-sm font-medium text-gray-300">Warmth</label>
                <span className="text-sm text-gray-400">{warmth}%</span>
              </div>
              <input
                id="warmth-slider"
                type="range"
                min="0"
                max="100"
                value={warmth}
                onChange={(e) => {
                  setWarmth(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Stability */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="stability-slider" className="text-sm font-medium text-gray-300">Stability</label>
                <span className="text-sm text-gray-400">{stability}%</span>
              </div>
              <input
                id="stability-slider"
                type="range"
                min="0"
                max="100"
                value={stability}
                onChange={(e) => {
                  setStability(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            <div className="border-t border-gray-700 my-4"></div>

            <h4 className="text-md font-medium mb-2 text-purple-400">Vinn AI Technology</h4>

            {/* Harmonics */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="harmonics-slider" className="text-sm font-medium text-gray-300">Harmonics</label>
                <span className="text-sm text-gray-400">{harmonics}%</span>
              </div>
              <input
                id="harmonics-slider"
                type="range"
                min="0"
                max="100"
                value={harmonics}
                onChange={(e) => {
                  setHarmonics(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-purple-900/50 rounded-lg appearance-none cursor-pointer"
              />
              <p className="text-xs text-gray-500 mt-1">Adds richness and depth to your voice with natural-sounding harmonics</p>
            </div>

            {/* Naturalness */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="naturalness-slider" className="text-sm font-medium text-gray-300">Naturalness</label>
                <span className="text-sm text-gray-400">{naturalness}%</span>
              </div>
              <input
                id="naturalness-slider"
                type="range"
                min="0"
                max="100"
                value={naturalness}
                onChange={(e) => {
                  setNaturalness(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-purple-900/50 rounded-lg appearance-none cursor-pointer"
              />
              <p className="text-xs text-gray-500 mt-1">Reduces artificial qualities and adds human-like imperfections</p>
            </div>

            {/* Expressiveness */}
            <div>
              <div className="flex justify-between mb-1">
                <label htmlFor="expressiveness-slider" className="text-sm font-medium text-gray-300">Expressiveness</label>
                <span className="text-sm text-gray-400">{expressiveness}%</span>
              </div>
              <input
                id="expressiveness-slider"
                type="range"
                min="0"
                max="100"
                value={expressiveness}
                onChange={(e) => {
                  setExpressiveness(parseInt(e.target.value));
                  setSelectedPreset('custom');
                }}
                className="w-full h-2 bg-purple-900/50 rounded-lg appearance-none cursor-pointer"
              />
              <p className="text-xs text-gray-500 mt-1">Enhances emotional delivery and dynamic range of your voice</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-800 rounded-lg text-red-200 text-sm">
          {error}
        </div>
      )}

      {/* Enhanced Audio Preview */}
      {enhancedAudioUrl && (
        <div className="mb-8 bg-gray-900/50 p-4 rounded-lg border border-gray-800">
          <h3 className="text-lg font-medium mb-3 text-green-400">Enhanced Audio</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-400 mb-2">Original:</p>
              <audio controls className="w-full">
                <source src={audioUrl as string} type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
            </div>

            <div>
              <p className="text-sm text-gray-400 mb-2">Enhanced:</p>
              <audio controls className="w-full">
                <source src={enhancedAudioUrl} type="audio/wav" />
                Your browser does not support the audio element.
              </audio>
            </div>
          </div>
        </div>
      )}

      {/* Enhance Button */}
      {audioUrl && !isRecording && !enhancedAudioUrl && (
        <div className="flex justify-center">
          <button
            onClick={enhanceAudio}
            disabled={isProcessing || !audioUrl}
            className={`px-8 py-3 rounded-lg font-medium transition-colors ${isProcessing
              ? 'bg-gray-600 text-gray-300'
              : 'bg-blue-600 hover:bg-blue-500 text-white'
              }`}
          >
            {isProcessing ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Retuning Audio...
              </span>
            ) : (
              'Retune Audio'
            )}
          </button>
        </div>
      )}

      {/* Continue to Export Button */}
      {enhancedAudioUrl && (
        <div className="flex justify-center">
          <button
            onClick={() => onAudioEnhanced(enhancedAudioUrl, audioUrl as string)}
            className="px-8 py-3 bg-green-600 hover:bg-green-500 text-white rounded-lg font-medium transition-colors"
          >
            Continue to Export
          </button>
        </div>
      )}

      {/* Technical Guide */}
      <div className="mt-8 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-medium text-purple-400 mb-3">Technical Guide: Vinn AI Retune Technology</h3>

        <div className="space-y-4">
          <div>
            <h4 className="text-md font-medium text-white mb-1">Studio-Grade DSP Processing Chain</h4>
            <p className="text-sm text-gray-400 mb-2">
              Our professional-grade DSP (Digital Signal Processing) chain includes:
            </p>
            <ol className="text-sm text-gray-400 list-decimal pl-5 space-y-1">
              <li><span className="text-purple-400">High-Pass Filter</span> - 1st order HPF at 80 Hz, Q=0.7 to remove sub-100 Hz rumble</li>
              <li><span className="text-purple-400">Parametric EQ</span> - 6-band EQ with genre-specific settings:
                <ul className="list-disc pl-5 mt-1 space-y-0.5">
                  <li>Band 1: Low Shelf at 120 Hz, Q=1.2</li>
                  <li>Band 2: Bell at 250 Hz, Q=1.5</li>
                  <li>Band 3: Bell at 500 Hz, Q=2.0</li>
                  <li>Band 4: Bell at 2 kHz, Q=1.4</li>
                  <li>Band 5: Bell at 5 kHz, Q=1.0</li>
                  <li>Band 6: High Shelf at 12 kHz, Q=0.8</li>
                </ul>
              </li>
              <li><span className="text-purple-400">Compressor</span> - Genre-specific settings:
                <ul className="list-disc pl-5 mt-1 space-y-0.5">
                  <li>Pop: Threshold –18 dB, Ratio 3:1, Attack 5 ms, Release 100 ms</li>
                  <li>Rap: Threshold –15 dB, Ratio 4:1, Attack 3 ms, Release 80 ms</li>
                  <li>R&B: Threshold –20 dB, Ratio 2.5:1, Attack 10 ms, Release 120 ms</li>
                </ul>
              </li>
              <li><span className="text-purple-400">De-esser</span> - Freq: 6 kHz ±1 kHz, Q=5.0, Threshold –30 dB, Range –6 dB max</li>
              <li><span className="text-purple-400">Pitch Correction</span> - Retune Speed 20 ms, Humanize 30%, Flex-Tune On, Key/Scale selectable</li>
              <li><span className="text-purple-400">Saturation</span> - Tube Mode (Drive 25%, Mix 40%) or Tape Mode (Drive 15%, Mix 30%)</li>
              <li><span className="text-purple-400">Spatial FX</span> - Reverb (Plate/Hall) and Delay (1/8 note or 120 ms slapback)</li>
              <li><span className="text-purple-400">Chorus/Doubler</span> - Rate: 0.8 Hz, Depth 20%, Mix 15%</li>
              <li><span className="text-purple-400">Limiter</span> - Target: –14 LUFS integrated, –1 dBTP ceiling</li>
            </ol>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Vinn AI Integration</h4>
            <p className="text-sm text-gray-400 mb-2">
              Vinn AI's proprietary voice technology enhances your voice by:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li>Adding natural harmonics that complement your voice's fundamental frequencies</li>
              <li>Introducing subtle human-like variations in timing and delivery</li>
              <li>Enhancing emotional expressiveness while preserving your unique vocal identity</li>
              <li>Applying genre-specific processing optimized for your selected preset</li>
            </ul>
            <p className="text-sm text-gray-400 mt-2 mb-1">
              Technical implementation uses APIT's proprietary audio processing suite:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li><span className="text-blue-400">APIT Studio Suite™</span> - Comprehensive audio processing framework</li>
              <li><span className="text-blue-400">SonicCore™</span> - High-quality filter and signal processing</li>
              <li><span className="text-blue-400">VocalFX™</span> - Advanced vocal effects processing</li>
              <li><span className="text-blue-400">PitchPerfect™</span> - Precision pitch correction technology</li>
              <li><span className="text-blue-400">HarmonicEngine™</span> - Harmonic enhancement and spectral processing</li>
            </ul>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Broadcast Standards</h4>
            <p className="text-sm text-gray-400">
              All processed audio meets professional broadcast standards with a target integrated loudness of -14 LUFS
              and a true peak ceiling of -1 dBTP, ensuring your vocals sound professional on all platforms.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
