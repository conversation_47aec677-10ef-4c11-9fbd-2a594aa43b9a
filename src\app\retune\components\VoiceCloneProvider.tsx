'use client';

import ElevenLabsVoiceCloner from '@/components/VoiceCloner/ElevenLabsVoiceCloner';
import PlayHtVoiceCloner from '@/components/VoiceCloner/PlayHtVoiceCloner';
import ResembleVoiceCloner from '@/components/VoiceCloner/ResembleVoiceCloner';
import { useState } from 'react';

// Define voice model type
interface VoiceModel {
  id: string;
  name: string;
  type: 'vocalsync' | 'flowclone';
  qualityScore: number;
  createdAt: string;
}

interface VoiceCloneProviderProps {
  onVoiceModelCreated: (model: VoiceModel, score: number) => void;
}

export default function VoiceCloneProvider({ onVoiceModelCreated }: VoiceCloneProviderProps) {
  // State for provider selection
  const [activeProvider, setActiveProvider] = useState<'elevenlabs' | 'resemble' | 'playht'>('elevenlabs');

  return (
    <div className="space-y-8">
      {/* Provider Selection */}
      <div className="retune-glass-container p-5">
        <h3 className="text-lg font-medium retune-text-glow-cyan mb-4">Select Voice Cloning Provider</h3>
        <div className="flex flex-wrap gap-3 mb-4">
          <button
            onClick={() => setActiveProvider('elevenlabs')}
            className={`retune-button py-2 px-4 text-sm ${activeProvider === 'elevenlabs'
                ? 'bg-black/40 border border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : 'bg-black/20 border border-white/10 hover:border-[#00FFEE]/30'
              }`}
          >
            ElevenLabs
          </button>
          <button
            onClick={() => setActiveProvider('resemble')}
            className={`retune-button py-2 px-4 text-sm ${activeProvider === 'resemble'
                ? 'bg-black/40 border border-[#C600FF]/60 shadow-[0_0_15px_rgba(198,0,255,0.4)]'
                : 'bg-black/20 border border-white/10 hover:border-[#C600FF]/30'
              }`}
          >
            Resemble AI
          </button>
          <button
            onClick={() => setActiveProvider('playht')}
            className={`retune-button py-2 px-4 text-sm ${activeProvider === 'playht'
                ? 'bg-black/40 border border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]'
                : 'bg-black/20 border border-white/10 hover:border-[#00FFEE]/30'
              }`}
          >
            Play.ht
          </button>
        </div>

        <div className="mt-4 text-sm text-white/70 p-3 bg-black/20 rounded-lg border border-white/5">
          {activeProvider === 'elevenlabs' && (
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-black/40 border border-[#00FFEE]/30 flex items-center justify-center mr-3 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE]" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <div>
                <p className="retune-text-glow-cyan mb-1">ElevenLabs Voice AI</p>
                <p>Provides high-quality voice cloning with natural-sounding results. Best for general purpose voice cloning with excellent emotional range.</p>
              </div>
            </div>
          )}
          {activeProvider === 'resemble' && (
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-black/40 border border-[#C600FF]/30 flex items-center justify-center mr-3 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#C600FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              </div>
              <div>
                <p className="retune-text-glow-magenta mb-1">Resemble AI</p>
                <p>Offers professional voice cloning with excellent emotional range and control. Ideal for expressive performances and musical applications.</p>
              </div>
            </div>
          )}
          {activeProvider === 'playht' && (
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full bg-black/40 border border-[#00FFEE]/30 flex items-center justify-center mr-3 flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#00FFEE]" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m0 0l-2.828 2.828m0 0a9 9 0 010-12.728m2.828 2.828a5 5 0 00-1.414 1.414m0 0L8.464 5.586m0 0a9 9 0 0112.728 0" />
                </svg>
              </div>
              <div>
                <p className="retune-text-glow-cyan mb-1">Play.ht</p>
                <p>Specializes in fast voice cloning with good quality. Great for quick prototyping, testing, and applications requiring rapid turnaround.</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Active Provider Component */}
      <div className="retune-glass-container p-5">
        <h3 className="text-lg font-medium retune-text-glow-cyan mb-4">
          {activeProvider === 'elevenlabs' && 'ElevenLabs Voice Cloning'}
          {activeProvider === 'resemble' && 'Resemble AI Voice Cloning'}
          {activeProvider === 'playht' && 'Play.ht Voice Cloning'}
        </h3>

        {activeProvider === 'elevenlabs' && <ElevenLabsVoiceCloner />}
        {activeProvider === 'resemble' && <ResembleVoiceCloner />}
        {activeProvider === 'playht' && <PlayHtVoiceCloner />}
      </div>
    </div>
  );
}
