'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface VoiceModel {
  name: string;
  type: string;
  engine: string;
  isMorphed: boolean;
  isAged: boolean;
  isAccented: boolean;
}

export default function VoiceMorphing() {
  const [models, setModels] = useState<VoiceModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Selected models for morphing
  const [modelA, setModelA] = useState<string>('');
  const [modelB, setModelB] = useState<string>('');
  const [blendRatio, setBlendRatio] = useState<number>(0.5);

  // Voice aging
  const [ageModel, setAgeModel] = useState<string>('');
  const [ageFactor, setAgeFactor] = useState<number>(0.5);

  // Accent transformation
  const [accentModel, setAccentModel] = useState<string>('');
  const [accent, setAccent] = useState<string>('british');
  const [accentStrength, setAccentStrength] = useState<number>(0.5);

  // Active tab
  const [activeTab, setActiveTab] = useState<'morph' | 'age' | 'accent'>('morph');

  // Processing state
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState<any | null>(null);

  // Fetch available models on component mount
  useEffect(() => {
    fetchModels();
  }, []);

  // Fetch available voice models (mock data)
  const fetchModels = async () => {
    setLoading(true);
    setError(null);

    try {
      // Mock data instead of API call
      const mockModels = [
        { name: 'My Voice', type: 'Original', engine: 'FlowClone', isMorphed: false, isAged: false, isAccented: false },
        { name: 'Studio Voice', type: 'Original', engine: 'VocalSynth', isMorphed: false, isAged: false, isAccented: false },
        { name: 'Podcast Voice', type: 'Original', engine: 'FlowClone', isMorphed: false, isAged: false, isAccented: false },
        { name: 'Morphed Voice 1', type: 'Morphed', engine: 'FlowClone', isMorphed: true, isAged: false, isAccented: false },
        { name: 'Aged Voice 1', type: 'Aged', engine: 'VocalSynth', isMorphed: false, isAged: true, isAccented: false },
        { name: 'British Accent', type: 'Accented', engine: 'FlowClone', isMorphed: false, isAged: false, isAccented: true },
      ];

      // Simulate network delay
      setTimeout(() => {
        setModels(mockModels);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error fetching models:', error);
      setError(`Error fetching models: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLoading(false);
    }
  };

  // Handle morphing (mock implementation)
  const handleMorph = async () => {
    if (!modelA || !modelB) {
      setError('Please select both models for morphing');
      return;
    }

    setProcessing(true);
    setError(null);
    setResult(null);

    try {
      // Simulate API call with timeout
      setTimeout(() => {
        // Create mock result
        const mockResult = {
          operation: 'morph',
          modelInfo: {
            path: `/models/morphed_${Date.now()}`,
            type: 'Morphed',
            engine: 'RVC'
          }
        };

        setResult(mockResult);

        // Add new model to the list
        setModels(prev => [
          ...prev,
          {
            name: `${modelA} + ${modelB}`,
            type: 'Morphed',
            engine: 'RVC',
            isMorphed: true,
            isAged: false,
            isAccented: false
          }
        ]);

        setProcessing(false);
      }, 2000);
    } catch (error) {
      console.error('Error morphing voices:', error);
      setError(`Error morphing voices: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setProcessing(false);
    }
  };

  // Handle aging (mock implementation)
  const handleAge = async () => {
    if (!ageModel) {
      setError('Please select a model for aging');
      return;
    }

    setProcessing(true);
    setError(null);
    setResult(null);

    try {
      // Simulate API call with timeout
      setTimeout(() => {
        // Create mock result
        const mockResult = {
          operation: 'age',
          modelInfo: {
            path: `/models/aged_${Date.now()}`,
            type: 'Aged',
            engine: 'SVC'
          }
        };

        setResult(mockResult);

        // Add new model to the list
        setModels(prev => [
          ...prev,
          {
            name: `${ageModel} ${ageFactor < 0 ? 'Younger' : 'Older'}`,
            type: 'Aged',
            engine: 'SVC',
            isMorphed: false,
            isAged: true,
            isAccented: false
          }
        ]);

        setProcessing(false);
      }, 2000);
    } catch (error) {
      console.error('Error aging voice:', error);
      setError(`Error aging voice: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setProcessing(false);
    }
  };

  // Handle accent transformation (mock implementation)
  const handleAccent = async () => {
    if (!accentModel || !accent) {
      setError('Please select a model and accent');
      return;
    }

    setProcessing(true);
    setError(null);
    setResult(null);

    try {
      // Simulate API call with timeout
      setTimeout(() => {
        // Create mock result
        const mockResult = {
          operation: 'accent',
          modelInfo: {
            path: `/models/accent_${Date.now()}`,
            type: 'Accented',
            engine: 'RVC'
          }
        };

        setResult(mockResult);

        // Add new model to the list
        setModels(prev => [
          ...prev,
          {
            name: `${accentModel} ${accent.charAt(0).toUpperCase() + accent.slice(1)}`,
            type: 'Accented',
            engine: 'RVC',
            isMorphed: false,
            isAged: false,
            isAccented: true
          }
        ]);

        setProcessing(false);
      }, 2000);
    } catch (error) {
      console.error('Error applying accent:', error);
      setError(`Error applying accent: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setProcessing(false);
    }
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-6 mb-8 border border-gray-700">
      <h2 className="text-2xl font-semibold mb-4">Voice Transformation Studio <span className="text-sm font-normal text-purple-400 ml-2">Powered by Sesame AI</span></h2>

      <div className="bg-blue-900/20 border border-blue-800/30 rounded-lg p-3 mb-6">
        <h3 className="text-sm font-medium text-blue-400 mb-1">Model Integration</h3>
        <div className="flex flex-wrap gap-2 mb-2">
          <span className="px-2 py-1 bg-blue-900/30 text-xs rounded border border-blue-700/30">so-vits-svc Embeddings</span>
          <span className="px-2 py-1 bg-purple-900/30 text-xs rounded border border-purple-700/30">RVC Voice Conversion</span>
          <span className="px-2 py-1 bg-green-900/30 text-xs rounded border border-green-700/30">Sesame AI Voice Morphing</span>
        </div>
        <p className="text-xs text-gray-400">
          Our voice transformation technology extracts voice embeddings from your models and applies advanced morphing techniques.
          For singing voices, we use so-vits-svc embeddings, while RVC is used for spoken word and rap.
          Sesame AI's technology ensures natural transitions between voice characteristics.
        </p>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-6">
        <button
          onClick={() => setActiveTab('morph')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'morph'
              ? 'text-blue-400 border-b-2 border-blue-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Voice Morphing
        </button>

        <button
          onClick={() => setActiveTab('age')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'age'
              ? 'text-purple-400 border-b-2 border-purple-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Voice Aging
        </button>

        <button
          onClick={() => setActiveTab('accent')}
          className={`px-4 py-2 font-medium ${
            activeTab === 'accent'
              ? 'text-green-400 border-b-2 border-green-400'
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          Accent Training
        </button>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"></div>
          <p className="text-gray-400">Loading voice models...</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Voice Morphing Tab */}
      {activeTab === 'morph' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Model A Selection */}
            <div>
              <h3 className="text-lg font-medium mb-3 text-blue-400">Voice Model A</h3>
              <select
                value={modelA}
                onChange={(e) => setModelA(e.target.value)}
                className="w-full bg-gray-700/50 border border-gray-600 rounded-lg p-3 text-white"
                disabled={processing}
              >
                <option value="">Select a voice model</option>
                {models
                  .filter(model => !model.isMorphed)
                  .map(model => (
                    <option key={model.name} value={model.name}>
                      {model.name} ({model.type})
                    </option>
                  ))}
              </select>
            </div>

            {/* Model B Selection */}
            <div>
              <h3 className="text-lg font-medium mb-3 text-blue-400">Voice Model B</h3>
              <select
                value={modelB}
                onChange={(e) => setModelB(e.target.value)}
                className="w-full bg-gray-700/50 border border-gray-600 rounded-lg p-3 text-white"
                disabled={processing}
              >
                <option value="">Select a voice model</option>
                {models
                  .filter(model => !model.isMorphed)
                  .map(model => (
                    <option key={model.name} value={model.name}>
                      {model.name} ({model.type})
                    </option>
                  ))}
              </select>
            </div>
          </div>

          {/* Blend Ratio Slider */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-blue-400">Blend Ratio</h3>
              <span className="text-gray-400">{Math.round(blendRatio * 100)}%</span>
            </div>

            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-400">Model A</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={blendRatio}
                onChange={(e) => setBlendRatio(parseFloat(e.target.value))}
                className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                disabled={processing}
              />
              <span className="text-sm text-gray-400">Model B</span>
            </div>

            {/* Visual representation of blend */}
            <div className="mt-4 h-4 bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                style={{ width: `${blendRatio * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Morph Button */}
          <button
            onClick={handleMorph}
            disabled={processing || !modelA || !modelB}
            className="w-full py-3 bg-blue-600 hover:bg-blue-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
          >
            {processing ? 'Creating Morphed Voice...' : 'Create Morphed Voice'}
          </button>
        </motion.div>
      )}

      {/* Voice Aging Tab */}
      {activeTab === 'age' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Model Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-purple-400">Voice Model</h3>
            <select
              value={ageModel}
              onChange={(e) => setAgeModel(e.target.value)}
              className="w-full bg-gray-700/50 border border-gray-600 rounded-lg p-3 text-white"
              disabled={processing}
            >
              <option value="">Select a voice model</option>
              {models
                .filter(model => !model.isAged)
                .map(model => (
                  <option key={model.name} value={model.name}>
                    {model.name} ({model.type})
                  </option>
                ))}
            </select>
          </div>

          {/* Age Factor Slider */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-purple-400">Age Factor</h3>
              <span className="text-gray-400">
                {ageFactor < 0 ? `${Math.abs(Math.round(ageFactor * 100))}% Younger` : `${Math.round(ageFactor * 100)}% Older`}
              </span>
            </div>

            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-400">Younger</span>
              <input
                type="range"
                min="-1"
                max="1"
                step="0.01"
                value={ageFactor}
                onChange={(e) => setAgeFactor(parseFloat(e.target.value))}
                className="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                disabled={processing}
              />
              <span className="text-sm text-gray-400">Older</span>
            </div>

            {/* Visual representation of age */}
            <div className="mt-4 h-4 bg-gray-700 rounded-full overflow-hidden relative">
              <div className="absolute top-0 left-1/2 w-1 h-full bg-gray-500"></div>
              <div
                className={`h-full ${ageFactor < 0 ? 'bg-green-500' : 'bg-purple-500'}`}
                style={{
                  width: `${Math.abs(ageFactor) * 50}%`,
                  marginLeft: ageFactor < 0 ? 'auto' : '50%',
                  marginRight: ageFactor < 0 ? '50%' : 'auto'
                }}
              ></div>
            </div>
          </div>

          {/* Age Button */}
          <button
            onClick={handleAge}
            disabled={processing || !ageModel}
            className="w-full py-3 bg-purple-600 hover:bg-purple-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
          >
            {processing ? 'Applying Age Transformation...' : 'Apply Age Transformation'}
          </button>
        </motion.div>
      )}

      {/* Accent Training Tab */}
      {activeTab === 'accent' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Model Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-green-400">Voice Model</h3>
            <select
              value={accentModel}
              onChange={(e) => setAccentModel(e.target.value)}
              className="w-full bg-gray-700/50 border border-gray-600 rounded-lg p-3 text-white"
              disabled={processing}
            >
              <option value="">Select a voice model</option>
              {models
                .filter(model => !model.isAccented)
                .map(model => (
                  <option key={model.name} value={model.name}>
                    {model.name} ({model.type})
                  </option>
                ))}
            </select>
          </div>

          {/* Accent Selection */}
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-green-400">Accent</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              {['british', 'american', 'australian', 'indian', 'spanish', 'french', 'german'].map((accentOption) => (
                <button
                  key={accentOption}
                  onClick={() => setAccent(accentOption)}
                  className={`p-3 rounded-lg border ${
                    accent === accentOption
                      ? 'bg-green-600/50 border-green-500'
                      : 'bg-gray-700/30 border-gray-600 hover:bg-gray-700/50'
                  } transition-colors`}
                  disabled={processing}
                >
                  <div className="font-medium capitalize">{accentOption}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Accent Strength Slider */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-green-400">Accent Strength</h3>
              <span className="text-gray-400">{Math.round(accentStrength * 100)}%</span>
            </div>

            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={accentStrength}
              onChange={(e) => setAccentStrength(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              disabled={processing}
            />
          </div>

          {/* Accent Button */}
          <button
            onClick={handleAccent}
            disabled={processing || !accentModel || !accent}
            className="w-full py-3 bg-green-600 hover:bg-green-500 rounded-lg text-white font-medium transition-colors disabled:opacity-50"
          >
            {processing ? 'Applying Accent...' : 'Apply Accent'}
          </button>
        </motion.div>
      )}

      {/* Result */}
      {result && (
        <div className="mt-6 bg-gray-700/30 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2 text-blue-400">Result</h3>
          <p className="text-green-400 mb-2">Successfully created new voice model!</p>

          <div className="bg-gray-800/50 p-3 rounded border border-gray-700">
            <p className="text-gray-300">
              <span className="text-blue-400 font-medium">Operation:</span> {result.operation}
            </p>
            <p className="text-gray-300">
              <span className="text-blue-400 font-medium">Model:</span> {result.modelInfo.path.split('/').pop()}
            </p>
            <p className="text-gray-300">
              <span className="text-blue-400 font-medium">Type:</span> {result.modelInfo.type}
            </p>
            <p className="text-gray-300">
              <span className="text-blue-400 font-medium">Engine:</span> {result.modelInfo.engine}
            </p>
          </div>

          <p className="mt-4 text-gray-400 text-sm">
            The new voice model is now available for use in the Voice Studio.
          </p>
        </div>
      )}

      {/* Technical Guide */}
      <div className="mt-8 bg-gray-800/50 rounded-lg p-4 border border-gray-700 mb-8">
        <h3 className="text-lg font-medium text-blue-400 mb-3">Technical Guide: Voice Transformation</h3>

        <div className="space-y-4">
          <div>
            <h4 className="text-md font-medium text-white mb-1">Voice Embedding Technology</h4>
            <p className="text-sm text-gray-400 mb-2">
              Our voice transformation system works by manipulating voice embeddings - mathematical representations of voice characteristics:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li><span className="text-blue-400">Voice Morphing</span> - Blends two voice models by interpolating between their embeddings</li>
              <li><span className="text-purple-400">Voice Aging</span> - Modifies embeddings to simulate younger or older versions of a voice</li>
              <li><span className="text-green-400">Accent Training</span> - Applies accent-specific transformations to voice embeddings</li>
            </ul>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Model Integration</h4>
            <p className="text-sm text-gray-400 mb-2">
              Different voice models use different technologies:
            </p>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li><span className="text-blue-400">VocalSynth™</span> - Used for singing voices with 256-dimensional embeddings</li>
              <li><span className="text-purple-400">FlowClone™</span> - Used for spoken word with focus on preserving speech patterns</li>
              <li><span className="text-green-400">Vinn AI</span> - Provides natural transitions and ensures voice quality during transformation</li>
            </ul>
          </div>

          <div>
            <h4 className="text-md font-medium text-white mb-1">Best Practices</h4>
            <ul className="text-sm text-gray-400 list-disc pl-5 space-y-1">
              <li>Use high-quality voice models (Clone Quality Score ≥85) for best results</li>
              <li>For voice morphing, choose voices with similar characteristics for more natural blending</li>
              <li>When aging voices, use smaller adjustments (±30%) for more realistic results</li>
              <li>For accent training, start with a lower strength setting and gradually increase if needed</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Available Models */}
      <div>
        <h3 className="text-lg font-medium mb-3 text-blue-400">Available Voice Models</h3>

        {models.length === 0 ? (
          <p className="text-gray-400">No voice models found.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
            {models.map((model) => (
              <div
                key={model.name}
                className={`p-3 rounded-lg border ${
                  model.isMorphed
                    ? 'border-blue-500/50 bg-blue-900/20'
                    : model.isAged
                      ? 'border-purple-500/50 bg-purple-900/20'
                      : model.isAccented
                        ? 'border-green-500/50 bg-green-900/20'
                        : 'border-gray-700 bg-gray-800/50'
                }`}
              >
                <div className="font-medium text-white">{model.name}</div>
                <div className="text-sm text-gray-400">{model.type} ({model.engine})</div>

                {model.isMorphed && (
                  <div className="mt-1 text-xs text-blue-400">Morphed</div>
                )}

                {model.isAged && (
                  <div className="mt-1 text-xs text-purple-400">Age-transformed</div>
                )}

                {model.isAccented && (
                  <div className="mt-1 text-xs text-green-400">Accent-trained</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
