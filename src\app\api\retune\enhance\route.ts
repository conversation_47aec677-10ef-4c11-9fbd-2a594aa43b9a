import fs from 'fs';
import { NextResponse } from 'next/server';
import path from 'path';

// Import utilities
import { createTempDirectory, deleteFile, saveFile } from '@/utils/fileManager';

// ElevenLabs API integration for voice enhancement
export async function POST(request: Request) {
  try {
    // Get the form data from the request
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const voiceModelId = formData.get('voiceModelId') as string;

    if (!audioFile) {
      return NextResponse.json({ error: 'No audio file provided' }, { status: 400 });
    }

    if (!voiceModelId) {
      return NextResponse.json({ error: 'No voice model ID provided' }, { status: 400 });
    }

    // Extract enhancement settings
    const clarity = parseFloat(formData.get('clarity') as string || '50') / 100;
    const resonance = parseFloat(formData.get('resonance') as string || '50') / 100;
    const confidence = parseFloat(formData.get('confidence') as string || '70') / 100;
    const warmth = parseFloat(formData.get('warmth') as string || '50') / 100;
    const stability = parseFloat(formData.get('stability') as string || '60') / 100;
    const preset = formData.get('preset') as string || 'custom';

    // Additional Vinn AI settings
    const harmonics = parseFloat(formData.get('harmonics') as string || '50') / 100;
    const naturalness = parseFloat(formData.get('naturalness') as string || '60') / 100;
    const expressiveness = parseFloat(formData.get('expressiveness') as string || '40') / 100;

    console.log('Enhancement settings:', {
      clarity, resonance, confidence, warmth, stability, preset,
      harmonics, naturalness, expressiveness
    });

    // Get the ElevenLabs API key from environment variables
    const apiKey = process.env.ELEVENLABS_API_KEY;
    if (!apiKey || apiKey === 'your_elevenlabs_api_key_here') {
      console.error('ElevenLabs API key not configured');
      return NextResponse.json({ error: 'Voice enhancement service not configured' }, { status: 500 });
    }

    // Create a temporary directory for this request
    const { id: requestId, path: requestDir } = createTempDirectory();

    // Save the audio file
    const inputFilePath = await saveFile(audioFile, requestDir, `input.wav`);
    const outputFilePath = path.join(requestDir, `enhanced.wav`);

    // Read the audio file as base64
    const audioBuffer = fs.readFileSync(inputFilePath);
    const audioBase64 = audioBuffer.toString('base64');

    // Map our enhancement settings to ElevenLabs parameters
    // ElevenLabs doesn't have direct equivalents, so we'll map them to their voice settings
    // For this example, we'll use the voice settings API to modify the voice characteristics

    // First, get the voice settings
    const voiceSettingsResponse = await fetch(`https://api.elevenlabs.io/v1/voices/${voiceModelId}/settings`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'xi-api-key': apiKey
      }
    });

    if (!voiceSettingsResponse.ok) {
      console.error('Failed to get voice settings from ElevenLabs');
      // If we can't get the voice settings, we'll use default settings
    }

    // Default voice settings
    let voiceSettings = {
      stability: 0.5,
      similarity_boost: 0.75,
      style: 0.0,
      use_speaker_boost: true
    };

    try {
      const voiceSettingsData = await voiceSettingsResponse.json();
      voiceSettings = voiceSettingsData;
    } catch (e) {
      console.error('Error parsing voice settings:', e);
      // Continue with default settings
    }

    // Map our enhancement settings to ElevenLabs parameters
    // stability: lower values = more expressive, higher values = more stable
    // similarity_boost: higher values = more similar to original voice
    // style: higher values = more stylistic variation

    // Apply our enhancement settings
    voiceSettings.stability = Math.max(0, Math.min(1, 1 - expressiveness)); // Inverse of expressiveness
    voiceSettings.similarity_boost = Math.max(0, Math.min(1, confidence)); // Directly map confidence
    voiceSettings.style = Math.max(0, Math.min(1, harmonics)); // Map harmonics to style

    // Apply preset adjustments
    if (preset === 'podcast') {
      voiceSettings.stability = 0.75; // More stable for clear speech
      voiceSettings.similarity_boost = 0.8; // Higher similarity for consistent voice
      voiceSettings.style = 0.3; // Moderate style for natural sound
    } else if (preset === 'music') {
      voiceSettings.stability = 0.4; // Less stable for more expression
      voiceSettings.similarity_boost = 0.7; // Good similarity but allow some variation
      voiceSettings.style = 0.7; // Higher style for musical quality
    } else if (preset === 'speech') {
      voiceSettings.stability = 0.8; // Very stable for clear speech
      voiceSettings.similarity_boost = 0.9; // High similarity for consistent voice
      voiceSettings.style = 0.2; // Low style for natural speech
    }

    // Update the voice settings
    const updateSettingsResponse = await fetch(`https://api.elevenlabs.io/v1/voices/${voiceModelId}/settings/edit`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey
      },
      body: JSON.stringify(voiceSettings)
    });

    if (!updateSettingsResponse.ok) {
      console.error('Failed to update voice settings');
      // Continue anyway, we'll use the default settings
    }

    // Now use the voice to generate enhanced audio
    // We'll use the text-to-speech endpoint with the audio as the text
    // This is a workaround since ElevenLabs doesn't have a direct voice enhancement API

    // For this example, we'll extract text from the audio using a simple placeholder
    // In a real implementation, you would use speech-to-text to get the actual text
    const placeholderText = "This is enhanced audio using ElevenLabs voice technology.";

    // Generate enhanced audio
    const enhanceResponse = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceModelId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey
      },
      body: JSON.stringify({
        text: placeholderText,
        model_id: "eleven_monolingual_v1",
        voice_settings: voiceSettings
      })
    });

    if (!enhanceResponse.ok) {
      console.error('Failed to enhance audio with ElevenLabs');
      // Fallback to original audio
      fs.copyFileSync(inputFilePath, outputFilePath);
    } else {
      // Save the enhanced audio
      const enhancedArrayBuffer = await enhanceResponse.arrayBuffer();
      const enhancedBuffer = Buffer.from(enhancedArrayBuffer);
      fs.writeFileSync(outputFilePath, enhancedBuffer);
    }

    // Read the enhanced file
    const finalEnhancedBuffer = fs.readFileSync(outputFilePath);

    // Clean up temporary files
    deleteFile(inputFilePath);
    // Don't delete the output file yet as we're reading it

    // Return the enhanced audio as a blob
    const response = new NextResponse(finalEnhancedBuffer, {
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Disposition': 'attachment; filename="enhanced.mp3"'
      }
    });

    // Now delete the output file
    deleteFile(outputFilePath);

    return response;

  } catch (error) {
    console.error('Error enhancing audio:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to enhance audio' },
      { status: 500 }
    );
  }
}
