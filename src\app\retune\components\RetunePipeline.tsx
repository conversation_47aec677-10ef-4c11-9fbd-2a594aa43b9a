'use client';

import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { ThemeButton, ThemeCard, ThemeHeading, ThemeSelect } from '@/components/ui';

interface PipelinePreset {
  id: string;
  name: string;
  description: string;
  steps: string[];
}

interface RetunePipelineProps {
  onProcessingComplete?: (audioUrl: string) => void;
}

const RetunePipeline: React.FC<RetunePipelineProps> = ({ onProcessingComplete }) => {
  // State
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [presets, setPresets] = useState<PipelinePreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string>('');
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string | null>(null);
  const [processingSteps, setProcessingSteps] = useState<{
    current: string | null;
    completed: string[];
    failed: string[];
  }>({
    current: null,
    completed: [],
    failed: []
  });
  
  // Refs
  const audioInputRef = useRef<HTMLInputElement>(null);
  
  // Fetch presets on mount
  useEffect(() => {
    const fetchPresets = async () => {
      try {
        const response = await fetch('/api/retune/pipeline/presets');
        
        if (!response.ok) {
          throw new Error('Failed to fetch presets');
        }
        
        const data = await response.json();
        setPresets(data);
        
        // Set default preset
        if (data.length > 0) {
          setSelectedPreset(data[0].id);
        }
      } catch (error) {
        console.error('Error fetching presets:', error);
        toast.error('Failed to fetch presets');
      }
    };
    
    fetchPresets();
  }, []);
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setAudioFile(file);
      
      // Create URL for audio preview
      const url = URL.createObjectURL(file);
      setAudioUrl(url);
      
      // Reset processed audio
      setProcessedAudioUrl(null);
      
      // Reset processing steps
      setProcessingSteps({
        current: null,
        completed: [],
        failed: []
      });
    }
  };
  
  // Handle preset selection
  const handlePresetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedPreset(e.target.value);
  };
  
  // Process audio through pipeline
  const handleProcess = async () => {
    if (!audioFile || !selectedPreset) {
      toast.error('Please select an audio file and a preset');
      return;
    }
    
    try {
      setIsProcessing(true);
      const toastId = toast.loading('Processing audio...');
      
      // Get the selected preset
      const preset = presets.find(p => p.id === selectedPreset);
      
      if (!preset) {
        throw new Error('Invalid preset');
      }
      
      // Update processing steps
      setProcessingSteps({
        current: preset.steps[0],
        completed: [],
        failed: []
      });
      
      // Create form data
      const formData = new FormData();
      formData.append('audio', audioFile);
      formData.append('preset', selectedPreset);
      
      // Send request
      const response = await fetch('/api/retune/pipeline', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process audio');
      }
      
      // The response is the audio blob
      const audioBlob = await response.blob();
      const url = URL.createObjectURL(audioBlob);
      setProcessedAudioUrl(url);
      
      // Update processing steps
      setProcessingSteps({
        current: null,
        completed: preset.steps,
        failed: []
      });
      
      toast.success('Audio processing complete', { id: toastId });
      
      // Call callback if provided
      if (onProcessingComplete) {
        onProcessingComplete(url);
      }
    } catch (error) {
      console.error('Error processing audio:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to process audio');
      
      // Update processing steps
      setProcessingSteps(prev => ({
        current: null,
        completed: prev.completed,
        failed: [...prev.failed, prev.current || '']
      }));
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) URL.revokeObjectURL(audioUrl);
      if (processedAudioUrl) URL.revokeObjectURL(processedAudioUrl);
    };
  }, [audioUrl, processedAudioUrl]);
  
  // Get step status
  const getStepStatus = (step: string) => {
    if (processingSteps.current === step) return 'processing';
    if (processingSteps.completed.includes(step)) return 'completed';
    if (processingSteps.failed.includes(step)) return 'failed';
    return 'pending';
  };
  
  return (
    <ThemeCard className="p-6">
      <ThemeHeading level={3} className="mb-4">Retune Pipeline</ThemeHeading>
      
      <div className="mb-6">
        <p className="text-sm text-gray-300 mb-4">
          Process your audio through a complete enhancement pipeline, combining multiple AI technologies
          for professional-grade results.
        </p>
        
        {/* Audio Input */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Select Audio File</label>
          <input
            type="file"
            ref={audioInputRef}
            onChange={handleFileChange}
            accept="audio/*"
            className="hidden"
          />
          <div className="flex items-center gap-3">
            <ThemeButton
              onClick={() => audioInputRef.current?.click()}
              disabled={isProcessing}
              variant="secondary"
            >
              Browse Files
            </ThemeButton>
            <span className="text-sm truncate">
              {audioFile ? audioFile.name : 'No file selected'}
            </span>
          </div>
        </div>
        
        {/* Audio Player */}
        {audioUrl && (
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Preview Audio</label>
            <audio
              controls
              src={audioUrl}
              className="w-full"
            ></audio>
          </div>
        )}
        
        {/* Preset Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Enhancement Preset</label>
          <ThemeSelect
            value={selectedPreset}
            onChange={handlePresetChange}
            disabled={isProcessing || presets.length === 0}
          >
            {presets.length === 0 ? (
              <option value="">Loading presets...</option>
            ) : (
              presets.map(preset => (
                <option key={preset.id} value={preset.id}>
                  {preset.name}
                </option>
              ))
            )}
          </ThemeSelect>
          
          {/* Preset Description */}
          {selectedPreset && (
            <p className="mt-2 text-sm text-gray-400">
              {presets.find(p => p.id === selectedPreset)?.description}
            </p>
          )}
        </div>
        
        {/* Process Button */}
        <ThemeButton
          onClick={handleProcess}
          disabled={!audioFile || !selectedPreset || isProcessing}
          className="w-full"
        >
          {isProcessing ? 'Processing...' : 'Process Audio'}
        </ThemeButton>
      </div>
      
      {/* Processing Steps */}
      {(processingSteps.current || processingSteps.completed.length > 0 || processingSteps.failed.length > 0) && (
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-3">Processing Steps</h4>
          
          <div className="space-y-3">
            {presets.find(p => p.id === selectedPreset)?.steps.map(step => {
              const status = getStepStatus(step);
              
              return (
                <div key={step} className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    status === 'processing' ? 'bg-yellow-500 animate-pulse' :
                    status === 'completed' ? 'bg-green-500' :
                    status === 'failed' ? 'bg-red-500' :
                    'bg-gray-500'
                  }`}></div>
                  <span className="text-sm capitalize">
                    {step === 'auphonic' ? 'Auphonic Audio Enhancement' :
                     step === 'imentiv' ? 'Imentiv Emotion AI' :
                     step}
                  </span>
                  <span className="text-xs text-gray-400 ml-auto">
                    {status === 'processing' ? 'Processing...' :
                     status === 'completed' ? 'Completed' :
                     status === 'failed' ? 'Failed' :
                     'Pending'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Processed Audio */}
      {processedAudioUrl && (
        <div className="mt-6">
          <h4 className="text-lg font-medium mb-3">Processed Audio</h4>
          <audio
            controls
            src={processedAudioUrl}
            className="w-full mb-3"
          ></audio>
          
          <div className="flex justify-end">
            <ThemeButton
              onClick={() => {
                const a = document.createElement('a');
                a.href = processedAudioUrl;
                a.download = `retune_${audioFile?.name || 'audio'}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }}
              variant="secondary"
              size="sm"
            >
              Download
            </ThemeButton>
          </div>
        </div>
      )}
    </ThemeCard>
  );
};

export default RetunePipeline;
