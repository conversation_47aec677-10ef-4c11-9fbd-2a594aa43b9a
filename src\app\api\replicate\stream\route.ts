import { NextResponse } from 'next/server';
import Replicate from 'replicate';

// This API route uses the Vercel AI SDK to stream the response from Replicate
export async function POST(req: Request) {
  try {
    // Get the request body
    const {
      prompt,
      alpha = 0.75,
      numInferenceSteps = 50,
      seed = Math.floor(Math.random() * 1000000)
    } = await req.json();

    // Validate the request
    if (!prompt) {
      return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
    }

    // Get the API key from environment variables
    const apiKey = process.env.REPLICATE_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Replicate API key not configured' },
        { status: 500 }
      );
    }

    // Initialize the Replicate client
    const replicate = new Replicate({
      auth: apiKey,
    });

    // Create the stream from Replicate
    const response = await replicate.predictions.create({
      version: "riffusion/riffusion:8cf61ea6c56afd61d8f5b9ffd14d7c216c0a93844ce2d82ac1c9ecc9c7f24e05",
      input: {
        prompt_a: prompt,
        alpha,
        num_inference_steps: numInferenceSteps,
        seed,
      },
      stream: true,
    });

    // Return the response directly
    return NextResponse.json({
      prediction: response,
      status: 'started'
    });
  } catch (error) {
    console.error('Error in Replicate streaming API:', error);
    return NextResponse.json(
      { error: 'Failed to generate music' },
      { status: 500 }
    );
  }
}
