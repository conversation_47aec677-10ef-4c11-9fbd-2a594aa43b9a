'use client';

import React, { useState } from 'react';

interface VoiceModelSelectorProps {
  type: 'cloning' | 'enhancement';
  onModelSelect?: (provider: string) => void;
}

const VoiceModelSelector: React.FC<VoiceModelSelectorProps> = ({ 
  type, 
  onModelSelect 
}) => {
  const [selectedProvider, setSelectedProvider] = useState<string>('elevenlabs');

  const handleProviderSelect = (provider: string) => {
    setSelectedProvider(provider);
    if (onModelSelect) {
      onModelSelect(provider);
    }
  };

  return (
    <div>
      <h3 className="text-sm font-medium mb-3 retune-text-glow-cyan">Select AI Provider</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* ElevenLabs */}
        <div 
          className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${
            selectedProvider === 'elevenlabs' 
              ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]' 
              : 'border-white/10 hover:border-[#00FFEE]/30'
          }`}
          onClick={() => handleProviderSelect('elevenlabs')}
        >
          <div className="flex items-center mb-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
              selectedProvider === 'elevenlabs' 
                ? 'bg-[#00FFEE]/20 text-[#00FFEE]' 
                : 'bg-black/40 text-white/60'
            }`}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium">ElevenLabs</p>
              <p className="text-xs text-white/60">Premium voice quality</p>
            </div>
          </div>
          <p className="text-xs text-white/70 ml-11">
            High-quality voice cloning with excellent tone preservation
          </p>
        </div>
        
        {/* Resemble */}
        <div 
          className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${
            selectedProvider === 'resemble' 
              ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]' 
              : 'border-white/10 hover:border-[#00FFEE]/30'
          }`}
          onClick={() => handleProviderSelect('resemble')}
        >
          <div className="flex items-center mb-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
              selectedProvider === 'resemble' 
                ? 'bg-[#00FFEE]/20 text-[#00FFEE]' 
                : 'bg-black/40 text-white/60'
            }`}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 001.414 1.414m0 0l-2.828 2.828m0 0a9 9 0 010-12.728m2.828 2.828a5 5 0 00-1.414 1.414m0 0L8.464 5.586m0 0a9 9 0 0112.728 0" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium">Resemble</p>
              <p className="text-xs text-white/60">Expressive voice cloning</p>
            </div>
          </div>
          <p className="text-xs text-white/70 ml-11">
            Excellent for expressive speech and singing with high emotional range
          </p>
        </div>
        
        {/* Voicemod */}
        <div 
          className={`retune-glass-container p-4 cursor-pointer transition-all duration-300 ${
            selectedProvider === 'voicemod' 
              ? 'border-[#00FFEE]/60 shadow-[0_0_15px_rgba(0,255,238,0.4)]' 
              : 'border-white/10 hover:border-[#00FFEE]/30'
          }`}
          onClick={() => handleProviderSelect('voicemod')}
        >
          <div className="flex items-center mb-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
              selectedProvider === 'voicemod' 
                ? 'bg-[#00FFEE]/20 text-[#00FFEE]' 
                : 'bg-black/40 text-white/60'
            }`}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
              </svg>
            </div>
            <div>
              <p className="text-sm font-medium">Voicemod</p>
              <p className="text-xs text-white/60">Voice transformation</p>
            </div>
          </div>
          <p className="text-xs text-white/70 ml-11">
            Fast and efficient voice conversion with good quality results
          </p>
        </div>
      </div>
      
      {/* Provider details */}
      <div className="mt-4 pt-4 border-t border-white/10">
        {selectedProvider === 'elevenlabs' && (
          <div className="text-xs text-white/70">
            <p className="mb-2">
              <span className="font-medium text-[#00FFEE]">ElevenLabs</span> provides state-of-the-art voice cloning technology with exceptional quality and natural-sounding results.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li>High-quality voice synthesis</li>
              <li>Excellent emotional range</li>
              <li>Natural-sounding results</li>
              <li>Fast processing time</li>
            </ul>
          </div>
        )}
        
        {selectedProvider === 'resemble' && (
          <div className="text-xs text-white/70">
            <p className="mb-2">
              <span className="font-medium text-[#00FFEE]">Resemble</span> specializes in expressive voice cloning with excellent emotional range and natural-sounding results.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li>Highly expressive voice synthesis</li>
              <li>Great for singing and emotional content</li>
              <li>Natural-sounding results</li>
              <li>Good for longer content</li>
            </ul>
          </div>
        )}
        
        {selectedProvider === 'voicemod' && (
          <div className="text-xs text-white/70">
            <p className="mb-2">
              <span className="font-medium text-[#00FFEE]">Voicemod</span> offers powerful voice transformation capabilities with a wide range of effects and modifications.
            </p>
            <ul className="list-disc list-inside space-y-1 pl-2">
              <li>Real-time voice transformation</li>
              <li>Wide range of voice effects</li>
              <li>Good for creative applications</li>
              <li>Fast processing time</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceModelSelector;
