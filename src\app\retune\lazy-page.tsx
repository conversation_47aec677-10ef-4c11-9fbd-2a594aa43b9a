'use client';

import LazyLoadingWrapper from '@/components/common/LazyLoadingWrapper';
import dynamic from 'next/dynamic';
import React, { Suspense } from 'react';

// Dynamically import the ActualRetunePage component
const ActualRetunePageComponent = dynamic(() => import('./actual-page'), {
  loading: () => <LazyLoadingWrapper pagePath="retune" pageName="Retune (Voice Studio)" />,
  ssr: false
});

const LazyRetunePage: React.FC = () => {
  return (
    <Suspense fallback={<LazyLoadingWrapper pagePath="retune" pageName="Retune (Voice Studio)" />}>
      <ActualRetunePageComponent />
    </Suspense>
  );
};

export default LazyRetunePage;
