import { NextApiRequest, NextApiResponse } from 'next';
import { kv } from '@vercel/kv';
import Groq from 'groq-sdk';

// Initialize API clients
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

// Genre to writer mapping
const GENRE_TO_WRITER: Record<string, string> = {
  hiphop: "Elias Fontaine",
  rnb: "Luna Rivers",
  pop: "<PERSON> Carter",
  country: "Max 'Sly' Dawson",
  jazz: "Zane Mercer",
  african: "Nova Sinclair",
  latin: "Rico Vega"
};

// Cache key generation
function createCacheKey(prompt: string, genres: string[], params: any): string {
  const genresStr = genres.sort().join(',');
  const paramStr = JSON.stringify(params, Object.keys(params).sort());
  return `lyrics:collaborative:${Buffer.from(genresStr).toString('base64')}:${Buffer.from(prompt).toString('base64')}:${Buffer.from(paramStr).toString('base64')}`;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      prompt = '',
      genres = ['pop', 'hiphop', 'rnb'],
      max_length = 200,
      temperature = 1.0,
      top_k = 50,
      top_p = 0.95,
      repetition_penalty = 1.2,
      model = 'claude', // Default to Claude
    } = req.body;

    // Validate inputs
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    if (!Array.isArray(genres) || genres.length === 0) {
      return res.status(400).json({ error: 'At least one genre is required' });
    }

    if (!['claude', 'groq', 'openai'].includes(model)) {
      return res.status(400).json({ error: 'Invalid model. Must be one of: claude, groq, openai' });
    }

    // Create cache key
    const params = {
      max_length,
      temperature,
      top_k,
      top_p,
      repetition_penalty,
      model,
    };
    const cacheKey = createCacheKey(prompt, genres, params);

    // Check cache
    const cachedResult = await kv.get(cacheKey);
    if (cachedResult) {
      console.log('Cache hit for collaborative generation');
      return res.status(200).json(cachedResult);
    }

    // Generate lyrics for each genre
    const collaborativeResults: Array<{
      genre: string;
      writer: string;
      text?: string;
      generationTime?: number;
      tokenCount?: number;
      model?: string;
      error?: string;
    }> = [];

    for (const genre of genres) {
      const startTime = Date.now();
      let text = '';
      let tokenCount = 0;

      try {
        // Use Groq for all generation
        const result = await generateWithGroq(genre, prompt, params);
        text = result.text || '';
        tokenCount = result.tokenCount;

        const generationTime = (Date.now() - startTime) / 1000;

        // Get writer name
        const writer = GENRE_TO_WRITER[genre] || "Unknown Writer";

        // Add to results
        collaborativeResults.push({
          genre,
          writer,
          text,
          generationTime,
          tokenCount,
          model,
        });
      } catch (error) {
        console.error(`Error generating lyrics for ${genre}:`, error);
        collaborativeResults.push({
          genre,
          writer: GENRE_TO_WRITER[genre] || "Unknown Writer",
          error: error.message || `Failed to generate lyrics for ${genre}`,
        });
      }

      // Add a small delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Create response
    const response = {
      prompt,
      genres,
      model,
      results: collaborativeResults,
      timestamp: new Date().toISOString(),
    };

    // Cache the result (expires in 1 hour)
    await kv.set(cacheKey, response, { ex: 3600 });

    return res.status(200).json(response);
  } catch (error) {
    console.error('Error in collaborative endpoint:', error);
    return res.status(500).json({ error: error.message || 'An error occurred during collaborative lyrics generation' });
  }
}



async function generateWithGroq(genre: string, prompt: string, params: any) {
  const systemPrompt = `You are ${GENRE_TO_WRITER[genre] || "an AI songwriter"}, a talented songwriter specializing in ${genre} music. 
Your task is to generate high-quality, original lyrics in the ${genre} style based on the user's prompt.
Follow these guidelines:
1. Create lyrics that match the conventions, themes, and language typical of ${genre} music
2. Maintain consistent tone, style, and voice throughout
3. Create a coherent structure with verses, chorus, and possibly bridge
4. Be creative and original while staying true to the genre
5. Respond ONLY with the generated lyrics, no explanations or commentary`;

  const response = await groq.chat.completions.create({
    model: 'llama3-70b-8192',
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: prompt }
    ],
    temperature: params.temperature,
    top_p: params.top_p,
    max_tokens: params.max_length,
  });

  return {
    text: response.choices[0]?.message?.content || '',
    tokenCount: response.usage?.completion_tokens || 0,
  };
}


