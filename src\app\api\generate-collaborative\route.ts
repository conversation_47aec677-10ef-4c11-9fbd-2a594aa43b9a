import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';
// Remove NextAuth imports as they're not available
// import { getServerSession } from 'next-auth';
// import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { rateLimit } from '@/lib/rate-limit';
// Remove missing db import
// import { saveGeneratedLyrics } from '@/lib/db/lyrics';

// Initialize Groq client
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

// Define the writer interface
interface AIWriter {
  id: string;
  name: string;
  genre: string;
  specialties: string[];
  avatar?: string;
  bio?: string;
  featured?: boolean;
  rating?: number;
}

// Define the writer assignment interface
interface WriterAssignment {
  writerId: string;
  part: 'verse' | 'chorus' | 'hook' | 'bridge' | 'all';
  writer: AIWriter;
}

export async function POST(req: NextRequest) {
  try {
    // Skip authentication for now since NextAuth is not configured
    // TODO: Implement proper authentication

    // Apply rate limiting based on IP
    const limiter = rateLimit({
      interval: 60 * 1000, // 1 minute
      uniqueTokenPerInterval: 10,
    });

    const clientIP = req.headers.get('x-forwarded-for') || 'anonymous';

    try {
      await limiter.check(10, clientIP);
    } catch (error) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    // Parse request body
    const { prompt, writerAssignments } = await req.json();

    // Validate input
    if (!prompt || !writerAssignments || !Array.isArray(writerAssignments) || writerAssignments.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request. Prompt and writer assignments are required.' },
        { status: 400 }
      );
    }

    // Create a system prompt based on the writer assignments
    const systemPrompt = createSystemPrompt(writerAssignments);

    // Generate lyrics using OpenAI
    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.8,
      max_tokens: 1000,
    });

    const generatedLyrics = completion.choices[0].message.content || '';

    // Extract metadata for response
    const genres = [...new Set(writerAssignments.map(assignment => assignment.writer.genre))];
    const writerNames = [...new Set(writerAssignments.map(assignment => assignment.writer.name))];

    // TODO: Save the generated lyrics to the database when auth is implemented
    console.log('Generated collaborative lyrics:', {
      prompt,
      lyrics: generatedLyrics,
      genres,
      writers: writerNames,
    });

    // Return the generated lyrics
    return NextResponse.json({
      lyrics: generatedLyrics,
      metadata: {
        genres,
        writers: writerNames,
      },
    });
  } catch (error) {
    console.error('Error generating collaborative lyrics:', error);
    return NextResponse.json(
      { error: 'Failed to generate lyrics. Please try again.' },
      { status: 500 }
    );
  }
}

// Helper function to create a system prompt based on writer assignments
function createSystemPrompt(writerAssignments: WriterAssignment[]): string {
  // Check if there's a single writer assigned to the entire song
  const fullSongWriter = writerAssignments.find(assignment => assignment.part === 'all');

  if (fullSongWriter) {
    return `You are ${fullSongWriter.writer.name}, a ${fullSongWriter.writer.genre} specialist with expertise in ${fullSongWriter.writer.specialties.join(', ')}.

${fullSongWriter.writer.bio || ''}

Write song lyrics in your unique style based on the user's prompt. Format the lyrics with clear sections (Verse, Chorus, etc.).`;
  }

  // For collaborative writing with multiple writers for different parts
  const verseWriters = writerAssignments.filter(assignment => assignment.part === 'verse');
  const chorusWriters = writerAssignments.filter(assignment => assignment.part === 'chorus');
  const hookWriters = writerAssignments.filter(assignment => assignment.part === 'hook');
  const bridgeWriters = writerAssignments.filter(assignment => assignment.part === 'bridge');

  let prompt = `You are a collaborative songwriting AI that can mimic the styles of different writers for different parts of a song. Write song lyrics based on the user's prompt with the following writers collaborating:

`;

  if (verseWriters.length > 0) {
    prompt += `VERSES written by ${verseWriters.map(w => w.writer.name).join(' and ')}, who specialize in ${verseWriters.map(w => w.writer.genre).join(' and ')} with expertise in ${verseWriters.flatMap(w => w.writer.specialties).join(', ')}.

`;
  }

  if (chorusWriters.length > 0) {
    prompt += `CHORUS written by ${chorusWriters.map(w => w.writer.name).join(' and ')}, who specialize in ${chorusWriters.map(w => w.writer.genre).join(' and ')} with expertise in ${chorusWriters.flatMap(w => w.writer.specialties).join(', ')}.

`;
  }

  if (hookWriters.length > 0) {
    prompt += `HOOK written by ${hookWriters.map(w => w.writer.name).join(' and ')}, who specialize in ${hookWriters.map(w => w.writer.genre).join(' and ')} with expertise in ${hookWriters.flatMap(w => w.writer.specialties).join(', ')}.

`;
  }

  if (bridgeWriters.length > 0) {
    prompt += `BRIDGE written by ${bridgeWriters.map(w => w.writer.name).join(' and ')}, who specialize in ${bridgeWriters.map(w => w.writer.genre).join(' and ')} with expertise in ${bridgeWriters.flatMap(w => w.writer.specialties).join(', ')}.

`;
  }

  prompt += `Format the lyrics with clear sections (Verse, Chorus, Hook, Bridge, etc.). Make sure each section reflects the style and expertise of the assigned writer(s).`;

  return prompt;
}
