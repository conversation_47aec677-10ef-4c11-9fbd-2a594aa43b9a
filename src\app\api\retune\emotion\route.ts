import { NextResponse } from 'next/server';

/**
 * Emotion types detected by Imentiv AI
 */
enum EmotionType {
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  FEARFUL = 'fearful',
  DISGUSTED = 'disgusted',
  SURPRISED = 'surprised',
  NEUTRAL = 'neutral',
  CONFIDENT = 'confident',
  PASSIONATE = 'passionate',
  ENERGETIC = 'energetic',
  CALM = 'calm',
  NOSTALGIC = 'nostalgic',
}

/**
 * Secure API route for emotion detection and adjustment using Imentiv AI
 *
 * POST /api/retune/emotion/detect
 * - Accepts audio file and detection options
 * - Returns detected emotions and tones
 *
 * POST /api/retune/emotion/adjust
 * - Accepts audio file, target emotion, and intensity
 * - Returns processed audio with adjusted emotions
 *
 * POST /api/retune/emotion/analyze
 * - Accepts audio file
 * - Returns voice quality analysis
 *
 * SECURITY: All API calls to Imentiv AI are made from the backend with secure environment variables
 */

// Secure API configuration - API key stored in environment variables only
const IMENTIV_API_KEY = process.env.IMENTIV_API_KEY;
const IMENTIV_API_BASE = 'https://api.imentiv.ai/v1';

/**
 * Securely detect emotions in audio file using Imentiv AI API
 */
async function detectEmotionsSecure(
  audioFile: File,
  options: {
    includeConfidenceScores?: boolean;
    includeTimestamps?: boolean;
    language?: string;
    enhanceSensitivity?: boolean;
  } = {}
) {
  if (!IMENTIV_API_KEY) {
    throw new Error('Imentiv AI API key not configured in environment variables');
  }

  try {
    // Create form data
    const formData = new FormData();
    formData.append('audio', audioFile);

    // Add options
    if (options.includeConfidenceScores !== undefined) {
      formData.append('include_confidence_scores', options.includeConfidenceScores.toString());
    }

    if (options.includeTimestamps !== undefined) {
      formData.append('include_timestamps', options.includeTimestamps.toString());
    }

    if (options.language) {
      formData.append('language', options.language);
    }

    if (options.enhanceSensitivity !== undefined) {
      formData.append('enhance_sensitivity', options.enhanceSensitivity.toString());
    }

    // Make secure API request from backend
    const response = await fetch(`${IMENTIV_API_BASE}/detect-emotion`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${IMENTIV_API_KEY}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Imentiv AI API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    return {
      success: true,
      emotions: {
        dominant: data.dominant_emotion,
        scores: data.emotion_scores || {},
        timeline: data.emotion_timeline || []
      },
      tones: {
        dominant: data.dominant_tone,
        scores: data.tone_scores || {}
      }
    };
  } catch (error) {
    console.error('Error detecting emotions with Imentiv AI API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Securely adjust emotions in audio file using Imentiv AI API
 */
async function adjustEmotionsSecure(
  audioFile: File,
  targetEmotion: EmotionType,
  intensity: number = 50
): Promise<Blob | null> {
  if (!IMENTIV_API_KEY) {
    throw new Error('Imentiv AI API key not configured in environment variables');
  }

  try {
    // Create form data
    const formData = new FormData();
    formData.append('audio', audioFile);
    formData.append('target_emotion', targetEmotion);
    formData.append('intensity', intensity.toString());

    // Make secure API request from backend
    const response = await fetch(`${IMENTIV_API_BASE}/adjust-emotion`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${IMENTIV_API_KEY}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Imentiv AI API error: ${errorData.error || response.statusText}`);
    }

    // Return the processed audio as a blob
    return await response.blob();
  } catch (error) {
    console.error('Error adjusting emotions with Imentiv AI API:', error);
    return null;
  }
}

/**
 * Securely analyze voice quality using Imentiv AI API
 */
async function analyzeVoiceQualitySecure(audioFile: File) {
  if (!IMENTIV_API_KEY) {
    throw new Error('Imentiv AI API key not configured in environment variables');
  }

  try {
    // Create form data
    const formData = new FormData();
    formData.append('audio', audioFile);

    // Make secure API request from backend
    const response = await fetch(`${IMENTIV_API_BASE}/analyze-voice-quality`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${IMENTIV_API_KEY}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Imentiv AI API error: ${errorData.error || response.statusText}`);
    }

    const data = await response.json();

    return {
      success: true,
      clarity: data.clarity,
      expressiveness: data.expressiveness,
      consistency: data.consistency,
      naturalness: data.naturalness,
      overall: data.overall_score
    };
  } catch (error) {
    console.error('Error analyzing voice quality with Imentiv AI API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export async function POST(request: Request) {
  try {
    // Get the path from the URL
    const { pathname } = new URL(request.url);
    const endpoint = pathname.split('/').pop();

    // Parse the multipart form data
    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;

    if (!audioFile) {
      return NextResponse.json({ error: 'Audio file is required' }, { status: 400 });
    }

    // Handle different endpoints using secure backend functions
    switch (endpoint) {
      case 'detect': {
        // Parse options
        const includeConfidenceScores = formData.get('includeConfidenceScores') === 'true';
        const includeTimestamps = formData.get('includeTimestamps') === 'true';
        const language = formData.get('language') as string || undefined;
        const enhanceSensitivity = formData.get('enhanceSensitivity') === 'true';

        // Detect emotions using secure backend function
        const result = await detectEmotionsSecure(audioFile, {
          includeConfidenceScores,
          includeTimestamps,
          language,
          enhanceSensitivity
        });

        if (!result.success) {
          return NextResponse.json({ error: result.error }, { status: 500 });
        }

        return NextResponse.json(result);
      }

      case 'adjust': {
        // Parse options
        const targetEmotion = formData.get('targetEmotion') as EmotionType;
        const intensity = parseInt(formData.get('intensity') as string || '50');

        if (!targetEmotion) {
          return NextResponse.json({ error: 'Target emotion is required' }, { status: 400 });
        }

        // Adjust emotions using secure backend function
        const result = await adjustEmotionsSecure(audioFile, targetEmotion, intensity);

        if (!result) {
          return NextResponse.json({ error: 'Failed to adjust emotions' }, { status: 500 });
        }

        // Return the processed audio
        return new NextResponse(result, {
          headers: {
            'Content-Type': 'audio/wav',
            'Content-Disposition': 'attachment; filename="emotion_adjusted.wav"'
          }
        });
      }

      case 'analyze': {
        // Analyze voice quality using secure backend function
        const result = await analyzeVoiceQualitySecure(audioFile);

        if (!result.success) {
          return NextResponse.json({ error: result.error }, { status: 500 });
        }

        return NextResponse.json(result);
      }

      default:
        return NextResponse.json({ error: 'Invalid endpoint' }, { status: 404 });
    }
  } catch (error) {
    console.error('Error processing emotion detection request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
