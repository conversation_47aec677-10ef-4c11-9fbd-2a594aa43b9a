import OpenAI from 'openai';
import { saveLyrics } from '@/utils/dbStorage';
import { uploadToBlob } from '@/utils/blobStorage';
import { addToKVList, trimKVList } from '@/utils/kvStorage';

// IMPORTANT: Set the runtime to edge
export const runtime = 'edge';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req: Request) {
  try {
    const { prompt, genre, mood, theme, structure, userId, model, temperature, maxTokens } = await req.json();

    // Validate required fields
    if (!prompt) {
      return new Response(JSON.stringify({ error: 'Prompt is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Cache the prompt in the user's recent prompts list
    if (userId) {
      await addToKVList(`user:${userId}:prompts`, prompt);
      await trimKVList(`user:${userId}:prompts`, 0, 9); // Keep only the 10 most recent prompts
    }

    // Build a comprehensive prompt that includes all parameters
    let enhancedPrompt = prompt;
    if (genre || mood || theme || structure) {
      enhancedPrompt += '\n\nAdditional parameters:';
      if (genre) enhancedPrompt += `\nGenre: ${genre}`;
      if (mood) enhancedPrompt += `\nMood: ${mood}`;
      if (theme) enhancedPrompt += `\nTheme: ${theme}`;
      if (structure) enhancedPrompt += `\nStructure: ${structure}`;
    }

    // Create system prompt for the lyricist
    const systemPrompt = 'You are a professional songwriter with expertise in creating original, creative, and engaging lyrics.';

    // Call OpenAI API with streaming enabled
    const response = await openai.chat.completions.create({
      model: model || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: enhancedPrompt,
        },
      ],
      temperature: temperature || 0.8,
      max_tokens: maxTokens || 1000,
      stream: true,
    });

    // Collect the response content
    let completion = '';
    for await (const chunk of response) {
      const content = chunk.choices[0]?.delta?.content || '';
      completion += content;
    }

    // Save lyrics to Blob storage if they're long
    let blobUrl = null;
    if (completion && completion.length > 1000) {
      try {
        const blob = await uploadToBlob(
          `lyrics/${userId || 'anonymous'}/${Date.now()}.txt`,
          new TextEncoder().encode(completion),
          { access: 'public' }
        );
        blobUrl = blob.url;
      } catch (error) {
        console.error('Error uploading to Blob:', error);
      }
    }

    // Save lyrics to database if user is authenticated
    if (userId) {
      try {
        await saveLyrics(userId, completion, prompt, blobUrl);
      } catch (error) {
        console.error('Error saving lyrics to database:', error);
      }
    }

    // Return the response
    return new Response(JSON.stringify({
      content: completion,
      blobUrl
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error generating lyrics:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred',
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
