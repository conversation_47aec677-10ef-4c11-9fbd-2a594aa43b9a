'use client';

import React from 'react';
import VoiceCloneGuidelines from './VoiceCloneGuidelines';
import VoiceCloneProvider from './VoiceCloneProvider';
import VoiceModelSelector from './VoiceModelSelector';

// Define voice model type
interface VoiceModel {
  id: string;
  name: string;
  type: 'vocalsync' | 'flowclone';
  qualityScore: number;
  createdAt: string;
}

interface VoiceCloneContentProps {
  onVoiceModelCreated: (model: VoiceModel, score: number) => void;
}

const VoiceCloneContent: React.FC<VoiceCloneContentProps> = ({ onVoiceModelCreated }) => {
  return (
    <div className="space-y-8">
      <div className="retune-glass-container p-6">
        <h2 className="retune-title text-2xl mb-4">Voice Cloning</h2>
        <p className="text-white/80 mb-6 retune-subtitle">
          Create a high-quality voice model by recording sample phrases or uploading your own audio.
          Choose from multiple AI providers for the best results.
        </p>

        {/* Voice Model Selector */}
        <div className="mb-6">
          <VoiceModelSelector type="cloning" />
        </div>

        {/* Decorative 3D microphone */}
        <div className="relative">
          <div className="absolute -top-12 -right-8 opacity-20 retune-3d-mic hidden md:block">
            <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="url(#mic-gradient-clone)" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
              <defs>
                <linearGradient id="mic-gradient-clone" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#00FFEE" />
                  <stop offset="100%" stopColor="#C600FF" />
                </linearGradient>
              </defs>
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
          </div>
        </div>

        <VoiceCloneProvider onVoiceModelCreated={onVoiceModelCreated} />
      </div>

      {/* Voice Cloning Guidelines */}
      <VoiceCloneGuidelines />
    </div>
  );
};

export default VoiceCloneContent;
